var Wh=Object.defineProperty;var qh=(e,t,r)=>t in e?Wh(e,t,{enumerable:!0,configurable:!0,writable:!0,value:r}):e[t]=r;var zn=(e,t,r)=>(qh(e,typeof t!="symbol"?t+"":t,r),r);function Kh(e,t){for(var r=0;r<t.length;r++){const s=t[r];if(typeof s!="string"&&!Array.isArray(s)){for(const n in s)if(n!=="default"&&!(n in e)){const i=Object.getOwnPropertyDescriptor(s,n);i&&Object.defineProperty(e,n,i.get?i:{enumerable:!0,get:()=>s[n]})}}}return Object.freeze(Object.defineProperty(e,Symbol.toStringTag,{value:"Module"}))}(function(){const t=document.createElement("link").relList;if(t&&t.supports&&t.supports("modulepreload"))return;for(const n of document.querySelectorAll('link[rel="modulepreload"]'))s(n);new MutationObserver(n=>{for(const i of n)if(i.type==="childList")for(const o of i.addedNodes)o.tagName==="LINK"&&o.rel==="modulepreload"&&s(o)}).observe(document,{childList:!0,subtree:!0});function r(n){const i={};return n.integrity&&(i.integrity=n.integrity),n.referrerPolicy&&(i.referrerPolicy=n.referrerPolicy),n.crossOrigin==="use-credentials"?i.credentials="include":n.crossOrigin==="anonymous"?i.credentials="omit":i.credentials="same-origin",i}function s(n){if(n.ep)return;n.ep=!0;const i=r(n);fetch(n.href,i)}})();function Qh(e){return e&&e.__esModule&&Object.prototype.hasOwnProperty.call(e,"default")?e.default:e}var Rd={exports:{}},ai={},_d={exports:{}},X={};/**
 * @license React
 * react.production.min.js
 *
 * Copyright (c) Facebook, Inc. and its affiliates.
 *
 * This source code is licensed under the MIT license found in the
 * LICENSE file in the root directory of this source tree.
 */var On=Symbol.for("react.element"),Yh=Symbol.for("react.portal"),Gh=Symbol.for("react.fragment"),Jh=Symbol.for("react.strict_mode"),Xh=Symbol.for("react.profiler"),Zh=Symbol.for("react.provider"),ep=Symbol.for("react.context"),tp=Symbol.for("react.forward_ref"),rp=Symbol.for("react.suspense"),sp=Symbol.for("react.memo"),np=Symbol.for("react.lazy"),Ic=Symbol.iterator;function ap(e){return e===null||typeof e!="object"?null:(e=Ic&&e[Ic]||e["@@iterator"],typeof e=="function"?e:null)}var Fd={isMounted:function(){return!1},enqueueForceUpdate:function(){},enqueueReplaceState:function(){},enqueueSetState:function(){}},Id=Object.assign,Md={};function bs(e,t,r){this.props=e,this.context=t,this.refs=Md,this.updater=r||Fd}bs.prototype.isReactComponent={};bs.prototype.setState=function(e,t){if(typeof e!="object"&&typeof e!="function"&&e!=null)throw Error("setState(...): takes an object of state variables to update or a function which returns an object of state variables.");this.updater.enqueueSetState(this,e,t,"setState")};bs.prototype.forceUpdate=function(e){this.updater.enqueueForceUpdate(this,e,"forceUpdate")};function Ud(){}Ud.prototype=bs.prototype;function Cl(e,t,r){this.props=e,this.context=t,this.refs=Md,this.updater=r||Fd}var $l=Cl.prototype=new Ud;$l.constructor=Cl;Id($l,bs.prototype);$l.isPureReactComponent=!0;var Mc=Array.isArray,zd=Object.prototype.hasOwnProperty,Pl={current:null},Vd={key:!0,ref:!0,__self:!0,__source:!0};function Bd(e,t,r){var s,n={},i=null,o=null;if(t!=null)for(s in t.ref!==void 0&&(o=t.ref),t.key!==void 0&&(i=""+t.key),t)zd.call(t,s)&&!Vd.hasOwnProperty(s)&&(n[s]=t[s]);var l=arguments.length-2;if(l===1)n.children=r;else if(1<l){for(var c=Array(l),u=0;u<l;u++)c[u]=arguments[u+2];n.children=c}if(e&&e.defaultProps)for(s in l=e.defaultProps,l)n[s]===void 0&&(n[s]=l[s]);return{$$typeof:On,type:e,key:i,ref:o,props:n,_owner:Pl.current}}function ip(e,t){return{$$typeof:On,type:e.type,key:t,ref:e.ref,props:e.props,_owner:e._owner}}function El(e){return typeof e=="object"&&e!==null&&e.$$typeof===On}function op(e){var t={"=":"=0",":":"=2"};return"$"+e.replace(/[=:]/g,function(r){return t[r]})}var Uc=/\/+/g;function Mi(e,t){return typeof e=="object"&&e!==null&&e.key!=null?op(""+e.key):t.toString(36)}function la(e,t,r,s,n){var i=typeof e;(i==="undefined"||i==="boolean")&&(e=null);var o=!1;if(e===null)o=!0;else switch(i){case"string":case"number":o=!0;break;case"object":switch(e.$$typeof){case On:case Yh:o=!0}}if(o)return o=e,n=n(o),e=s===""?"."+Mi(o,0):s,Mc(n)?(r="",e!=null&&(r=e.replace(Uc,"$&/")+"/"),la(n,t,r,"",function(u){return u})):n!=null&&(El(n)&&(n=ip(n,r+(!n.key||o&&o.key===n.key?"":(""+n.key).replace(Uc,"$&/")+"/")+e)),t.push(n)),1;if(o=0,s=s===""?".":s+":",Mc(e))for(var l=0;l<e.length;l++){i=e[l];var c=s+Mi(i,l);o+=la(i,t,r,c,n)}else if(c=ap(e),typeof c=="function")for(e=c.call(e),l=0;!(i=e.next()).done;)i=i.value,c=s+Mi(i,l++),o+=la(i,t,r,c,n);else if(i==="object")throw t=String(e),Error("Objects are not valid as a React child (found: "+(t==="[object Object]"?"object with keys {"+Object.keys(e).join(", ")+"}":t)+"). If you meant to render a collection of children, use an array instead.");return o}function Vn(e,t,r){if(e==null)return e;var s=[],n=0;return la(e,s,"","",function(i){return t.call(r,i,n++)}),s}function lp(e){if(e._status===-1){var t=e._result;t=t(),t.then(function(r){(e._status===0||e._status===-1)&&(e._status=1,e._result=r)},function(r){(e._status===0||e._status===-1)&&(e._status=2,e._result=r)}),e._status===-1&&(e._status=0,e._result=t)}if(e._status===1)return e._result.default;throw e._result}var Be={current:null},ca={transition:null},cp={ReactCurrentDispatcher:Be,ReactCurrentBatchConfig:ca,ReactCurrentOwner:Pl};function Hd(){throw Error("act(...) is not supported in production builds of React.")}X.Children={map:Vn,forEach:function(e,t,r){Vn(e,function(){t.apply(this,arguments)},r)},count:function(e){var t=0;return Vn(e,function(){t++}),t},toArray:function(e){return Vn(e,function(t){return t})||[]},only:function(e){if(!El(e))throw Error("React.Children.only expected to receive a single React element child.");return e}};X.Component=bs;X.Fragment=Gh;X.Profiler=Xh;X.PureComponent=Cl;X.StrictMode=Jh;X.Suspense=rp;X.__SECRET_INTERNALS_DO_NOT_USE_OR_YOU_WILL_BE_FIRED=cp;X.act=Hd;X.cloneElement=function(e,t,r){if(e==null)throw Error("React.cloneElement(...): The argument must be a React element, but you passed "+e+".");var s=Id({},e.props),n=e.key,i=e.ref,o=e._owner;if(t!=null){if(t.ref!==void 0&&(i=t.ref,o=Pl.current),t.key!==void 0&&(n=""+t.key),e.type&&e.type.defaultProps)var l=e.type.defaultProps;for(c in t)zd.call(t,c)&&!Vd.hasOwnProperty(c)&&(s[c]=t[c]===void 0&&l!==void 0?l[c]:t[c])}var c=arguments.length-2;if(c===1)s.children=r;else if(1<c){l=Array(c);for(var u=0;u<c;u++)l[u]=arguments[u+2];s.children=l}return{$$typeof:On,type:e.type,key:n,ref:i,props:s,_owner:o}};X.createContext=function(e){return e={$$typeof:ep,_currentValue:e,_currentValue2:e,_threadCount:0,Provider:null,Consumer:null,_defaultValue:null,_globalName:null},e.Provider={$$typeof:Zh,_context:e},e.Consumer=e};X.createElement=Bd;X.createFactory=function(e){var t=Bd.bind(null,e);return t.type=e,t};X.createRef=function(){return{current:null}};X.forwardRef=function(e){return{$$typeof:tp,render:e}};X.isValidElement=El;X.lazy=function(e){return{$$typeof:np,_payload:{_status:-1,_result:e},_init:lp}};X.memo=function(e,t){return{$$typeof:sp,type:e,compare:t===void 0?null:t}};X.startTransition=function(e){var t=ca.transition;ca.transition={};try{e()}finally{ca.transition=t}};X.unstable_act=Hd;X.useCallback=function(e,t){return Be.current.useCallback(e,t)};X.useContext=function(e){return Be.current.useContext(e)};X.useDebugValue=function(){};X.useDeferredValue=function(e){return Be.current.useDeferredValue(e)};X.useEffect=function(e,t){return Be.current.useEffect(e,t)};X.useId=function(){return Be.current.useId()};X.useImperativeHandle=function(e,t,r){return Be.current.useImperativeHandle(e,t,r)};X.useInsertionEffect=function(e,t){return Be.current.useInsertionEffect(e,t)};X.useLayoutEffect=function(e,t){return Be.current.useLayoutEffect(e,t)};X.useMemo=function(e,t){return Be.current.useMemo(e,t)};X.useReducer=function(e,t,r){return Be.current.useReducer(e,t,r)};X.useRef=function(e){return Be.current.useRef(e)};X.useState=function(e){return Be.current.useState(e)};X.useSyncExternalStore=function(e,t,r){return Be.current.useSyncExternalStore(e,t,r)};X.useTransition=function(){return Be.current.useTransition()};X.version="18.3.1";_d.exports=X;var j=_d.exports;const Re=Qh(j),up=Kh({__proto__:null,default:Re},[j]);/**
 * @license React
 * react-jsx-runtime.production.min.js
 *
 * Copyright (c) Facebook, Inc. and its affiliates.
 *
 * This source code is licensed under the MIT license found in the
 * LICENSE file in the root directory of this source tree.
 */var dp=j,fp=Symbol.for("react.element"),mp=Symbol.for("react.fragment"),hp=Object.prototype.hasOwnProperty,pp=dp.__SECRET_INTERNALS_DO_NOT_USE_OR_YOU_WILL_BE_FIRED.ReactCurrentOwner,gp={key:!0,ref:!0,__self:!0,__source:!0};function Wd(e,t,r){var s,n={},i=null,o=null;r!==void 0&&(i=""+r),t.key!==void 0&&(i=""+t.key),t.ref!==void 0&&(o=t.ref);for(s in t)hp.call(t,s)&&!gp.hasOwnProperty(s)&&(n[s]=t[s]);if(e&&e.defaultProps)for(s in t=e.defaultProps,t)n[s]===void 0&&(n[s]=t[s]);return{$$typeof:fp,type:e,key:i,ref:o,props:n,_owner:pp.current}}ai.Fragment=mp;ai.jsx=Wd;ai.jsxs=Wd;Rd.exports=ai;var a=Rd.exports,vo={},qd={exports:{}},st={},Kd={exports:{}},Qd={};/**
 * @license React
 * scheduler.production.min.js
 *
 * Copyright (c) Facebook, Inc. and its affiliates.
 *
 * This source code is licensed under the MIT license found in the
 * LICENSE file in the root directory of this source tree.
 */(function(e){function t(T,V){var q=T.length;T.push(V);e:for(;0<q;){var J=q-1>>>1,ne=T[J];if(0<n(ne,V))T[J]=V,T[q]=ne,q=J;else break e}}function r(T){return T.length===0?null:T[0]}function s(T){if(T.length===0)return null;var V=T[0],q=T.pop();if(q!==V){T[0]=q;e:for(var J=0,ne=T.length,Xe=ne>>>1;J<Xe;){var it=2*(J+1)-1,Lt=T[it],Tt=it+1,Gt=T[Tt];if(0>n(Lt,q))Tt<ne&&0>n(Gt,Lt)?(T[J]=Gt,T[Tt]=q,J=Tt):(T[J]=Lt,T[it]=q,J=it);else if(Tt<ne&&0>n(Gt,q))T[J]=Gt,T[Tt]=q,J=Tt;else break e}}return V}function n(T,V){var q=T.sortIndex-V.sortIndex;return q!==0?q:T.id-V.id}if(typeof performance=="object"&&typeof performance.now=="function"){var i=performance;e.unstable_now=function(){return i.now()}}else{var o=Date,l=o.now();e.unstable_now=function(){return o.now()-l}}var c=[],u=[],d=1,f=null,m=3,v=!1,g=!1,w=!1,y=typeof setTimeout=="function"?setTimeout:null,p=typeof clearTimeout=="function"?clearTimeout:null,h=typeof setImmediate<"u"?setImmediate:null;typeof navigator<"u"&&navigator.scheduling!==void 0&&navigator.scheduling.isInputPending!==void 0&&navigator.scheduling.isInputPending.bind(navigator.scheduling);function x(T){for(var V=r(u);V!==null;){if(V.callback===null)s(u);else if(V.startTime<=T)s(u),V.sortIndex=V.expirationTime,t(c,V);else break;V=r(u)}}function b(T){if(w=!1,x(T),!g)if(r(c)!==null)g=!0,te(k);else{var V=r(u);V!==null&&ue(b,V.startTime-T)}}function k(T,V){g=!1,w&&(w=!1,p($),$=-1),v=!0;var q=m;try{for(x(V),f=r(c);f!==null&&(!(f.expirationTime>V)||T&&!U());){var J=f.callback;if(typeof J=="function"){f.callback=null,m=f.priorityLevel;var ne=J(f.expirationTime<=V);V=e.unstable_now(),typeof ne=="function"?f.callback=ne:f===r(c)&&s(c),x(V)}else s(c);f=r(c)}if(f!==null)var Xe=!0;else{var it=r(u);it!==null&&ue(b,it.startTime-V),Xe=!1}return Xe}finally{f=null,m=q,v=!1}}var E=!1,O=null,$=-1,C=5,D=-1;function U(){return!(e.unstable_now()-D<C)}function W(){if(O!==null){var T=e.unstable_now();D=T;var V=!0;try{V=O(!0,T)}finally{V?I():(E=!1,O=null)}}else E=!1}var I;if(typeof h=="function")I=function(){h(W)};else if(typeof MessageChannel<"u"){var Y=new MessageChannel,Ne=Y.port2;Y.port1.onmessage=W,I=function(){Ne.postMessage(null)}}else I=function(){y(W,0)};function te(T){O=T,E||(E=!0,I())}function ue(T,V){$=y(function(){T(e.unstable_now())},V)}e.unstable_IdlePriority=5,e.unstable_ImmediatePriority=1,e.unstable_LowPriority=4,e.unstable_NormalPriority=3,e.unstable_Profiling=null,e.unstable_UserBlockingPriority=2,e.unstable_cancelCallback=function(T){T.callback=null},e.unstable_continueExecution=function(){g||v||(g=!0,te(k))},e.unstable_forceFrameRate=function(T){0>T||125<T?console.error("forceFrameRate takes a positive int between 0 and 125, forcing frame rates higher than 125 fps is not supported"):C=0<T?Math.floor(1e3/T):5},e.unstable_getCurrentPriorityLevel=function(){return m},e.unstable_getFirstCallbackNode=function(){return r(c)},e.unstable_next=function(T){switch(m){case 1:case 2:case 3:var V=3;break;default:V=m}var q=m;m=V;try{return T()}finally{m=q}},e.unstable_pauseExecution=function(){},e.unstable_requestPaint=function(){},e.unstable_runWithPriority=function(T,V){switch(T){case 1:case 2:case 3:case 4:case 5:break;default:T=3}var q=m;m=T;try{return V()}finally{m=q}},e.unstable_scheduleCallback=function(T,V,q){var J=e.unstable_now();switch(typeof q=="object"&&q!==null?(q=q.delay,q=typeof q=="number"&&0<q?J+q:J):q=J,T){case 1:var ne=-1;break;case 2:ne=250;break;case 5:ne=**********;break;case 4:ne=1e4;break;default:ne=5e3}return ne=q+ne,T={id:d++,callback:V,priorityLevel:T,startTime:q,expirationTime:ne,sortIndex:-1},q>J?(T.sortIndex=q,t(u,T),r(c)===null&&T===r(u)&&(w?(p($),$=-1):w=!0,ue(b,q-J))):(T.sortIndex=ne,t(c,T),g||v||(g=!0,te(k))),T},e.unstable_shouldYield=U,e.unstable_wrapCallback=function(T){var V=m;return function(){var q=m;m=V;try{return T.apply(this,arguments)}finally{m=q}}}})(Qd);Kd.exports=Qd;var xp=Kd.exports;/**
 * @license React
 * react-dom.production.min.js
 *
 * Copyright (c) Facebook, Inc. and its affiliates.
 *
 * This source code is licensed under the MIT license found in the
 * LICENSE file in the root directory of this source tree.
 */var yp=j,rt=xp;function A(e){for(var t="https://reactjs.org/docs/error-decoder.html?invariant="+e,r=1;r<arguments.length;r++)t+="&args[]="+encodeURIComponent(arguments[r]);return"Minified React error #"+e+"; visit "+t+" for the full message or use the non-minified dev environment for full errors and additional helpful warnings."}var Yd=new Set,on={};function zr(e,t){hs(e,t),hs(e+"Capture",t)}function hs(e,t){for(on[e]=t,e=0;e<t.length;e++)Yd.add(t[e])}var Vt=!(typeof window>"u"||typeof window.document>"u"||typeof window.document.createElement>"u"),wo=Object.prototype.hasOwnProperty,vp=/^[:A-Z_a-z\u00C0-\u00D6\u00D8-\u00F6\u00F8-\u02FF\u0370-\u037D\u037F-\u1FFF\u200C-\u200D\u2070-\u218F\u2C00-\u2FEF\u3001-\uD7FF\uF900-\uFDCF\uFDF0-\uFFFD][:A-Z_a-z\u00C0-\u00D6\u00D8-\u00F6\u00F8-\u02FF\u0370-\u037D\u037F-\u1FFF\u200C-\u200D\u2070-\u218F\u2C00-\u2FEF\u3001-\uD7FF\uF900-\uFDCF\uFDF0-\uFFFD\-.0-9\u00B7\u0300-\u036F\u203F-\u2040]*$/,zc={},Vc={};function wp(e){return wo.call(Vc,e)?!0:wo.call(zc,e)?!1:vp.test(e)?Vc[e]=!0:(zc[e]=!0,!1)}function Np(e,t,r,s){if(r!==null&&r.type===0)return!1;switch(typeof t){case"function":case"symbol":return!0;case"boolean":return s?!1:r!==null?!r.acceptsBooleans:(e=e.toLowerCase().slice(0,5),e!=="data-"&&e!=="aria-");default:return!1}}function jp(e,t,r,s){if(t===null||typeof t>"u"||Np(e,t,r,s))return!0;if(s)return!1;if(r!==null)switch(r.type){case 3:return!t;case 4:return t===!1;case 5:return isNaN(t);case 6:return isNaN(t)||1>t}return!1}function He(e,t,r,s,n,i,o){this.acceptsBooleans=t===2||t===3||t===4,this.attributeName=s,this.attributeNamespace=n,this.mustUseProperty=r,this.propertyName=e,this.type=t,this.sanitizeURL=i,this.removeEmptyString=o}var De={};"children dangerouslySetInnerHTML defaultValue defaultChecked innerHTML suppressContentEditableWarning suppressHydrationWarning style".split(" ").forEach(function(e){De[e]=new He(e,0,!1,e,null,!1,!1)});[["acceptCharset","accept-charset"],["className","class"],["htmlFor","for"],["httpEquiv","http-equiv"]].forEach(function(e){var t=e[0];De[t]=new He(t,1,!1,e[1],null,!1,!1)});["contentEditable","draggable","spellCheck","value"].forEach(function(e){De[e]=new He(e,2,!1,e.toLowerCase(),null,!1,!1)});["autoReverse","externalResourcesRequired","focusable","preserveAlpha"].forEach(function(e){De[e]=new He(e,2,!1,e,null,!1,!1)});"allowFullScreen async autoFocus autoPlay controls default defer disabled disablePictureInPicture disableRemotePlayback formNoValidate hidden loop noModule noValidate open playsInline readOnly required reversed scoped seamless itemScope".split(" ").forEach(function(e){De[e]=new He(e,3,!1,e.toLowerCase(),null,!1,!1)});["checked","multiple","muted","selected"].forEach(function(e){De[e]=new He(e,3,!0,e,null,!1,!1)});["capture","download"].forEach(function(e){De[e]=new He(e,4,!1,e,null,!1,!1)});["cols","rows","size","span"].forEach(function(e){De[e]=new He(e,6,!1,e,null,!1,!1)});["rowSpan","start"].forEach(function(e){De[e]=new He(e,5,!1,e.toLowerCase(),null,!1,!1)});var Ol=/[\-:]([a-z])/g;function Al(e){return e[1].toUpperCase()}"accent-height alignment-baseline arabic-form baseline-shift cap-height clip-path clip-rule color-interpolation color-interpolation-filters color-profile color-rendering dominant-baseline enable-background fill-opacity fill-rule flood-color flood-opacity font-family font-size font-size-adjust font-stretch font-style font-variant font-weight glyph-name glyph-orientation-horizontal glyph-orientation-vertical horiz-adv-x horiz-origin-x image-rendering letter-spacing lighting-color marker-end marker-mid marker-start overline-position overline-thickness paint-order panose-1 pointer-events rendering-intent shape-rendering stop-color stop-opacity strikethrough-position strikethrough-thickness stroke-dasharray stroke-dashoffset stroke-linecap stroke-linejoin stroke-miterlimit stroke-opacity stroke-width text-anchor text-decoration text-rendering underline-position underline-thickness unicode-bidi unicode-range units-per-em v-alphabetic v-hanging v-ideographic v-mathematical vector-effect vert-adv-y vert-origin-x vert-origin-y word-spacing writing-mode xmlns:xlink x-height".split(" ").forEach(function(e){var t=e.replace(Ol,Al);De[t]=new He(t,1,!1,e,null,!1,!1)});"xlink:actuate xlink:arcrole xlink:role xlink:show xlink:title xlink:type".split(" ").forEach(function(e){var t=e.replace(Ol,Al);De[t]=new He(t,1,!1,e,"http://www.w3.org/1999/xlink",!1,!1)});["xml:base","xml:lang","xml:space"].forEach(function(e){var t=e.replace(Ol,Al);De[t]=new He(t,1,!1,e,"http://www.w3.org/XML/1998/namespace",!1,!1)});["tabIndex","crossOrigin"].forEach(function(e){De[e]=new He(e,1,!1,e.toLowerCase(),null,!1,!1)});De.xlinkHref=new He("xlinkHref",1,!1,"xlink:href","http://www.w3.org/1999/xlink",!0,!1);["src","href","action","formAction"].forEach(function(e){De[e]=new He(e,1,!1,e.toLowerCase(),null,!0,!0)});function Dl(e,t,r,s){var n=De.hasOwnProperty(t)?De[t]:null;(n!==null?n.type!==0:s||!(2<t.length)||t[0]!=="o"&&t[0]!=="O"||t[1]!=="n"&&t[1]!=="N")&&(jp(t,r,n,s)&&(r=null),s||n===null?wp(t)&&(r===null?e.removeAttribute(t):e.setAttribute(t,""+r)):n.mustUseProperty?e[n.propertyName]=r===null?n.type===3?!1:"":r:(t=n.attributeName,s=n.attributeNamespace,r===null?e.removeAttribute(t):(n=n.type,r=n===3||n===4&&r===!0?"":""+r,s?e.setAttributeNS(s,t,r):e.setAttribute(t,r))))}var Kt=yp.__SECRET_INTERNALS_DO_NOT_USE_OR_YOU_WILL_BE_FIRED,Bn=Symbol.for("react.element"),Yr=Symbol.for("react.portal"),Gr=Symbol.for("react.fragment"),Ll=Symbol.for("react.strict_mode"),No=Symbol.for("react.profiler"),Gd=Symbol.for("react.provider"),Jd=Symbol.for("react.context"),Tl=Symbol.for("react.forward_ref"),jo=Symbol.for("react.suspense"),bo=Symbol.for("react.suspense_list"),Rl=Symbol.for("react.memo"),Zt=Symbol.for("react.lazy"),Xd=Symbol.for("react.offscreen"),Bc=Symbol.iterator;function Os(e){return e===null||typeof e!="object"?null:(e=Bc&&e[Bc]||e["@@iterator"],typeof e=="function"?e:null)}var xe=Object.assign,Ui;function Vs(e){if(Ui===void 0)try{throw Error()}catch(r){var t=r.stack.trim().match(/\n( *(at )?)/);Ui=t&&t[1]||""}return`
`+Ui+e}var zi=!1;function Vi(e,t){if(!e||zi)return"";zi=!0;var r=Error.prepareStackTrace;Error.prepareStackTrace=void 0;try{if(t)if(t=function(){throw Error()},Object.defineProperty(t.prototype,"props",{set:function(){throw Error()}}),typeof Reflect=="object"&&Reflect.construct){try{Reflect.construct(t,[])}catch(u){var s=u}Reflect.construct(e,[],t)}else{try{t.call()}catch(u){s=u}e.call(t.prototype)}else{try{throw Error()}catch(u){s=u}e()}}catch(u){if(u&&s&&typeof u.stack=="string"){for(var n=u.stack.split(`
`),i=s.stack.split(`
`),o=n.length-1,l=i.length-1;1<=o&&0<=l&&n[o]!==i[l];)l--;for(;1<=o&&0<=l;o--,l--)if(n[o]!==i[l]){if(o!==1||l!==1)do if(o--,l--,0>l||n[o]!==i[l]){var c=`
`+n[o].replace(" at new "," at ");return e.displayName&&c.includes("<anonymous>")&&(c=c.replace("<anonymous>",e.displayName)),c}while(1<=o&&0<=l);break}}}finally{zi=!1,Error.prepareStackTrace=r}return(e=e?e.displayName||e.name:"")?Vs(e):""}function bp(e){switch(e.tag){case 5:return Vs(e.type);case 16:return Vs("Lazy");case 13:return Vs("Suspense");case 19:return Vs("SuspenseList");case 0:case 2:case 15:return e=Vi(e.type,!1),e;case 11:return e=Vi(e.type.render,!1),e;case 1:return e=Vi(e.type,!0),e;default:return""}}function ko(e){if(e==null)return null;if(typeof e=="function")return e.displayName||e.name||null;if(typeof e=="string")return e;switch(e){case Gr:return"Fragment";case Yr:return"Portal";case No:return"Profiler";case Ll:return"StrictMode";case jo:return"Suspense";case bo:return"SuspenseList"}if(typeof e=="object")switch(e.$$typeof){case Jd:return(e.displayName||"Context")+".Consumer";case Gd:return(e._context.displayName||"Context")+".Provider";case Tl:var t=e.render;return e=e.displayName,e||(e=t.displayName||t.name||"",e=e!==""?"ForwardRef("+e+")":"ForwardRef"),e;case Rl:return t=e.displayName||null,t!==null?t:ko(e.type)||"Memo";case Zt:t=e._payload,e=e._init;try{return ko(e(t))}catch{}}return null}function kp(e){var t=e.type;switch(e.tag){case 24:return"Cache";case 9:return(t.displayName||"Context")+".Consumer";case 10:return(t._context.displayName||"Context")+".Provider";case 18:return"DehydratedFragment";case 11:return e=t.render,e=e.displayName||e.name||"",t.displayName||(e!==""?"ForwardRef("+e+")":"ForwardRef");case 7:return"Fragment";case 5:return t;case 4:return"Portal";case 3:return"Root";case 6:return"Text";case 16:return ko(t);case 8:return t===Ll?"StrictMode":"Mode";case 22:return"Offscreen";case 12:return"Profiler";case 21:return"Scope";case 13:return"Suspense";case 19:return"SuspenseList";case 25:return"TracingMarker";case 1:case 0:case 17:case 2:case 14:case 15:if(typeof t=="function")return t.displayName||t.name||null;if(typeof t=="string")return t}return null}function yr(e){switch(typeof e){case"boolean":case"number":case"string":case"undefined":return e;case"object":return e;default:return""}}function Zd(e){var t=e.type;return(e=e.nodeName)&&e.toLowerCase()==="input"&&(t==="checkbox"||t==="radio")}function Sp(e){var t=Zd(e)?"checked":"value",r=Object.getOwnPropertyDescriptor(e.constructor.prototype,t),s=""+e[t];if(!e.hasOwnProperty(t)&&typeof r<"u"&&typeof r.get=="function"&&typeof r.set=="function"){var n=r.get,i=r.set;return Object.defineProperty(e,t,{configurable:!0,get:function(){return n.call(this)},set:function(o){s=""+o,i.call(this,o)}}),Object.defineProperty(e,t,{enumerable:r.enumerable}),{getValue:function(){return s},setValue:function(o){s=""+o},stopTracking:function(){e._valueTracker=null,delete e[t]}}}}function Hn(e){e._valueTracker||(e._valueTracker=Sp(e))}function ef(e){if(!e)return!1;var t=e._valueTracker;if(!t)return!0;var r=t.getValue(),s="";return e&&(s=Zd(e)?e.checked?"true":"false":e.value),e=s,e!==r?(t.setValue(e),!0):!1}function ba(e){if(e=e||(typeof document<"u"?document:void 0),typeof e>"u")return null;try{return e.activeElement||e.body}catch{return e.body}}function So(e,t){var r=t.checked;return xe({},t,{defaultChecked:void 0,defaultValue:void 0,value:void 0,checked:r??e._wrapperState.initialChecked})}function Hc(e,t){var r=t.defaultValue==null?"":t.defaultValue,s=t.checked!=null?t.checked:t.defaultChecked;r=yr(t.value!=null?t.value:r),e._wrapperState={initialChecked:s,initialValue:r,controlled:t.type==="checkbox"||t.type==="radio"?t.checked!=null:t.value!=null}}function tf(e,t){t=t.checked,t!=null&&Dl(e,"checked",t,!1)}function Co(e,t){tf(e,t);var r=yr(t.value),s=t.type;if(r!=null)s==="number"?(r===0&&e.value===""||e.value!=r)&&(e.value=""+r):e.value!==""+r&&(e.value=""+r);else if(s==="submit"||s==="reset"){e.removeAttribute("value");return}t.hasOwnProperty("value")?$o(e,t.type,r):t.hasOwnProperty("defaultValue")&&$o(e,t.type,yr(t.defaultValue)),t.checked==null&&t.defaultChecked!=null&&(e.defaultChecked=!!t.defaultChecked)}function Wc(e,t,r){if(t.hasOwnProperty("value")||t.hasOwnProperty("defaultValue")){var s=t.type;if(!(s!=="submit"&&s!=="reset"||t.value!==void 0&&t.value!==null))return;t=""+e._wrapperState.initialValue,r||t===e.value||(e.value=t),e.defaultValue=t}r=e.name,r!==""&&(e.name=""),e.defaultChecked=!!e._wrapperState.initialChecked,r!==""&&(e.name=r)}function $o(e,t,r){(t!=="number"||ba(e.ownerDocument)!==e)&&(r==null?e.defaultValue=""+e._wrapperState.initialValue:e.defaultValue!==""+r&&(e.defaultValue=""+r))}var Bs=Array.isArray;function os(e,t,r,s){if(e=e.options,t){t={};for(var n=0;n<r.length;n++)t["$"+r[n]]=!0;for(r=0;r<e.length;r++)n=t.hasOwnProperty("$"+e[r].value),e[r].selected!==n&&(e[r].selected=n),n&&s&&(e[r].defaultSelected=!0)}else{for(r=""+yr(r),t=null,n=0;n<e.length;n++){if(e[n].value===r){e[n].selected=!0,s&&(e[n].defaultSelected=!0);return}t!==null||e[n].disabled||(t=e[n])}t!==null&&(t.selected=!0)}}function Po(e,t){if(t.dangerouslySetInnerHTML!=null)throw Error(A(91));return xe({},t,{value:void 0,defaultValue:void 0,children:""+e._wrapperState.initialValue})}function qc(e,t){var r=t.value;if(r==null){if(r=t.children,t=t.defaultValue,r!=null){if(t!=null)throw Error(A(92));if(Bs(r)){if(1<r.length)throw Error(A(93));r=r[0]}t=r}t==null&&(t=""),r=t}e._wrapperState={initialValue:yr(r)}}function rf(e,t){var r=yr(t.value),s=yr(t.defaultValue);r!=null&&(r=""+r,r!==e.value&&(e.value=r),t.defaultValue==null&&e.defaultValue!==r&&(e.defaultValue=r)),s!=null&&(e.defaultValue=""+s)}function Kc(e){var t=e.textContent;t===e._wrapperState.initialValue&&t!==""&&t!==null&&(e.value=t)}function sf(e){switch(e){case"svg":return"http://www.w3.org/2000/svg";case"math":return"http://www.w3.org/1998/Math/MathML";default:return"http://www.w3.org/1999/xhtml"}}function Eo(e,t){return e==null||e==="http://www.w3.org/1999/xhtml"?sf(t):e==="http://www.w3.org/2000/svg"&&t==="foreignObject"?"http://www.w3.org/1999/xhtml":e}var Wn,nf=function(e){return typeof MSApp<"u"&&MSApp.execUnsafeLocalFunction?function(t,r,s,n){MSApp.execUnsafeLocalFunction(function(){return e(t,r,s,n)})}:e}(function(e,t){if(e.namespaceURI!=="http://www.w3.org/2000/svg"||"innerHTML"in e)e.innerHTML=t;else{for(Wn=Wn||document.createElement("div"),Wn.innerHTML="<svg>"+t.valueOf().toString()+"</svg>",t=Wn.firstChild;e.firstChild;)e.removeChild(e.firstChild);for(;t.firstChild;)e.appendChild(t.firstChild)}});function ln(e,t){if(t){var r=e.firstChild;if(r&&r===e.lastChild&&r.nodeType===3){r.nodeValue=t;return}}e.textContent=t}var qs={animationIterationCount:!0,aspectRatio:!0,borderImageOutset:!0,borderImageSlice:!0,borderImageWidth:!0,boxFlex:!0,boxFlexGroup:!0,boxOrdinalGroup:!0,columnCount:!0,columns:!0,flex:!0,flexGrow:!0,flexPositive:!0,flexShrink:!0,flexNegative:!0,flexOrder:!0,gridArea:!0,gridRow:!0,gridRowEnd:!0,gridRowSpan:!0,gridRowStart:!0,gridColumn:!0,gridColumnEnd:!0,gridColumnSpan:!0,gridColumnStart:!0,fontWeight:!0,lineClamp:!0,lineHeight:!0,opacity:!0,order:!0,orphans:!0,tabSize:!0,widows:!0,zIndex:!0,zoom:!0,fillOpacity:!0,floodOpacity:!0,stopOpacity:!0,strokeDasharray:!0,strokeDashoffset:!0,strokeMiterlimit:!0,strokeOpacity:!0,strokeWidth:!0},Cp=["Webkit","ms","Moz","O"];Object.keys(qs).forEach(function(e){Cp.forEach(function(t){t=t+e.charAt(0).toUpperCase()+e.substring(1),qs[t]=qs[e]})});function af(e,t,r){return t==null||typeof t=="boolean"||t===""?"":r||typeof t!="number"||t===0||qs.hasOwnProperty(e)&&qs[e]?(""+t).trim():t+"px"}function of(e,t){e=e.style;for(var r in t)if(t.hasOwnProperty(r)){var s=r.indexOf("--")===0,n=af(r,t[r],s);r==="float"&&(r="cssFloat"),s?e.setProperty(r,n):e[r]=n}}var $p=xe({menuitem:!0},{area:!0,base:!0,br:!0,col:!0,embed:!0,hr:!0,img:!0,input:!0,keygen:!0,link:!0,meta:!0,param:!0,source:!0,track:!0,wbr:!0});function Oo(e,t){if(t){if($p[e]&&(t.children!=null||t.dangerouslySetInnerHTML!=null))throw Error(A(137,e));if(t.dangerouslySetInnerHTML!=null){if(t.children!=null)throw Error(A(60));if(typeof t.dangerouslySetInnerHTML!="object"||!("__html"in t.dangerouslySetInnerHTML))throw Error(A(61))}if(t.style!=null&&typeof t.style!="object")throw Error(A(62))}}function Ao(e,t){if(e.indexOf("-")===-1)return typeof t.is=="string";switch(e){case"annotation-xml":case"color-profile":case"font-face":case"font-face-src":case"font-face-uri":case"font-face-format":case"font-face-name":case"missing-glyph":return!1;default:return!0}}var Do=null;function _l(e){return e=e.target||e.srcElement||window,e.correspondingUseElement&&(e=e.correspondingUseElement),e.nodeType===3?e.parentNode:e}var Lo=null,ls=null,cs=null;function Qc(e){if(e=Ln(e)){if(typeof Lo!="function")throw Error(A(280));var t=e.stateNode;t&&(t=ui(t),Lo(e.stateNode,e.type,t))}}function lf(e){ls?cs?cs.push(e):cs=[e]:ls=e}function cf(){if(ls){var e=ls,t=cs;if(cs=ls=null,Qc(e),t)for(e=0;e<t.length;e++)Qc(t[e])}}function uf(e,t){return e(t)}function df(){}var Bi=!1;function ff(e,t,r){if(Bi)return e(t,r);Bi=!0;try{return uf(e,t,r)}finally{Bi=!1,(ls!==null||cs!==null)&&(df(),cf())}}function cn(e,t){var r=e.stateNode;if(r===null)return null;var s=ui(r);if(s===null)return null;r=s[t];e:switch(t){case"onClick":case"onClickCapture":case"onDoubleClick":case"onDoubleClickCapture":case"onMouseDown":case"onMouseDownCapture":case"onMouseMove":case"onMouseMoveCapture":case"onMouseUp":case"onMouseUpCapture":case"onMouseEnter":(s=!s.disabled)||(e=e.type,s=!(e==="button"||e==="input"||e==="select"||e==="textarea")),e=!s;break e;default:e=!1}if(e)return null;if(r&&typeof r!="function")throw Error(A(231,t,typeof r));return r}var To=!1;if(Vt)try{var As={};Object.defineProperty(As,"passive",{get:function(){To=!0}}),window.addEventListener("test",As,As),window.removeEventListener("test",As,As)}catch{To=!1}function Pp(e,t,r,s,n,i,o,l,c){var u=Array.prototype.slice.call(arguments,3);try{t.apply(r,u)}catch(d){this.onError(d)}}var Ks=!1,ka=null,Sa=!1,Ro=null,Ep={onError:function(e){Ks=!0,ka=e}};function Op(e,t,r,s,n,i,o,l,c){Ks=!1,ka=null,Pp.apply(Ep,arguments)}function Ap(e,t,r,s,n,i,o,l,c){if(Op.apply(this,arguments),Ks){if(Ks){var u=ka;Ks=!1,ka=null}else throw Error(A(198));Sa||(Sa=!0,Ro=u)}}function Vr(e){var t=e,r=e;if(e.alternate)for(;t.return;)t=t.return;else{e=t;do t=e,t.flags&4098&&(r=t.return),e=t.return;while(e)}return t.tag===3?r:null}function mf(e){if(e.tag===13){var t=e.memoizedState;if(t===null&&(e=e.alternate,e!==null&&(t=e.memoizedState)),t!==null)return t.dehydrated}return null}function Yc(e){if(Vr(e)!==e)throw Error(A(188))}function Dp(e){var t=e.alternate;if(!t){if(t=Vr(e),t===null)throw Error(A(188));return t!==e?null:e}for(var r=e,s=t;;){var n=r.return;if(n===null)break;var i=n.alternate;if(i===null){if(s=n.return,s!==null){r=s;continue}break}if(n.child===i.child){for(i=n.child;i;){if(i===r)return Yc(n),e;if(i===s)return Yc(n),t;i=i.sibling}throw Error(A(188))}if(r.return!==s.return)r=n,s=i;else{for(var o=!1,l=n.child;l;){if(l===r){o=!0,r=n,s=i;break}if(l===s){o=!0,s=n,r=i;break}l=l.sibling}if(!o){for(l=i.child;l;){if(l===r){o=!0,r=i,s=n;break}if(l===s){o=!0,s=i,r=n;break}l=l.sibling}if(!o)throw Error(A(189))}}if(r.alternate!==s)throw Error(A(190))}if(r.tag!==3)throw Error(A(188));return r.stateNode.current===r?e:t}function hf(e){return e=Dp(e),e!==null?pf(e):null}function pf(e){if(e.tag===5||e.tag===6)return e;for(e=e.child;e!==null;){var t=pf(e);if(t!==null)return t;e=e.sibling}return null}var gf=rt.unstable_scheduleCallback,Gc=rt.unstable_cancelCallback,Lp=rt.unstable_shouldYield,Tp=rt.unstable_requestPaint,ve=rt.unstable_now,Rp=rt.unstable_getCurrentPriorityLevel,Fl=rt.unstable_ImmediatePriority,xf=rt.unstable_UserBlockingPriority,Ca=rt.unstable_NormalPriority,_p=rt.unstable_LowPriority,yf=rt.unstable_IdlePriority,ii=null,Ot=null;function Fp(e){if(Ot&&typeof Ot.onCommitFiberRoot=="function")try{Ot.onCommitFiberRoot(ii,e,void 0,(e.current.flags&128)===128)}catch{}}var wt=Math.clz32?Math.clz32:Up,Ip=Math.log,Mp=Math.LN2;function Up(e){return e>>>=0,e===0?32:31-(Ip(e)/Mp|0)|0}var qn=64,Kn=4194304;function Hs(e){switch(e&-e){case 1:return 1;case 2:return 2;case 4:return 4;case 8:return 8;case 16:return 16;case 32:return 32;case 64:case 128:case 256:case 512:case 1024:case 2048:case 4096:case 8192:case 16384:case 32768:case 65536:case 131072:case 262144:case 524288:case 1048576:case 2097152:return e&4194240;case 4194304:case 8388608:case 16777216:case 33554432:case 67108864:return e&130023424;case 134217728:return 134217728;case 268435456:return 268435456;case 536870912:return 536870912;case 1073741824:return 1073741824;default:return e}}function $a(e,t){var r=e.pendingLanes;if(r===0)return 0;var s=0,n=e.suspendedLanes,i=e.pingedLanes,o=r&268435455;if(o!==0){var l=o&~n;l!==0?s=Hs(l):(i&=o,i!==0&&(s=Hs(i)))}else o=r&~n,o!==0?s=Hs(o):i!==0&&(s=Hs(i));if(s===0)return 0;if(t!==0&&t!==s&&!(t&n)&&(n=s&-s,i=t&-t,n>=i||n===16&&(i&4194240)!==0))return t;if(s&4&&(s|=r&16),t=e.entangledLanes,t!==0)for(e=e.entanglements,t&=s;0<t;)r=31-wt(t),n=1<<r,s|=e[r],t&=~n;return s}function zp(e,t){switch(e){case 1:case 2:case 4:return t+250;case 8:case 16:case 32:case 64:case 128:case 256:case 512:case 1024:case 2048:case 4096:case 8192:case 16384:case 32768:case 65536:case 131072:case 262144:case 524288:case 1048576:case 2097152:return t+5e3;case 4194304:case 8388608:case 16777216:case 33554432:case 67108864:return-1;case 134217728:case 268435456:case 536870912:case 1073741824:return-1;default:return-1}}function Vp(e,t){for(var r=e.suspendedLanes,s=e.pingedLanes,n=e.expirationTimes,i=e.pendingLanes;0<i;){var o=31-wt(i),l=1<<o,c=n[o];c===-1?(!(l&r)||l&s)&&(n[o]=zp(l,t)):c<=t&&(e.expiredLanes|=l),i&=~l}}function _o(e){return e=e.pendingLanes&-1073741825,e!==0?e:e&1073741824?1073741824:0}function vf(){var e=qn;return qn<<=1,!(qn&4194240)&&(qn=64),e}function Hi(e){for(var t=[],r=0;31>r;r++)t.push(e);return t}function An(e,t,r){e.pendingLanes|=t,t!==536870912&&(e.suspendedLanes=0,e.pingedLanes=0),e=e.eventTimes,t=31-wt(t),e[t]=r}function Bp(e,t){var r=e.pendingLanes&~t;e.pendingLanes=t,e.suspendedLanes=0,e.pingedLanes=0,e.expiredLanes&=t,e.mutableReadLanes&=t,e.entangledLanes&=t,t=e.entanglements;var s=e.eventTimes;for(e=e.expirationTimes;0<r;){var n=31-wt(r),i=1<<n;t[n]=0,s[n]=-1,e[n]=-1,r&=~i}}function Il(e,t){var r=e.entangledLanes|=t;for(e=e.entanglements;r;){var s=31-wt(r),n=1<<s;n&t|e[s]&t&&(e[s]|=t),r&=~n}}var se=0;function wf(e){return e&=-e,1<e?4<e?e&268435455?16:536870912:4:1}var Nf,Ml,jf,bf,kf,Fo=!1,Qn=[],cr=null,ur=null,dr=null,un=new Map,dn=new Map,tr=[],Hp="mousedown mouseup touchcancel touchend touchstart auxclick dblclick pointercancel pointerdown pointerup dragend dragstart drop compositionend compositionstart keydown keypress keyup input textInput copy cut paste click change contextmenu reset submit".split(" ");function Jc(e,t){switch(e){case"focusin":case"focusout":cr=null;break;case"dragenter":case"dragleave":ur=null;break;case"mouseover":case"mouseout":dr=null;break;case"pointerover":case"pointerout":un.delete(t.pointerId);break;case"gotpointercapture":case"lostpointercapture":dn.delete(t.pointerId)}}function Ds(e,t,r,s,n,i){return e===null||e.nativeEvent!==i?(e={blockedOn:t,domEventName:r,eventSystemFlags:s,nativeEvent:i,targetContainers:[n]},t!==null&&(t=Ln(t),t!==null&&Ml(t)),e):(e.eventSystemFlags|=s,t=e.targetContainers,n!==null&&t.indexOf(n)===-1&&t.push(n),e)}function Wp(e,t,r,s,n){switch(t){case"focusin":return cr=Ds(cr,e,t,r,s,n),!0;case"dragenter":return ur=Ds(ur,e,t,r,s,n),!0;case"mouseover":return dr=Ds(dr,e,t,r,s,n),!0;case"pointerover":var i=n.pointerId;return un.set(i,Ds(un.get(i)||null,e,t,r,s,n)),!0;case"gotpointercapture":return i=n.pointerId,dn.set(i,Ds(dn.get(i)||null,e,t,r,s,n)),!0}return!1}function Sf(e){var t=$r(e.target);if(t!==null){var r=Vr(t);if(r!==null){if(t=r.tag,t===13){if(t=mf(r),t!==null){e.blockedOn=t,kf(e.priority,function(){jf(r)});return}}else if(t===3&&r.stateNode.current.memoizedState.isDehydrated){e.blockedOn=r.tag===3?r.stateNode.containerInfo:null;return}}}e.blockedOn=null}function ua(e){if(e.blockedOn!==null)return!1;for(var t=e.targetContainers;0<t.length;){var r=Io(e.domEventName,e.eventSystemFlags,t[0],e.nativeEvent);if(r===null){r=e.nativeEvent;var s=new r.constructor(r.type,r);Do=s,r.target.dispatchEvent(s),Do=null}else return t=Ln(r),t!==null&&Ml(t),e.blockedOn=r,!1;t.shift()}return!0}function Xc(e,t,r){ua(e)&&r.delete(t)}function qp(){Fo=!1,cr!==null&&ua(cr)&&(cr=null),ur!==null&&ua(ur)&&(ur=null),dr!==null&&ua(dr)&&(dr=null),un.forEach(Xc),dn.forEach(Xc)}function Ls(e,t){e.blockedOn===t&&(e.blockedOn=null,Fo||(Fo=!0,rt.unstable_scheduleCallback(rt.unstable_NormalPriority,qp)))}function fn(e){function t(n){return Ls(n,e)}if(0<Qn.length){Ls(Qn[0],e);for(var r=1;r<Qn.length;r++){var s=Qn[r];s.blockedOn===e&&(s.blockedOn=null)}}for(cr!==null&&Ls(cr,e),ur!==null&&Ls(ur,e),dr!==null&&Ls(dr,e),un.forEach(t),dn.forEach(t),r=0;r<tr.length;r++)s=tr[r],s.blockedOn===e&&(s.blockedOn=null);for(;0<tr.length&&(r=tr[0],r.blockedOn===null);)Sf(r),r.blockedOn===null&&tr.shift()}var us=Kt.ReactCurrentBatchConfig,Pa=!0;function Kp(e,t,r,s){var n=se,i=us.transition;us.transition=null;try{se=1,Ul(e,t,r,s)}finally{se=n,us.transition=i}}function Qp(e,t,r,s){var n=se,i=us.transition;us.transition=null;try{se=4,Ul(e,t,r,s)}finally{se=n,us.transition=i}}function Ul(e,t,r,s){if(Pa){var n=Io(e,t,r,s);if(n===null)eo(e,t,s,Ea,r),Jc(e,s);else if(Wp(n,e,t,r,s))s.stopPropagation();else if(Jc(e,s),t&4&&-1<Hp.indexOf(e)){for(;n!==null;){var i=Ln(n);if(i!==null&&Nf(i),i=Io(e,t,r,s),i===null&&eo(e,t,s,Ea,r),i===n)break;n=i}n!==null&&s.stopPropagation()}else eo(e,t,s,null,r)}}var Ea=null;function Io(e,t,r,s){if(Ea=null,e=_l(s),e=$r(e),e!==null)if(t=Vr(e),t===null)e=null;else if(r=t.tag,r===13){if(e=mf(t),e!==null)return e;e=null}else if(r===3){if(t.stateNode.current.memoizedState.isDehydrated)return t.tag===3?t.stateNode.containerInfo:null;e=null}else t!==e&&(e=null);return Ea=e,null}function Cf(e){switch(e){case"cancel":case"click":case"close":case"contextmenu":case"copy":case"cut":case"auxclick":case"dblclick":case"dragend":case"dragstart":case"drop":case"focusin":case"focusout":case"input":case"invalid":case"keydown":case"keypress":case"keyup":case"mousedown":case"mouseup":case"paste":case"pause":case"play":case"pointercancel":case"pointerdown":case"pointerup":case"ratechange":case"reset":case"resize":case"seeked":case"submit":case"touchcancel":case"touchend":case"touchstart":case"volumechange":case"change":case"selectionchange":case"textInput":case"compositionstart":case"compositionend":case"compositionupdate":case"beforeblur":case"afterblur":case"beforeinput":case"blur":case"fullscreenchange":case"focus":case"hashchange":case"popstate":case"select":case"selectstart":return 1;case"drag":case"dragenter":case"dragexit":case"dragleave":case"dragover":case"mousemove":case"mouseout":case"mouseover":case"pointermove":case"pointerout":case"pointerover":case"scroll":case"toggle":case"touchmove":case"wheel":case"mouseenter":case"mouseleave":case"pointerenter":case"pointerleave":return 4;case"message":switch(Rp()){case Fl:return 1;case xf:return 4;case Ca:case _p:return 16;case yf:return 536870912;default:return 16}default:return 16}}var ir=null,zl=null,da=null;function $f(){if(da)return da;var e,t=zl,r=t.length,s,n="value"in ir?ir.value:ir.textContent,i=n.length;for(e=0;e<r&&t[e]===n[e];e++);var o=r-e;for(s=1;s<=o&&t[r-s]===n[i-s];s++);return da=n.slice(e,1<s?1-s:void 0)}function fa(e){var t=e.keyCode;return"charCode"in e?(e=e.charCode,e===0&&t===13&&(e=13)):e=t,e===10&&(e=13),32<=e||e===13?e:0}function Yn(){return!0}function Zc(){return!1}function nt(e){function t(r,s,n,i,o){this._reactName=r,this._targetInst=n,this.type=s,this.nativeEvent=i,this.target=o,this.currentTarget=null;for(var l in e)e.hasOwnProperty(l)&&(r=e[l],this[l]=r?r(i):i[l]);return this.isDefaultPrevented=(i.defaultPrevented!=null?i.defaultPrevented:i.returnValue===!1)?Yn:Zc,this.isPropagationStopped=Zc,this}return xe(t.prototype,{preventDefault:function(){this.defaultPrevented=!0;var r=this.nativeEvent;r&&(r.preventDefault?r.preventDefault():typeof r.returnValue!="unknown"&&(r.returnValue=!1),this.isDefaultPrevented=Yn)},stopPropagation:function(){var r=this.nativeEvent;r&&(r.stopPropagation?r.stopPropagation():typeof r.cancelBubble!="unknown"&&(r.cancelBubble=!0),this.isPropagationStopped=Yn)},persist:function(){},isPersistent:Yn}),t}var ks={eventPhase:0,bubbles:0,cancelable:0,timeStamp:function(e){return e.timeStamp||Date.now()},defaultPrevented:0,isTrusted:0},Vl=nt(ks),Dn=xe({},ks,{view:0,detail:0}),Yp=nt(Dn),Wi,qi,Ts,oi=xe({},Dn,{screenX:0,screenY:0,clientX:0,clientY:0,pageX:0,pageY:0,ctrlKey:0,shiftKey:0,altKey:0,metaKey:0,getModifierState:Bl,button:0,buttons:0,relatedTarget:function(e){return e.relatedTarget===void 0?e.fromElement===e.srcElement?e.toElement:e.fromElement:e.relatedTarget},movementX:function(e){return"movementX"in e?e.movementX:(e!==Ts&&(Ts&&e.type==="mousemove"?(Wi=e.screenX-Ts.screenX,qi=e.screenY-Ts.screenY):qi=Wi=0,Ts=e),Wi)},movementY:function(e){return"movementY"in e?e.movementY:qi}}),eu=nt(oi),Gp=xe({},oi,{dataTransfer:0}),Jp=nt(Gp),Xp=xe({},Dn,{relatedTarget:0}),Ki=nt(Xp),Zp=xe({},ks,{animationName:0,elapsedTime:0,pseudoElement:0}),eg=nt(Zp),tg=xe({},ks,{clipboardData:function(e){return"clipboardData"in e?e.clipboardData:window.clipboardData}}),rg=nt(tg),sg=xe({},ks,{data:0}),tu=nt(sg),ng={Esc:"Escape",Spacebar:" ",Left:"ArrowLeft",Up:"ArrowUp",Right:"ArrowRight",Down:"ArrowDown",Del:"Delete",Win:"OS",Menu:"ContextMenu",Apps:"ContextMenu",Scroll:"ScrollLock",MozPrintableKey:"Unidentified"},ag={8:"Backspace",9:"Tab",12:"Clear",13:"Enter",16:"Shift",17:"Control",18:"Alt",19:"Pause",20:"CapsLock",27:"Escape",32:" ",33:"PageUp",34:"PageDown",35:"End",36:"Home",37:"ArrowLeft",38:"ArrowUp",39:"ArrowRight",40:"ArrowDown",45:"Insert",46:"Delete",112:"F1",113:"F2",114:"F3",115:"F4",116:"F5",117:"F6",118:"F7",119:"F8",120:"F9",121:"F10",122:"F11",123:"F12",144:"NumLock",145:"ScrollLock",224:"Meta"},ig={Alt:"altKey",Control:"ctrlKey",Meta:"metaKey",Shift:"shiftKey"};function og(e){var t=this.nativeEvent;return t.getModifierState?t.getModifierState(e):(e=ig[e])?!!t[e]:!1}function Bl(){return og}var lg=xe({},Dn,{key:function(e){if(e.key){var t=ng[e.key]||e.key;if(t!=="Unidentified")return t}return e.type==="keypress"?(e=fa(e),e===13?"Enter":String.fromCharCode(e)):e.type==="keydown"||e.type==="keyup"?ag[e.keyCode]||"Unidentified":""},code:0,location:0,ctrlKey:0,shiftKey:0,altKey:0,metaKey:0,repeat:0,locale:0,getModifierState:Bl,charCode:function(e){return e.type==="keypress"?fa(e):0},keyCode:function(e){return e.type==="keydown"||e.type==="keyup"?e.keyCode:0},which:function(e){return e.type==="keypress"?fa(e):e.type==="keydown"||e.type==="keyup"?e.keyCode:0}}),cg=nt(lg),ug=xe({},oi,{pointerId:0,width:0,height:0,pressure:0,tangentialPressure:0,tiltX:0,tiltY:0,twist:0,pointerType:0,isPrimary:0}),ru=nt(ug),dg=xe({},Dn,{touches:0,targetTouches:0,changedTouches:0,altKey:0,metaKey:0,ctrlKey:0,shiftKey:0,getModifierState:Bl}),fg=nt(dg),mg=xe({},ks,{propertyName:0,elapsedTime:0,pseudoElement:0}),hg=nt(mg),pg=xe({},oi,{deltaX:function(e){return"deltaX"in e?e.deltaX:"wheelDeltaX"in e?-e.wheelDeltaX:0},deltaY:function(e){return"deltaY"in e?e.deltaY:"wheelDeltaY"in e?-e.wheelDeltaY:"wheelDelta"in e?-e.wheelDelta:0},deltaZ:0,deltaMode:0}),gg=nt(pg),xg=[9,13,27,32],Hl=Vt&&"CompositionEvent"in window,Qs=null;Vt&&"documentMode"in document&&(Qs=document.documentMode);var yg=Vt&&"TextEvent"in window&&!Qs,Pf=Vt&&(!Hl||Qs&&8<Qs&&11>=Qs),su=String.fromCharCode(32),nu=!1;function Ef(e,t){switch(e){case"keyup":return xg.indexOf(t.keyCode)!==-1;case"keydown":return t.keyCode!==229;case"keypress":case"mousedown":case"focusout":return!0;default:return!1}}function Of(e){return e=e.detail,typeof e=="object"&&"data"in e?e.data:null}var Jr=!1;function vg(e,t){switch(e){case"compositionend":return Of(t);case"keypress":return t.which!==32?null:(nu=!0,su);case"textInput":return e=t.data,e===su&&nu?null:e;default:return null}}function wg(e,t){if(Jr)return e==="compositionend"||!Hl&&Ef(e,t)?(e=$f(),da=zl=ir=null,Jr=!1,e):null;switch(e){case"paste":return null;case"keypress":if(!(t.ctrlKey||t.altKey||t.metaKey)||t.ctrlKey&&t.altKey){if(t.char&&1<t.char.length)return t.char;if(t.which)return String.fromCharCode(t.which)}return null;case"compositionend":return Pf&&t.locale!=="ko"?null:t.data;default:return null}}var Ng={color:!0,date:!0,datetime:!0,"datetime-local":!0,email:!0,month:!0,number:!0,password:!0,range:!0,search:!0,tel:!0,text:!0,time:!0,url:!0,week:!0};function au(e){var t=e&&e.nodeName&&e.nodeName.toLowerCase();return t==="input"?!!Ng[e.type]:t==="textarea"}function Af(e,t,r,s){lf(s),t=Oa(t,"onChange"),0<t.length&&(r=new Vl("onChange","change",null,r,s),e.push({event:r,listeners:t}))}var Ys=null,mn=null;function jg(e){Vf(e,0)}function li(e){var t=es(e);if(ef(t))return e}function bg(e,t){if(e==="change")return t}var Df=!1;if(Vt){var Qi;if(Vt){var Yi="oninput"in document;if(!Yi){var iu=document.createElement("div");iu.setAttribute("oninput","return;"),Yi=typeof iu.oninput=="function"}Qi=Yi}else Qi=!1;Df=Qi&&(!document.documentMode||9<document.documentMode)}function ou(){Ys&&(Ys.detachEvent("onpropertychange",Lf),mn=Ys=null)}function Lf(e){if(e.propertyName==="value"&&li(mn)){var t=[];Af(t,mn,e,_l(e)),ff(jg,t)}}function kg(e,t,r){e==="focusin"?(ou(),Ys=t,mn=r,Ys.attachEvent("onpropertychange",Lf)):e==="focusout"&&ou()}function Sg(e){if(e==="selectionchange"||e==="keyup"||e==="keydown")return li(mn)}function Cg(e,t){if(e==="click")return li(t)}function $g(e,t){if(e==="input"||e==="change")return li(t)}function Pg(e,t){return e===t&&(e!==0||1/e===1/t)||e!==e&&t!==t}var jt=typeof Object.is=="function"?Object.is:Pg;function hn(e,t){if(jt(e,t))return!0;if(typeof e!="object"||e===null||typeof t!="object"||t===null)return!1;var r=Object.keys(e),s=Object.keys(t);if(r.length!==s.length)return!1;for(s=0;s<r.length;s++){var n=r[s];if(!wo.call(t,n)||!jt(e[n],t[n]))return!1}return!0}function lu(e){for(;e&&e.firstChild;)e=e.firstChild;return e}function cu(e,t){var r=lu(e);e=0;for(var s;r;){if(r.nodeType===3){if(s=e+r.textContent.length,e<=t&&s>=t)return{node:r,offset:t-e};e=s}e:{for(;r;){if(r.nextSibling){r=r.nextSibling;break e}r=r.parentNode}r=void 0}r=lu(r)}}function Tf(e,t){return e&&t?e===t?!0:e&&e.nodeType===3?!1:t&&t.nodeType===3?Tf(e,t.parentNode):"contains"in e?e.contains(t):e.compareDocumentPosition?!!(e.compareDocumentPosition(t)&16):!1:!1}function Rf(){for(var e=window,t=ba();t instanceof e.HTMLIFrameElement;){try{var r=typeof t.contentWindow.location.href=="string"}catch{r=!1}if(r)e=t.contentWindow;else break;t=ba(e.document)}return t}function Wl(e){var t=e&&e.nodeName&&e.nodeName.toLowerCase();return t&&(t==="input"&&(e.type==="text"||e.type==="search"||e.type==="tel"||e.type==="url"||e.type==="password")||t==="textarea"||e.contentEditable==="true")}function Eg(e){var t=Rf(),r=e.focusedElem,s=e.selectionRange;if(t!==r&&r&&r.ownerDocument&&Tf(r.ownerDocument.documentElement,r)){if(s!==null&&Wl(r)){if(t=s.start,e=s.end,e===void 0&&(e=t),"selectionStart"in r)r.selectionStart=t,r.selectionEnd=Math.min(e,r.value.length);else if(e=(t=r.ownerDocument||document)&&t.defaultView||window,e.getSelection){e=e.getSelection();var n=r.textContent.length,i=Math.min(s.start,n);s=s.end===void 0?i:Math.min(s.end,n),!e.extend&&i>s&&(n=s,s=i,i=n),n=cu(r,i);var o=cu(r,s);n&&o&&(e.rangeCount!==1||e.anchorNode!==n.node||e.anchorOffset!==n.offset||e.focusNode!==o.node||e.focusOffset!==o.offset)&&(t=t.createRange(),t.setStart(n.node,n.offset),e.removeAllRanges(),i>s?(e.addRange(t),e.extend(o.node,o.offset)):(t.setEnd(o.node,o.offset),e.addRange(t)))}}for(t=[],e=r;e=e.parentNode;)e.nodeType===1&&t.push({element:e,left:e.scrollLeft,top:e.scrollTop});for(typeof r.focus=="function"&&r.focus(),r=0;r<t.length;r++)e=t[r],e.element.scrollLeft=e.left,e.element.scrollTop=e.top}}var Og=Vt&&"documentMode"in document&&11>=document.documentMode,Xr=null,Mo=null,Gs=null,Uo=!1;function uu(e,t,r){var s=r.window===r?r.document:r.nodeType===9?r:r.ownerDocument;Uo||Xr==null||Xr!==ba(s)||(s=Xr,"selectionStart"in s&&Wl(s)?s={start:s.selectionStart,end:s.selectionEnd}:(s=(s.ownerDocument&&s.ownerDocument.defaultView||window).getSelection(),s={anchorNode:s.anchorNode,anchorOffset:s.anchorOffset,focusNode:s.focusNode,focusOffset:s.focusOffset}),Gs&&hn(Gs,s)||(Gs=s,s=Oa(Mo,"onSelect"),0<s.length&&(t=new Vl("onSelect","select",null,t,r),e.push({event:t,listeners:s}),t.target=Xr)))}function Gn(e,t){var r={};return r[e.toLowerCase()]=t.toLowerCase(),r["Webkit"+e]="webkit"+t,r["Moz"+e]="moz"+t,r}var Zr={animationend:Gn("Animation","AnimationEnd"),animationiteration:Gn("Animation","AnimationIteration"),animationstart:Gn("Animation","AnimationStart"),transitionend:Gn("Transition","TransitionEnd")},Gi={},_f={};Vt&&(_f=document.createElement("div").style,"AnimationEvent"in window||(delete Zr.animationend.animation,delete Zr.animationiteration.animation,delete Zr.animationstart.animation),"TransitionEvent"in window||delete Zr.transitionend.transition);function ci(e){if(Gi[e])return Gi[e];if(!Zr[e])return e;var t=Zr[e],r;for(r in t)if(t.hasOwnProperty(r)&&r in _f)return Gi[e]=t[r];return e}var Ff=ci("animationend"),If=ci("animationiteration"),Mf=ci("animationstart"),Uf=ci("transitionend"),zf=new Map,du="abort auxClick cancel canPlay canPlayThrough click close contextMenu copy cut drag dragEnd dragEnter dragExit dragLeave dragOver dragStart drop durationChange emptied encrypted ended error gotPointerCapture input invalid keyDown keyPress keyUp load loadedData loadedMetadata loadStart lostPointerCapture mouseDown mouseMove mouseOut mouseOver mouseUp paste pause play playing pointerCancel pointerDown pointerMove pointerOut pointerOver pointerUp progress rateChange reset resize seeked seeking stalled submit suspend timeUpdate touchCancel touchEnd touchStart volumeChange scroll toggle touchMove waiting wheel".split(" ");function Nr(e,t){zf.set(e,t),zr(t,[e])}for(var Ji=0;Ji<du.length;Ji++){var Xi=du[Ji],Ag=Xi.toLowerCase(),Dg=Xi[0].toUpperCase()+Xi.slice(1);Nr(Ag,"on"+Dg)}Nr(Ff,"onAnimationEnd");Nr(If,"onAnimationIteration");Nr(Mf,"onAnimationStart");Nr("dblclick","onDoubleClick");Nr("focusin","onFocus");Nr("focusout","onBlur");Nr(Uf,"onTransitionEnd");hs("onMouseEnter",["mouseout","mouseover"]);hs("onMouseLeave",["mouseout","mouseover"]);hs("onPointerEnter",["pointerout","pointerover"]);hs("onPointerLeave",["pointerout","pointerover"]);zr("onChange","change click focusin focusout input keydown keyup selectionchange".split(" "));zr("onSelect","focusout contextmenu dragend focusin keydown keyup mousedown mouseup selectionchange".split(" "));zr("onBeforeInput",["compositionend","keypress","textInput","paste"]);zr("onCompositionEnd","compositionend focusout keydown keypress keyup mousedown".split(" "));zr("onCompositionStart","compositionstart focusout keydown keypress keyup mousedown".split(" "));zr("onCompositionUpdate","compositionupdate focusout keydown keypress keyup mousedown".split(" "));var Ws="abort canplay canplaythrough durationchange emptied encrypted ended error loadeddata loadedmetadata loadstart pause play playing progress ratechange resize seeked seeking stalled suspend timeupdate volumechange waiting".split(" "),Lg=new Set("cancel close invalid load scroll toggle".split(" ").concat(Ws));function fu(e,t,r){var s=e.type||"unknown-event";e.currentTarget=r,Ap(s,t,void 0,e),e.currentTarget=null}function Vf(e,t){t=(t&4)!==0;for(var r=0;r<e.length;r++){var s=e[r],n=s.event;s=s.listeners;e:{var i=void 0;if(t)for(var o=s.length-1;0<=o;o--){var l=s[o],c=l.instance,u=l.currentTarget;if(l=l.listener,c!==i&&n.isPropagationStopped())break e;fu(n,l,u),i=c}else for(o=0;o<s.length;o++){if(l=s[o],c=l.instance,u=l.currentTarget,l=l.listener,c!==i&&n.isPropagationStopped())break e;fu(n,l,u),i=c}}}if(Sa)throw e=Ro,Sa=!1,Ro=null,e}function le(e,t){var r=t[Wo];r===void 0&&(r=t[Wo]=new Set);var s=e+"__bubble";r.has(s)||(Bf(t,e,2,!1),r.add(s))}function Zi(e,t,r){var s=0;t&&(s|=4),Bf(r,e,s,t)}var Jn="_reactListening"+Math.random().toString(36).slice(2);function pn(e){if(!e[Jn]){e[Jn]=!0,Yd.forEach(function(r){r!=="selectionchange"&&(Lg.has(r)||Zi(r,!1,e),Zi(r,!0,e))});var t=e.nodeType===9?e:e.ownerDocument;t===null||t[Jn]||(t[Jn]=!0,Zi("selectionchange",!1,t))}}function Bf(e,t,r,s){switch(Cf(t)){case 1:var n=Kp;break;case 4:n=Qp;break;default:n=Ul}r=n.bind(null,t,r,e),n=void 0,!To||t!=="touchstart"&&t!=="touchmove"&&t!=="wheel"||(n=!0),s?n!==void 0?e.addEventListener(t,r,{capture:!0,passive:n}):e.addEventListener(t,r,!0):n!==void 0?e.addEventListener(t,r,{passive:n}):e.addEventListener(t,r,!1)}function eo(e,t,r,s,n){var i=s;if(!(t&1)&&!(t&2)&&s!==null)e:for(;;){if(s===null)return;var o=s.tag;if(o===3||o===4){var l=s.stateNode.containerInfo;if(l===n||l.nodeType===8&&l.parentNode===n)break;if(o===4)for(o=s.return;o!==null;){var c=o.tag;if((c===3||c===4)&&(c=o.stateNode.containerInfo,c===n||c.nodeType===8&&c.parentNode===n))return;o=o.return}for(;l!==null;){if(o=$r(l),o===null)return;if(c=o.tag,c===5||c===6){s=i=o;continue e}l=l.parentNode}}s=s.return}ff(function(){var u=i,d=_l(r),f=[];e:{var m=zf.get(e);if(m!==void 0){var v=Vl,g=e;switch(e){case"keypress":if(fa(r)===0)break e;case"keydown":case"keyup":v=cg;break;case"focusin":g="focus",v=Ki;break;case"focusout":g="blur",v=Ki;break;case"beforeblur":case"afterblur":v=Ki;break;case"click":if(r.button===2)break e;case"auxclick":case"dblclick":case"mousedown":case"mousemove":case"mouseup":case"mouseout":case"mouseover":case"contextmenu":v=eu;break;case"drag":case"dragend":case"dragenter":case"dragexit":case"dragleave":case"dragover":case"dragstart":case"drop":v=Jp;break;case"touchcancel":case"touchend":case"touchmove":case"touchstart":v=fg;break;case Ff:case If:case Mf:v=eg;break;case Uf:v=hg;break;case"scroll":v=Yp;break;case"wheel":v=gg;break;case"copy":case"cut":case"paste":v=rg;break;case"gotpointercapture":case"lostpointercapture":case"pointercancel":case"pointerdown":case"pointermove":case"pointerout":case"pointerover":case"pointerup":v=ru}var w=(t&4)!==0,y=!w&&e==="scroll",p=w?m!==null?m+"Capture":null:m;w=[];for(var h=u,x;h!==null;){x=h;var b=x.stateNode;if(x.tag===5&&b!==null&&(x=b,p!==null&&(b=cn(h,p),b!=null&&w.push(gn(h,b,x)))),y)break;h=h.return}0<w.length&&(m=new v(m,g,null,r,d),f.push({event:m,listeners:w}))}}if(!(t&7)){e:{if(m=e==="mouseover"||e==="pointerover",v=e==="mouseout"||e==="pointerout",m&&r!==Do&&(g=r.relatedTarget||r.fromElement)&&($r(g)||g[Bt]))break e;if((v||m)&&(m=d.window===d?d:(m=d.ownerDocument)?m.defaultView||m.parentWindow:window,v?(g=r.relatedTarget||r.toElement,v=u,g=g?$r(g):null,g!==null&&(y=Vr(g),g!==y||g.tag!==5&&g.tag!==6)&&(g=null)):(v=null,g=u),v!==g)){if(w=eu,b="onMouseLeave",p="onMouseEnter",h="mouse",(e==="pointerout"||e==="pointerover")&&(w=ru,b="onPointerLeave",p="onPointerEnter",h="pointer"),y=v==null?m:es(v),x=g==null?m:es(g),m=new w(b,h+"leave",v,r,d),m.target=y,m.relatedTarget=x,b=null,$r(d)===u&&(w=new w(p,h+"enter",g,r,d),w.target=x,w.relatedTarget=y,b=w),y=b,v&&g)t:{for(w=v,p=g,h=0,x=w;x;x=Hr(x))h++;for(x=0,b=p;b;b=Hr(b))x++;for(;0<h-x;)w=Hr(w),h--;for(;0<x-h;)p=Hr(p),x--;for(;h--;){if(w===p||p!==null&&w===p.alternate)break t;w=Hr(w),p=Hr(p)}w=null}else w=null;v!==null&&mu(f,m,v,w,!1),g!==null&&y!==null&&mu(f,y,g,w,!0)}}e:{if(m=u?es(u):window,v=m.nodeName&&m.nodeName.toLowerCase(),v==="select"||v==="input"&&m.type==="file")var k=bg;else if(au(m))if(Df)k=$g;else{k=Sg;var E=kg}else(v=m.nodeName)&&v.toLowerCase()==="input"&&(m.type==="checkbox"||m.type==="radio")&&(k=Cg);if(k&&(k=k(e,u))){Af(f,k,r,d);break e}E&&E(e,m,u),e==="focusout"&&(E=m._wrapperState)&&E.controlled&&m.type==="number"&&$o(m,"number",m.value)}switch(E=u?es(u):window,e){case"focusin":(au(E)||E.contentEditable==="true")&&(Xr=E,Mo=u,Gs=null);break;case"focusout":Gs=Mo=Xr=null;break;case"mousedown":Uo=!0;break;case"contextmenu":case"mouseup":case"dragend":Uo=!1,uu(f,r,d);break;case"selectionchange":if(Og)break;case"keydown":case"keyup":uu(f,r,d)}var O;if(Hl)e:{switch(e){case"compositionstart":var $="onCompositionStart";break e;case"compositionend":$="onCompositionEnd";break e;case"compositionupdate":$="onCompositionUpdate";break e}$=void 0}else Jr?Ef(e,r)&&($="onCompositionEnd"):e==="keydown"&&r.keyCode===229&&($="onCompositionStart");$&&(Pf&&r.locale!=="ko"&&(Jr||$!=="onCompositionStart"?$==="onCompositionEnd"&&Jr&&(O=$f()):(ir=d,zl="value"in ir?ir.value:ir.textContent,Jr=!0)),E=Oa(u,$),0<E.length&&($=new tu($,e,null,r,d),f.push({event:$,listeners:E}),O?$.data=O:(O=Of(r),O!==null&&($.data=O)))),(O=yg?vg(e,r):wg(e,r))&&(u=Oa(u,"onBeforeInput"),0<u.length&&(d=new tu("onBeforeInput","beforeinput",null,r,d),f.push({event:d,listeners:u}),d.data=O))}Vf(f,t)})}function gn(e,t,r){return{instance:e,listener:t,currentTarget:r}}function Oa(e,t){for(var r=t+"Capture",s=[];e!==null;){var n=e,i=n.stateNode;n.tag===5&&i!==null&&(n=i,i=cn(e,r),i!=null&&s.unshift(gn(e,i,n)),i=cn(e,t),i!=null&&s.push(gn(e,i,n))),e=e.return}return s}function Hr(e){if(e===null)return null;do e=e.return;while(e&&e.tag!==5);return e||null}function mu(e,t,r,s,n){for(var i=t._reactName,o=[];r!==null&&r!==s;){var l=r,c=l.alternate,u=l.stateNode;if(c!==null&&c===s)break;l.tag===5&&u!==null&&(l=u,n?(c=cn(r,i),c!=null&&o.unshift(gn(r,c,l))):n||(c=cn(r,i),c!=null&&o.push(gn(r,c,l)))),r=r.return}o.length!==0&&e.push({event:t,listeners:o})}var Tg=/\r\n?/g,Rg=/\u0000|\uFFFD/g;function hu(e){return(typeof e=="string"?e:""+e).replace(Tg,`
`).replace(Rg,"")}function Xn(e,t,r){if(t=hu(t),hu(e)!==t&&r)throw Error(A(425))}function Aa(){}var zo=null,Vo=null;function Bo(e,t){return e==="textarea"||e==="noscript"||typeof t.children=="string"||typeof t.children=="number"||typeof t.dangerouslySetInnerHTML=="object"&&t.dangerouslySetInnerHTML!==null&&t.dangerouslySetInnerHTML.__html!=null}var Ho=typeof setTimeout=="function"?setTimeout:void 0,_g=typeof clearTimeout=="function"?clearTimeout:void 0,pu=typeof Promise=="function"?Promise:void 0,Fg=typeof queueMicrotask=="function"?queueMicrotask:typeof pu<"u"?function(e){return pu.resolve(null).then(e).catch(Ig)}:Ho;function Ig(e){setTimeout(function(){throw e})}function to(e,t){var r=t,s=0;do{var n=r.nextSibling;if(e.removeChild(r),n&&n.nodeType===8)if(r=n.data,r==="/$"){if(s===0){e.removeChild(n),fn(t);return}s--}else r!=="$"&&r!=="$?"&&r!=="$!"||s++;r=n}while(r);fn(t)}function fr(e){for(;e!=null;e=e.nextSibling){var t=e.nodeType;if(t===1||t===3)break;if(t===8){if(t=e.data,t==="$"||t==="$!"||t==="$?")break;if(t==="/$")return null}}return e}function gu(e){e=e.previousSibling;for(var t=0;e;){if(e.nodeType===8){var r=e.data;if(r==="$"||r==="$!"||r==="$?"){if(t===0)return e;t--}else r==="/$"&&t++}e=e.previousSibling}return null}var Ss=Math.random().toString(36).slice(2),$t="__reactFiber$"+Ss,xn="__reactProps$"+Ss,Bt="__reactContainer$"+Ss,Wo="__reactEvents$"+Ss,Mg="__reactListeners$"+Ss,Ug="__reactHandles$"+Ss;function $r(e){var t=e[$t];if(t)return t;for(var r=e.parentNode;r;){if(t=r[Bt]||r[$t]){if(r=t.alternate,t.child!==null||r!==null&&r.child!==null)for(e=gu(e);e!==null;){if(r=e[$t])return r;e=gu(e)}return t}e=r,r=e.parentNode}return null}function Ln(e){return e=e[$t]||e[Bt],!e||e.tag!==5&&e.tag!==6&&e.tag!==13&&e.tag!==3?null:e}function es(e){if(e.tag===5||e.tag===6)return e.stateNode;throw Error(A(33))}function ui(e){return e[xn]||null}var qo=[],ts=-1;function jr(e){return{current:e}}function ce(e){0>ts||(e.current=qo[ts],qo[ts]=null,ts--)}function ie(e,t){ts++,qo[ts]=e.current,e.current=t}var vr={},Fe=jr(vr),Qe=jr(!1),Rr=vr;function ps(e,t){var r=e.type.contextTypes;if(!r)return vr;var s=e.stateNode;if(s&&s.__reactInternalMemoizedUnmaskedChildContext===t)return s.__reactInternalMemoizedMaskedChildContext;var n={},i;for(i in r)n[i]=t[i];return s&&(e=e.stateNode,e.__reactInternalMemoizedUnmaskedChildContext=t,e.__reactInternalMemoizedMaskedChildContext=n),n}function Ye(e){return e=e.childContextTypes,e!=null}function Da(){ce(Qe),ce(Fe)}function xu(e,t,r){if(Fe.current!==vr)throw Error(A(168));ie(Fe,t),ie(Qe,r)}function Hf(e,t,r){var s=e.stateNode;if(t=t.childContextTypes,typeof s.getChildContext!="function")return r;s=s.getChildContext();for(var n in s)if(!(n in t))throw Error(A(108,kp(e)||"Unknown",n));return xe({},r,s)}function La(e){return e=(e=e.stateNode)&&e.__reactInternalMemoizedMergedChildContext||vr,Rr=Fe.current,ie(Fe,e),ie(Qe,Qe.current),!0}function yu(e,t,r){var s=e.stateNode;if(!s)throw Error(A(169));r?(e=Hf(e,t,Rr),s.__reactInternalMemoizedMergedChildContext=e,ce(Qe),ce(Fe),ie(Fe,e)):ce(Qe),ie(Qe,r)}var It=null,di=!1,ro=!1;function Wf(e){It===null?It=[e]:It.push(e)}function zg(e){di=!0,Wf(e)}function br(){if(!ro&&It!==null){ro=!0;var e=0,t=se;try{var r=It;for(se=1;e<r.length;e++){var s=r[e];do s=s(!0);while(s!==null)}It=null,di=!1}catch(n){throw It!==null&&(It=It.slice(e+1)),gf(Fl,br),n}finally{se=t,ro=!1}}return null}var rs=[],ss=0,Ta=null,Ra=0,lt=[],ct=0,_r=null,Mt=1,Ut="";function Sr(e,t){rs[ss++]=Ra,rs[ss++]=Ta,Ta=e,Ra=t}function qf(e,t,r){lt[ct++]=Mt,lt[ct++]=Ut,lt[ct++]=_r,_r=e;var s=Mt;e=Ut;var n=32-wt(s)-1;s&=~(1<<n),r+=1;var i=32-wt(t)+n;if(30<i){var o=n-n%5;i=(s&(1<<o)-1).toString(32),s>>=o,n-=o,Mt=1<<32-wt(t)+n|r<<n|s,Ut=i+e}else Mt=1<<i|r<<n|s,Ut=e}function ql(e){e.return!==null&&(Sr(e,1),qf(e,1,0))}function Kl(e){for(;e===Ta;)Ta=rs[--ss],rs[ss]=null,Ra=rs[--ss],rs[ss]=null;for(;e===_r;)_r=lt[--ct],lt[ct]=null,Ut=lt[--ct],lt[ct]=null,Mt=lt[--ct],lt[ct]=null}var tt=null,et=null,fe=!1,xt=null;function Kf(e,t){var r=ut(5,null,null,0);r.elementType="DELETED",r.stateNode=t,r.return=e,t=e.deletions,t===null?(e.deletions=[r],e.flags|=16):t.push(r)}function vu(e,t){switch(e.tag){case 5:var r=e.type;return t=t.nodeType!==1||r.toLowerCase()!==t.nodeName.toLowerCase()?null:t,t!==null?(e.stateNode=t,tt=e,et=fr(t.firstChild),!0):!1;case 6:return t=e.pendingProps===""||t.nodeType!==3?null:t,t!==null?(e.stateNode=t,tt=e,et=null,!0):!1;case 13:return t=t.nodeType!==8?null:t,t!==null?(r=_r!==null?{id:Mt,overflow:Ut}:null,e.memoizedState={dehydrated:t,treeContext:r,retryLane:1073741824},r=ut(18,null,null,0),r.stateNode=t,r.return=e,e.child=r,tt=e,et=null,!0):!1;default:return!1}}function Ko(e){return(e.mode&1)!==0&&(e.flags&128)===0}function Qo(e){if(fe){var t=et;if(t){var r=t;if(!vu(e,t)){if(Ko(e))throw Error(A(418));t=fr(r.nextSibling);var s=tt;t&&vu(e,t)?Kf(s,r):(e.flags=e.flags&-4097|2,fe=!1,tt=e)}}else{if(Ko(e))throw Error(A(418));e.flags=e.flags&-4097|2,fe=!1,tt=e}}}function wu(e){for(e=e.return;e!==null&&e.tag!==5&&e.tag!==3&&e.tag!==13;)e=e.return;tt=e}function Zn(e){if(e!==tt)return!1;if(!fe)return wu(e),fe=!0,!1;var t;if((t=e.tag!==3)&&!(t=e.tag!==5)&&(t=e.type,t=t!=="head"&&t!=="body"&&!Bo(e.type,e.memoizedProps)),t&&(t=et)){if(Ko(e))throw Qf(),Error(A(418));for(;t;)Kf(e,t),t=fr(t.nextSibling)}if(wu(e),e.tag===13){if(e=e.memoizedState,e=e!==null?e.dehydrated:null,!e)throw Error(A(317));e:{for(e=e.nextSibling,t=0;e;){if(e.nodeType===8){var r=e.data;if(r==="/$"){if(t===0){et=fr(e.nextSibling);break e}t--}else r!=="$"&&r!=="$!"&&r!=="$?"||t++}e=e.nextSibling}et=null}}else et=tt?fr(e.stateNode.nextSibling):null;return!0}function Qf(){for(var e=et;e;)e=fr(e.nextSibling)}function gs(){et=tt=null,fe=!1}function Ql(e){xt===null?xt=[e]:xt.push(e)}var Vg=Kt.ReactCurrentBatchConfig;function Rs(e,t,r){if(e=r.ref,e!==null&&typeof e!="function"&&typeof e!="object"){if(r._owner){if(r=r._owner,r){if(r.tag!==1)throw Error(A(309));var s=r.stateNode}if(!s)throw Error(A(147,e));var n=s,i=""+e;return t!==null&&t.ref!==null&&typeof t.ref=="function"&&t.ref._stringRef===i?t.ref:(t=function(o){var l=n.refs;o===null?delete l[i]:l[i]=o},t._stringRef=i,t)}if(typeof e!="string")throw Error(A(284));if(!r._owner)throw Error(A(290,e))}return e}function ea(e,t){throw e=Object.prototype.toString.call(t),Error(A(31,e==="[object Object]"?"object with keys {"+Object.keys(t).join(", ")+"}":e))}function Nu(e){var t=e._init;return t(e._payload)}function Yf(e){function t(p,h){if(e){var x=p.deletions;x===null?(p.deletions=[h],p.flags|=16):x.push(h)}}function r(p,h){if(!e)return null;for(;h!==null;)t(p,h),h=h.sibling;return null}function s(p,h){for(p=new Map;h!==null;)h.key!==null?p.set(h.key,h):p.set(h.index,h),h=h.sibling;return p}function n(p,h){return p=gr(p,h),p.index=0,p.sibling=null,p}function i(p,h,x){return p.index=x,e?(x=p.alternate,x!==null?(x=x.index,x<h?(p.flags|=2,h):x):(p.flags|=2,h)):(p.flags|=1048576,h)}function o(p){return e&&p.alternate===null&&(p.flags|=2),p}function l(p,h,x,b){return h===null||h.tag!==6?(h=co(x,p.mode,b),h.return=p,h):(h=n(h,x),h.return=p,h)}function c(p,h,x,b){var k=x.type;return k===Gr?d(p,h,x.props.children,b,x.key):h!==null&&(h.elementType===k||typeof k=="object"&&k!==null&&k.$$typeof===Zt&&Nu(k)===h.type)?(b=n(h,x.props),b.ref=Rs(p,h,x),b.return=p,b):(b=va(x.type,x.key,x.props,null,p.mode,b),b.ref=Rs(p,h,x),b.return=p,b)}function u(p,h,x,b){return h===null||h.tag!==4||h.stateNode.containerInfo!==x.containerInfo||h.stateNode.implementation!==x.implementation?(h=uo(x,p.mode,b),h.return=p,h):(h=n(h,x.children||[]),h.return=p,h)}function d(p,h,x,b,k){return h===null||h.tag!==7?(h=Lr(x,p.mode,b,k),h.return=p,h):(h=n(h,x),h.return=p,h)}function f(p,h,x){if(typeof h=="string"&&h!==""||typeof h=="number")return h=co(""+h,p.mode,x),h.return=p,h;if(typeof h=="object"&&h!==null){switch(h.$$typeof){case Bn:return x=va(h.type,h.key,h.props,null,p.mode,x),x.ref=Rs(p,null,h),x.return=p,x;case Yr:return h=uo(h,p.mode,x),h.return=p,h;case Zt:var b=h._init;return f(p,b(h._payload),x)}if(Bs(h)||Os(h))return h=Lr(h,p.mode,x,null),h.return=p,h;ea(p,h)}return null}function m(p,h,x,b){var k=h!==null?h.key:null;if(typeof x=="string"&&x!==""||typeof x=="number")return k!==null?null:l(p,h,""+x,b);if(typeof x=="object"&&x!==null){switch(x.$$typeof){case Bn:return x.key===k?c(p,h,x,b):null;case Yr:return x.key===k?u(p,h,x,b):null;case Zt:return k=x._init,m(p,h,k(x._payload),b)}if(Bs(x)||Os(x))return k!==null?null:d(p,h,x,b,null);ea(p,x)}return null}function v(p,h,x,b,k){if(typeof b=="string"&&b!==""||typeof b=="number")return p=p.get(x)||null,l(h,p,""+b,k);if(typeof b=="object"&&b!==null){switch(b.$$typeof){case Bn:return p=p.get(b.key===null?x:b.key)||null,c(h,p,b,k);case Yr:return p=p.get(b.key===null?x:b.key)||null,u(h,p,b,k);case Zt:var E=b._init;return v(p,h,x,E(b._payload),k)}if(Bs(b)||Os(b))return p=p.get(x)||null,d(h,p,b,k,null);ea(h,b)}return null}function g(p,h,x,b){for(var k=null,E=null,O=h,$=h=0,C=null;O!==null&&$<x.length;$++){O.index>$?(C=O,O=null):C=O.sibling;var D=m(p,O,x[$],b);if(D===null){O===null&&(O=C);break}e&&O&&D.alternate===null&&t(p,O),h=i(D,h,$),E===null?k=D:E.sibling=D,E=D,O=C}if($===x.length)return r(p,O),fe&&Sr(p,$),k;if(O===null){for(;$<x.length;$++)O=f(p,x[$],b),O!==null&&(h=i(O,h,$),E===null?k=O:E.sibling=O,E=O);return fe&&Sr(p,$),k}for(O=s(p,O);$<x.length;$++)C=v(O,p,$,x[$],b),C!==null&&(e&&C.alternate!==null&&O.delete(C.key===null?$:C.key),h=i(C,h,$),E===null?k=C:E.sibling=C,E=C);return e&&O.forEach(function(U){return t(p,U)}),fe&&Sr(p,$),k}function w(p,h,x,b){var k=Os(x);if(typeof k!="function")throw Error(A(150));if(x=k.call(x),x==null)throw Error(A(151));for(var E=k=null,O=h,$=h=0,C=null,D=x.next();O!==null&&!D.done;$++,D=x.next()){O.index>$?(C=O,O=null):C=O.sibling;var U=m(p,O,D.value,b);if(U===null){O===null&&(O=C);break}e&&O&&U.alternate===null&&t(p,O),h=i(U,h,$),E===null?k=U:E.sibling=U,E=U,O=C}if(D.done)return r(p,O),fe&&Sr(p,$),k;if(O===null){for(;!D.done;$++,D=x.next())D=f(p,D.value,b),D!==null&&(h=i(D,h,$),E===null?k=D:E.sibling=D,E=D);return fe&&Sr(p,$),k}for(O=s(p,O);!D.done;$++,D=x.next())D=v(O,p,$,D.value,b),D!==null&&(e&&D.alternate!==null&&O.delete(D.key===null?$:D.key),h=i(D,h,$),E===null?k=D:E.sibling=D,E=D);return e&&O.forEach(function(W){return t(p,W)}),fe&&Sr(p,$),k}function y(p,h,x,b){if(typeof x=="object"&&x!==null&&x.type===Gr&&x.key===null&&(x=x.props.children),typeof x=="object"&&x!==null){switch(x.$$typeof){case Bn:e:{for(var k=x.key,E=h;E!==null;){if(E.key===k){if(k=x.type,k===Gr){if(E.tag===7){r(p,E.sibling),h=n(E,x.props.children),h.return=p,p=h;break e}}else if(E.elementType===k||typeof k=="object"&&k!==null&&k.$$typeof===Zt&&Nu(k)===E.type){r(p,E.sibling),h=n(E,x.props),h.ref=Rs(p,E,x),h.return=p,p=h;break e}r(p,E);break}else t(p,E);E=E.sibling}x.type===Gr?(h=Lr(x.props.children,p.mode,b,x.key),h.return=p,p=h):(b=va(x.type,x.key,x.props,null,p.mode,b),b.ref=Rs(p,h,x),b.return=p,p=b)}return o(p);case Yr:e:{for(E=x.key;h!==null;){if(h.key===E)if(h.tag===4&&h.stateNode.containerInfo===x.containerInfo&&h.stateNode.implementation===x.implementation){r(p,h.sibling),h=n(h,x.children||[]),h.return=p,p=h;break e}else{r(p,h);break}else t(p,h);h=h.sibling}h=uo(x,p.mode,b),h.return=p,p=h}return o(p);case Zt:return E=x._init,y(p,h,E(x._payload),b)}if(Bs(x))return g(p,h,x,b);if(Os(x))return w(p,h,x,b);ea(p,x)}return typeof x=="string"&&x!==""||typeof x=="number"?(x=""+x,h!==null&&h.tag===6?(r(p,h.sibling),h=n(h,x),h.return=p,p=h):(r(p,h),h=co(x,p.mode,b),h.return=p,p=h),o(p)):r(p,h)}return y}var xs=Yf(!0),Gf=Yf(!1),_a=jr(null),Fa=null,ns=null,Yl=null;function Gl(){Yl=ns=Fa=null}function Jl(e){var t=_a.current;ce(_a),e._currentValue=t}function Yo(e,t,r){for(;e!==null;){var s=e.alternate;if((e.childLanes&t)!==t?(e.childLanes|=t,s!==null&&(s.childLanes|=t)):s!==null&&(s.childLanes&t)!==t&&(s.childLanes|=t),e===r)break;e=e.return}}function ds(e,t){Fa=e,Yl=ns=null,e=e.dependencies,e!==null&&e.firstContext!==null&&(e.lanes&t&&(Ke=!0),e.firstContext=null)}function ft(e){var t=e._currentValue;if(Yl!==e)if(e={context:e,memoizedValue:t,next:null},ns===null){if(Fa===null)throw Error(A(308));ns=e,Fa.dependencies={lanes:0,firstContext:e}}else ns=ns.next=e;return t}var Pr=null;function Xl(e){Pr===null?Pr=[e]:Pr.push(e)}function Jf(e,t,r,s){var n=t.interleaved;return n===null?(r.next=r,Xl(t)):(r.next=n.next,n.next=r),t.interleaved=r,Ht(e,s)}function Ht(e,t){e.lanes|=t;var r=e.alternate;for(r!==null&&(r.lanes|=t),r=e,e=e.return;e!==null;)e.childLanes|=t,r=e.alternate,r!==null&&(r.childLanes|=t),r=e,e=e.return;return r.tag===3?r.stateNode:null}var er=!1;function Zl(e){e.updateQueue={baseState:e.memoizedState,firstBaseUpdate:null,lastBaseUpdate:null,shared:{pending:null,interleaved:null,lanes:0},effects:null}}function Xf(e,t){e=e.updateQueue,t.updateQueue===e&&(t.updateQueue={baseState:e.baseState,firstBaseUpdate:e.firstBaseUpdate,lastBaseUpdate:e.lastBaseUpdate,shared:e.shared,effects:e.effects})}function zt(e,t){return{eventTime:e,lane:t,tag:0,payload:null,callback:null,next:null}}function mr(e,t,r){var s=e.updateQueue;if(s===null)return null;if(s=s.shared,Z&2){var n=s.pending;return n===null?t.next=t:(t.next=n.next,n.next=t),s.pending=t,Ht(e,r)}return n=s.interleaved,n===null?(t.next=t,Xl(s)):(t.next=n.next,n.next=t),s.interleaved=t,Ht(e,r)}function ma(e,t,r){if(t=t.updateQueue,t!==null&&(t=t.shared,(r&4194240)!==0)){var s=t.lanes;s&=e.pendingLanes,r|=s,t.lanes=r,Il(e,r)}}function ju(e,t){var r=e.updateQueue,s=e.alternate;if(s!==null&&(s=s.updateQueue,r===s)){var n=null,i=null;if(r=r.firstBaseUpdate,r!==null){do{var o={eventTime:r.eventTime,lane:r.lane,tag:r.tag,payload:r.payload,callback:r.callback,next:null};i===null?n=i=o:i=i.next=o,r=r.next}while(r!==null);i===null?n=i=t:i=i.next=t}else n=i=t;r={baseState:s.baseState,firstBaseUpdate:n,lastBaseUpdate:i,shared:s.shared,effects:s.effects},e.updateQueue=r;return}e=r.lastBaseUpdate,e===null?r.firstBaseUpdate=t:e.next=t,r.lastBaseUpdate=t}function Ia(e,t,r,s){var n=e.updateQueue;er=!1;var i=n.firstBaseUpdate,o=n.lastBaseUpdate,l=n.shared.pending;if(l!==null){n.shared.pending=null;var c=l,u=c.next;c.next=null,o===null?i=u:o.next=u,o=c;var d=e.alternate;d!==null&&(d=d.updateQueue,l=d.lastBaseUpdate,l!==o&&(l===null?d.firstBaseUpdate=u:l.next=u,d.lastBaseUpdate=c))}if(i!==null){var f=n.baseState;o=0,d=u=c=null,l=i;do{var m=l.lane,v=l.eventTime;if((s&m)===m){d!==null&&(d=d.next={eventTime:v,lane:0,tag:l.tag,payload:l.payload,callback:l.callback,next:null});e:{var g=e,w=l;switch(m=t,v=r,w.tag){case 1:if(g=w.payload,typeof g=="function"){f=g.call(v,f,m);break e}f=g;break e;case 3:g.flags=g.flags&-65537|128;case 0:if(g=w.payload,m=typeof g=="function"?g.call(v,f,m):g,m==null)break e;f=xe({},f,m);break e;case 2:er=!0}}l.callback!==null&&l.lane!==0&&(e.flags|=64,m=n.effects,m===null?n.effects=[l]:m.push(l))}else v={eventTime:v,lane:m,tag:l.tag,payload:l.payload,callback:l.callback,next:null},d===null?(u=d=v,c=f):d=d.next=v,o|=m;if(l=l.next,l===null){if(l=n.shared.pending,l===null)break;m=l,l=m.next,m.next=null,n.lastBaseUpdate=m,n.shared.pending=null}}while(1);if(d===null&&(c=f),n.baseState=c,n.firstBaseUpdate=u,n.lastBaseUpdate=d,t=n.shared.interleaved,t!==null){n=t;do o|=n.lane,n=n.next;while(n!==t)}else i===null&&(n.shared.lanes=0);Ir|=o,e.lanes=o,e.memoizedState=f}}function bu(e,t,r){if(e=t.effects,t.effects=null,e!==null)for(t=0;t<e.length;t++){var s=e[t],n=s.callback;if(n!==null){if(s.callback=null,s=r,typeof n!="function")throw Error(A(191,n));n.call(s)}}}var Tn={},At=jr(Tn),yn=jr(Tn),vn=jr(Tn);function Er(e){if(e===Tn)throw Error(A(174));return e}function ec(e,t){switch(ie(vn,t),ie(yn,e),ie(At,Tn),e=t.nodeType,e){case 9:case 11:t=(t=t.documentElement)?t.namespaceURI:Eo(null,"");break;default:e=e===8?t.parentNode:t,t=e.namespaceURI||null,e=e.tagName,t=Eo(t,e)}ce(At),ie(At,t)}function ys(){ce(At),ce(yn),ce(vn)}function Zf(e){Er(vn.current);var t=Er(At.current),r=Eo(t,e.type);t!==r&&(ie(yn,e),ie(At,r))}function tc(e){yn.current===e&&(ce(At),ce(yn))}var me=jr(0);function Ma(e){for(var t=e;t!==null;){if(t.tag===13){var r=t.memoizedState;if(r!==null&&(r=r.dehydrated,r===null||r.data==="$?"||r.data==="$!"))return t}else if(t.tag===19&&t.memoizedProps.revealOrder!==void 0){if(t.flags&128)return t}else if(t.child!==null){t.child.return=t,t=t.child;continue}if(t===e)break;for(;t.sibling===null;){if(t.return===null||t.return===e)return null;t=t.return}t.sibling.return=t.return,t=t.sibling}return null}var so=[];function rc(){for(var e=0;e<so.length;e++)so[e]._workInProgressVersionPrimary=null;so.length=0}var ha=Kt.ReactCurrentDispatcher,no=Kt.ReactCurrentBatchConfig,Fr=0,pe=null,Se=null,Pe=null,Ua=!1,Js=!1,wn=0,Bg=0;function Le(){throw Error(A(321))}function sc(e,t){if(t===null)return!1;for(var r=0;r<t.length&&r<e.length;r++)if(!jt(e[r],t[r]))return!1;return!0}function nc(e,t,r,s,n,i){if(Fr=i,pe=t,t.memoizedState=null,t.updateQueue=null,t.lanes=0,ha.current=e===null||e.memoizedState===null?Kg:Qg,e=r(s,n),Js){i=0;do{if(Js=!1,wn=0,25<=i)throw Error(A(301));i+=1,Pe=Se=null,t.updateQueue=null,ha.current=Yg,e=r(s,n)}while(Js)}if(ha.current=za,t=Se!==null&&Se.next!==null,Fr=0,Pe=Se=pe=null,Ua=!1,t)throw Error(A(300));return e}function ac(){var e=wn!==0;return wn=0,e}function St(){var e={memoizedState:null,baseState:null,baseQueue:null,queue:null,next:null};return Pe===null?pe.memoizedState=Pe=e:Pe=Pe.next=e,Pe}function mt(){if(Se===null){var e=pe.alternate;e=e!==null?e.memoizedState:null}else e=Se.next;var t=Pe===null?pe.memoizedState:Pe.next;if(t!==null)Pe=t,Se=e;else{if(e===null)throw Error(A(310));Se=e,e={memoizedState:Se.memoizedState,baseState:Se.baseState,baseQueue:Se.baseQueue,queue:Se.queue,next:null},Pe===null?pe.memoizedState=Pe=e:Pe=Pe.next=e}return Pe}function Nn(e,t){return typeof t=="function"?t(e):t}function ao(e){var t=mt(),r=t.queue;if(r===null)throw Error(A(311));r.lastRenderedReducer=e;var s=Se,n=s.baseQueue,i=r.pending;if(i!==null){if(n!==null){var o=n.next;n.next=i.next,i.next=o}s.baseQueue=n=i,r.pending=null}if(n!==null){i=n.next,s=s.baseState;var l=o=null,c=null,u=i;do{var d=u.lane;if((Fr&d)===d)c!==null&&(c=c.next={lane:0,action:u.action,hasEagerState:u.hasEagerState,eagerState:u.eagerState,next:null}),s=u.hasEagerState?u.eagerState:e(s,u.action);else{var f={lane:d,action:u.action,hasEagerState:u.hasEagerState,eagerState:u.eagerState,next:null};c===null?(l=c=f,o=s):c=c.next=f,pe.lanes|=d,Ir|=d}u=u.next}while(u!==null&&u!==i);c===null?o=s:c.next=l,jt(s,t.memoizedState)||(Ke=!0),t.memoizedState=s,t.baseState=o,t.baseQueue=c,r.lastRenderedState=s}if(e=r.interleaved,e!==null){n=e;do i=n.lane,pe.lanes|=i,Ir|=i,n=n.next;while(n!==e)}else n===null&&(r.lanes=0);return[t.memoizedState,r.dispatch]}function io(e){var t=mt(),r=t.queue;if(r===null)throw Error(A(311));r.lastRenderedReducer=e;var s=r.dispatch,n=r.pending,i=t.memoizedState;if(n!==null){r.pending=null;var o=n=n.next;do i=e(i,o.action),o=o.next;while(o!==n);jt(i,t.memoizedState)||(Ke=!0),t.memoizedState=i,t.baseQueue===null&&(t.baseState=i),r.lastRenderedState=i}return[i,s]}function em(){}function tm(e,t){var r=pe,s=mt(),n=t(),i=!jt(s.memoizedState,n);if(i&&(s.memoizedState=n,Ke=!0),s=s.queue,ic(nm.bind(null,r,s,e),[e]),s.getSnapshot!==t||i||Pe!==null&&Pe.memoizedState.tag&1){if(r.flags|=2048,jn(9,sm.bind(null,r,s,n,t),void 0,null),Ee===null)throw Error(A(349));Fr&30||rm(r,t,n)}return n}function rm(e,t,r){e.flags|=16384,e={getSnapshot:t,value:r},t=pe.updateQueue,t===null?(t={lastEffect:null,stores:null},pe.updateQueue=t,t.stores=[e]):(r=t.stores,r===null?t.stores=[e]:r.push(e))}function sm(e,t,r,s){t.value=r,t.getSnapshot=s,am(t)&&im(e)}function nm(e,t,r){return r(function(){am(t)&&im(e)})}function am(e){var t=e.getSnapshot;e=e.value;try{var r=t();return!jt(e,r)}catch{return!0}}function im(e){var t=Ht(e,1);t!==null&&Nt(t,e,1,-1)}function ku(e){var t=St();return typeof e=="function"&&(e=e()),t.memoizedState=t.baseState=e,e={pending:null,interleaved:null,lanes:0,dispatch:null,lastRenderedReducer:Nn,lastRenderedState:e},t.queue=e,e=e.dispatch=qg.bind(null,pe,e),[t.memoizedState,e]}function jn(e,t,r,s){return e={tag:e,create:t,destroy:r,deps:s,next:null},t=pe.updateQueue,t===null?(t={lastEffect:null,stores:null},pe.updateQueue=t,t.lastEffect=e.next=e):(r=t.lastEffect,r===null?t.lastEffect=e.next=e:(s=r.next,r.next=e,e.next=s,t.lastEffect=e)),e}function om(){return mt().memoizedState}function pa(e,t,r,s){var n=St();pe.flags|=e,n.memoizedState=jn(1|t,r,void 0,s===void 0?null:s)}function fi(e,t,r,s){var n=mt();s=s===void 0?null:s;var i=void 0;if(Se!==null){var o=Se.memoizedState;if(i=o.destroy,s!==null&&sc(s,o.deps)){n.memoizedState=jn(t,r,i,s);return}}pe.flags|=e,n.memoizedState=jn(1|t,r,i,s)}function Su(e,t){return pa(8390656,8,e,t)}function ic(e,t){return fi(2048,8,e,t)}function lm(e,t){return fi(4,2,e,t)}function cm(e,t){return fi(4,4,e,t)}function um(e,t){if(typeof t=="function")return e=e(),t(e),function(){t(null)};if(t!=null)return e=e(),t.current=e,function(){t.current=null}}function dm(e,t,r){return r=r!=null?r.concat([e]):null,fi(4,4,um.bind(null,t,e),r)}function oc(){}function fm(e,t){var r=mt();t=t===void 0?null:t;var s=r.memoizedState;return s!==null&&t!==null&&sc(t,s[1])?s[0]:(r.memoizedState=[e,t],e)}function mm(e,t){var r=mt();t=t===void 0?null:t;var s=r.memoizedState;return s!==null&&t!==null&&sc(t,s[1])?s[0]:(e=e(),r.memoizedState=[e,t],e)}function hm(e,t,r){return Fr&21?(jt(r,t)||(r=vf(),pe.lanes|=r,Ir|=r,e.baseState=!0),t):(e.baseState&&(e.baseState=!1,Ke=!0),e.memoizedState=r)}function Hg(e,t){var r=se;se=r!==0&&4>r?r:4,e(!0);var s=no.transition;no.transition={};try{e(!1),t()}finally{se=r,no.transition=s}}function pm(){return mt().memoizedState}function Wg(e,t,r){var s=pr(e);if(r={lane:s,action:r,hasEagerState:!1,eagerState:null,next:null},gm(e))xm(t,r);else if(r=Jf(e,t,r,s),r!==null){var n=Ve();Nt(r,e,s,n),ym(r,t,s)}}function qg(e,t,r){var s=pr(e),n={lane:s,action:r,hasEagerState:!1,eagerState:null,next:null};if(gm(e))xm(t,n);else{var i=e.alternate;if(e.lanes===0&&(i===null||i.lanes===0)&&(i=t.lastRenderedReducer,i!==null))try{var o=t.lastRenderedState,l=i(o,r);if(n.hasEagerState=!0,n.eagerState=l,jt(l,o)){var c=t.interleaved;c===null?(n.next=n,Xl(t)):(n.next=c.next,c.next=n),t.interleaved=n;return}}catch{}finally{}r=Jf(e,t,n,s),r!==null&&(n=Ve(),Nt(r,e,s,n),ym(r,t,s))}}function gm(e){var t=e.alternate;return e===pe||t!==null&&t===pe}function xm(e,t){Js=Ua=!0;var r=e.pending;r===null?t.next=t:(t.next=r.next,r.next=t),e.pending=t}function ym(e,t,r){if(r&4194240){var s=t.lanes;s&=e.pendingLanes,r|=s,t.lanes=r,Il(e,r)}}var za={readContext:ft,useCallback:Le,useContext:Le,useEffect:Le,useImperativeHandle:Le,useInsertionEffect:Le,useLayoutEffect:Le,useMemo:Le,useReducer:Le,useRef:Le,useState:Le,useDebugValue:Le,useDeferredValue:Le,useTransition:Le,useMutableSource:Le,useSyncExternalStore:Le,useId:Le,unstable_isNewReconciler:!1},Kg={readContext:ft,useCallback:function(e,t){return St().memoizedState=[e,t===void 0?null:t],e},useContext:ft,useEffect:Su,useImperativeHandle:function(e,t,r){return r=r!=null?r.concat([e]):null,pa(4194308,4,um.bind(null,t,e),r)},useLayoutEffect:function(e,t){return pa(4194308,4,e,t)},useInsertionEffect:function(e,t){return pa(4,2,e,t)},useMemo:function(e,t){var r=St();return t=t===void 0?null:t,e=e(),r.memoizedState=[e,t],e},useReducer:function(e,t,r){var s=St();return t=r!==void 0?r(t):t,s.memoizedState=s.baseState=t,e={pending:null,interleaved:null,lanes:0,dispatch:null,lastRenderedReducer:e,lastRenderedState:t},s.queue=e,e=e.dispatch=Wg.bind(null,pe,e),[s.memoizedState,e]},useRef:function(e){var t=St();return e={current:e},t.memoizedState=e},useState:ku,useDebugValue:oc,useDeferredValue:function(e){return St().memoizedState=e},useTransition:function(){var e=ku(!1),t=e[0];return e=Hg.bind(null,e[1]),St().memoizedState=e,[t,e]},useMutableSource:function(){},useSyncExternalStore:function(e,t,r){var s=pe,n=St();if(fe){if(r===void 0)throw Error(A(407));r=r()}else{if(r=t(),Ee===null)throw Error(A(349));Fr&30||rm(s,t,r)}n.memoizedState=r;var i={value:r,getSnapshot:t};return n.queue=i,Su(nm.bind(null,s,i,e),[e]),s.flags|=2048,jn(9,sm.bind(null,s,i,r,t),void 0,null),r},useId:function(){var e=St(),t=Ee.identifierPrefix;if(fe){var r=Ut,s=Mt;r=(s&~(1<<32-wt(s)-1)).toString(32)+r,t=":"+t+"R"+r,r=wn++,0<r&&(t+="H"+r.toString(32)),t+=":"}else r=Bg++,t=":"+t+"r"+r.toString(32)+":";return e.memoizedState=t},unstable_isNewReconciler:!1},Qg={readContext:ft,useCallback:fm,useContext:ft,useEffect:ic,useImperativeHandle:dm,useInsertionEffect:lm,useLayoutEffect:cm,useMemo:mm,useReducer:ao,useRef:om,useState:function(){return ao(Nn)},useDebugValue:oc,useDeferredValue:function(e){var t=mt();return hm(t,Se.memoizedState,e)},useTransition:function(){var e=ao(Nn)[0],t=mt().memoizedState;return[e,t]},useMutableSource:em,useSyncExternalStore:tm,useId:pm,unstable_isNewReconciler:!1},Yg={readContext:ft,useCallback:fm,useContext:ft,useEffect:ic,useImperativeHandle:dm,useInsertionEffect:lm,useLayoutEffect:cm,useMemo:mm,useReducer:io,useRef:om,useState:function(){return io(Nn)},useDebugValue:oc,useDeferredValue:function(e){var t=mt();return Se===null?t.memoizedState=e:hm(t,Se.memoizedState,e)},useTransition:function(){var e=io(Nn)[0],t=mt().memoizedState;return[e,t]},useMutableSource:em,useSyncExternalStore:tm,useId:pm,unstable_isNewReconciler:!1};function pt(e,t){if(e&&e.defaultProps){t=xe({},t),e=e.defaultProps;for(var r in e)t[r]===void 0&&(t[r]=e[r]);return t}return t}function Go(e,t,r,s){t=e.memoizedState,r=r(s,t),r=r==null?t:xe({},t,r),e.memoizedState=r,e.lanes===0&&(e.updateQueue.baseState=r)}var mi={isMounted:function(e){return(e=e._reactInternals)?Vr(e)===e:!1},enqueueSetState:function(e,t,r){e=e._reactInternals;var s=Ve(),n=pr(e),i=zt(s,n);i.payload=t,r!=null&&(i.callback=r),t=mr(e,i,n),t!==null&&(Nt(t,e,n,s),ma(t,e,n))},enqueueReplaceState:function(e,t,r){e=e._reactInternals;var s=Ve(),n=pr(e),i=zt(s,n);i.tag=1,i.payload=t,r!=null&&(i.callback=r),t=mr(e,i,n),t!==null&&(Nt(t,e,n,s),ma(t,e,n))},enqueueForceUpdate:function(e,t){e=e._reactInternals;var r=Ve(),s=pr(e),n=zt(r,s);n.tag=2,t!=null&&(n.callback=t),t=mr(e,n,s),t!==null&&(Nt(t,e,s,r),ma(t,e,s))}};function Cu(e,t,r,s,n,i,o){return e=e.stateNode,typeof e.shouldComponentUpdate=="function"?e.shouldComponentUpdate(s,i,o):t.prototype&&t.prototype.isPureReactComponent?!hn(r,s)||!hn(n,i):!0}function vm(e,t,r){var s=!1,n=vr,i=t.contextType;return typeof i=="object"&&i!==null?i=ft(i):(n=Ye(t)?Rr:Fe.current,s=t.contextTypes,i=(s=s!=null)?ps(e,n):vr),t=new t(r,i),e.memoizedState=t.state!==null&&t.state!==void 0?t.state:null,t.updater=mi,e.stateNode=t,t._reactInternals=e,s&&(e=e.stateNode,e.__reactInternalMemoizedUnmaskedChildContext=n,e.__reactInternalMemoizedMaskedChildContext=i),t}function $u(e,t,r,s){e=t.state,typeof t.componentWillReceiveProps=="function"&&t.componentWillReceiveProps(r,s),typeof t.UNSAFE_componentWillReceiveProps=="function"&&t.UNSAFE_componentWillReceiveProps(r,s),t.state!==e&&mi.enqueueReplaceState(t,t.state,null)}function Jo(e,t,r,s){var n=e.stateNode;n.props=r,n.state=e.memoizedState,n.refs={},Zl(e);var i=t.contextType;typeof i=="object"&&i!==null?n.context=ft(i):(i=Ye(t)?Rr:Fe.current,n.context=ps(e,i)),n.state=e.memoizedState,i=t.getDerivedStateFromProps,typeof i=="function"&&(Go(e,t,i,r),n.state=e.memoizedState),typeof t.getDerivedStateFromProps=="function"||typeof n.getSnapshotBeforeUpdate=="function"||typeof n.UNSAFE_componentWillMount!="function"&&typeof n.componentWillMount!="function"||(t=n.state,typeof n.componentWillMount=="function"&&n.componentWillMount(),typeof n.UNSAFE_componentWillMount=="function"&&n.UNSAFE_componentWillMount(),t!==n.state&&mi.enqueueReplaceState(n,n.state,null),Ia(e,r,n,s),n.state=e.memoizedState),typeof n.componentDidMount=="function"&&(e.flags|=4194308)}function vs(e,t){try{var r="",s=t;do r+=bp(s),s=s.return;while(s);var n=r}catch(i){n=`
Error generating stack: `+i.message+`
`+i.stack}return{value:e,source:t,stack:n,digest:null}}function oo(e,t,r){return{value:e,source:null,stack:r??null,digest:t??null}}function Xo(e,t){try{console.error(t.value)}catch(r){setTimeout(function(){throw r})}}var Gg=typeof WeakMap=="function"?WeakMap:Map;function wm(e,t,r){r=zt(-1,r),r.tag=3,r.payload={element:null};var s=t.value;return r.callback=function(){Ba||(Ba=!0,ll=s),Xo(e,t)},r}function Nm(e,t,r){r=zt(-1,r),r.tag=3;var s=e.type.getDerivedStateFromError;if(typeof s=="function"){var n=t.value;r.payload=function(){return s(n)},r.callback=function(){Xo(e,t)}}var i=e.stateNode;return i!==null&&typeof i.componentDidCatch=="function"&&(r.callback=function(){Xo(e,t),typeof s!="function"&&(hr===null?hr=new Set([this]):hr.add(this));var o=t.stack;this.componentDidCatch(t.value,{componentStack:o!==null?o:""})}),r}function Pu(e,t,r){var s=e.pingCache;if(s===null){s=e.pingCache=new Gg;var n=new Set;s.set(t,n)}else n=s.get(t),n===void 0&&(n=new Set,s.set(t,n));n.has(r)||(n.add(r),e=ux.bind(null,e,t,r),t.then(e,e))}function Eu(e){do{var t;if((t=e.tag===13)&&(t=e.memoizedState,t=t!==null?t.dehydrated!==null:!0),t)return e;e=e.return}while(e!==null);return null}function Ou(e,t,r,s,n){return e.mode&1?(e.flags|=65536,e.lanes=n,e):(e===t?e.flags|=65536:(e.flags|=128,r.flags|=131072,r.flags&=-52805,r.tag===1&&(r.alternate===null?r.tag=17:(t=zt(-1,1),t.tag=2,mr(r,t,1))),r.lanes|=1),e)}var Jg=Kt.ReactCurrentOwner,Ke=!1;function Me(e,t,r,s){t.child=e===null?Gf(t,null,r,s):xs(t,e.child,r,s)}function Au(e,t,r,s,n){r=r.render;var i=t.ref;return ds(t,n),s=nc(e,t,r,s,i,n),r=ac(),e!==null&&!Ke?(t.updateQueue=e.updateQueue,t.flags&=-2053,e.lanes&=~n,Wt(e,t,n)):(fe&&r&&ql(t),t.flags|=1,Me(e,t,s,n),t.child)}function Du(e,t,r,s,n){if(e===null){var i=r.type;return typeof i=="function"&&!pc(i)&&i.defaultProps===void 0&&r.compare===null&&r.defaultProps===void 0?(t.tag=15,t.type=i,jm(e,t,i,s,n)):(e=va(r.type,null,s,t,t.mode,n),e.ref=t.ref,e.return=t,t.child=e)}if(i=e.child,!(e.lanes&n)){var o=i.memoizedProps;if(r=r.compare,r=r!==null?r:hn,r(o,s)&&e.ref===t.ref)return Wt(e,t,n)}return t.flags|=1,e=gr(i,s),e.ref=t.ref,e.return=t,t.child=e}function jm(e,t,r,s,n){if(e!==null){var i=e.memoizedProps;if(hn(i,s)&&e.ref===t.ref)if(Ke=!1,t.pendingProps=s=i,(e.lanes&n)!==0)e.flags&131072&&(Ke=!0);else return t.lanes=e.lanes,Wt(e,t,n)}return Zo(e,t,r,s,n)}function bm(e,t,r){var s=t.pendingProps,n=s.children,i=e!==null?e.memoizedState:null;if(s.mode==="hidden")if(!(t.mode&1))t.memoizedState={baseLanes:0,cachePool:null,transitions:null},ie(is,Ze),Ze|=r;else{if(!(r&1073741824))return e=i!==null?i.baseLanes|r:r,t.lanes=t.childLanes=1073741824,t.memoizedState={baseLanes:e,cachePool:null,transitions:null},t.updateQueue=null,ie(is,Ze),Ze|=e,null;t.memoizedState={baseLanes:0,cachePool:null,transitions:null},s=i!==null?i.baseLanes:r,ie(is,Ze),Ze|=s}else i!==null?(s=i.baseLanes|r,t.memoizedState=null):s=r,ie(is,Ze),Ze|=s;return Me(e,t,n,r),t.child}function km(e,t){var r=t.ref;(e===null&&r!==null||e!==null&&e.ref!==r)&&(t.flags|=512,t.flags|=2097152)}function Zo(e,t,r,s,n){var i=Ye(r)?Rr:Fe.current;return i=ps(t,i),ds(t,n),r=nc(e,t,r,s,i,n),s=ac(),e!==null&&!Ke?(t.updateQueue=e.updateQueue,t.flags&=-2053,e.lanes&=~n,Wt(e,t,n)):(fe&&s&&ql(t),t.flags|=1,Me(e,t,r,n),t.child)}function Lu(e,t,r,s,n){if(Ye(r)){var i=!0;La(t)}else i=!1;if(ds(t,n),t.stateNode===null)ga(e,t),vm(t,r,s),Jo(t,r,s,n),s=!0;else if(e===null){var o=t.stateNode,l=t.memoizedProps;o.props=l;var c=o.context,u=r.contextType;typeof u=="object"&&u!==null?u=ft(u):(u=Ye(r)?Rr:Fe.current,u=ps(t,u));var d=r.getDerivedStateFromProps,f=typeof d=="function"||typeof o.getSnapshotBeforeUpdate=="function";f||typeof o.UNSAFE_componentWillReceiveProps!="function"&&typeof o.componentWillReceiveProps!="function"||(l!==s||c!==u)&&$u(t,o,s,u),er=!1;var m=t.memoizedState;o.state=m,Ia(t,s,o,n),c=t.memoizedState,l!==s||m!==c||Qe.current||er?(typeof d=="function"&&(Go(t,r,d,s),c=t.memoizedState),(l=er||Cu(t,r,l,s,m,c,u))?(f||typeof o.UNSAFE_componentWillMount!="function"&&typeof o.componentWillMount!="function"||(typeof o.componentWillMount=="function"&&o.componentWillMount(),typeof o.UNSAFE_componentWillMount=="function"&&o.UNSAFE_componentWillMount()),typeof o.componentDidMount=="function"&&(t.flags|=4194308)):(typeof o.componentDidMount=="function"&&(t.flags|=4194308),t.memoizedProps=s,t.memoizedState=c),o.props=s,o.state=c,o.context=u,s=l):(typeof o.componentDidMount=="function"&&(t.flags|=4194308),s=!1)}else{o=t.stateNode,Xf(e,t),l=t.memoizedProps,u=t.type===t.elementType?l:pt(t.type,l),o.props=u,f=t.pendingProps,m=o.context,c=r.contextType,typeof c=="object"&&c!==null?c=ft(c):(c=Ye(r)?Rr:Fe.current,c=ps(t,c));var v=r.getDerivedStateFromProps;(d=typeof v=="function"||typeof o.getSnapshotBeforeUpdate=="function")||typeof o.UNSAFE_componentWillReceiveProps!="function"&&typeof o.componentWillReceiveProps!="function"||(l!==f||m!==c)&&$u(t,o,s,c),er=!1,m=t.memoizedState,o.state=m,Ia(t,s,o,n);var g=t.memoizedState;l!==f||m!==g||Qe.current||er?(typeof v=="function"&&(Go(t,r,v,s),g=t.memoizedState),(u=er||Cu(t,r,u,s,m,g,c)||!1)?(d||typeof o.UNSAFE_componentWillUpdate!="function"&&typeof o.componentWillUpdate!="function"||(typeof o.componentWillUpdate=="function"&&o.componentWillUpdate(s,g,c),typeof o.UNSAFE_componentWillUpdate=="function"&&o.UNSAFE_componentWillUpdate(s,g,c)),typeof o.componentDidUpdate=="function"&&(t.flags|=4),typeof o.getSnapshotBeforeUpdate=="function"&&(t.flags|=1024)):(typeof o.componentDidUpdate!="function"||l===e.memoizedProps&&m===e.memoizedState||(t.flags|=4),typeof o.getSnapshotBeforeUpdate!="function"||l===e.memoizedProps&&m===e.memoizedState||(t.flags|=1024),t.memoizedProps=s,t.memoizedState=g),o.props=s,o.state=g,o.context=c,s=u):(typeof o.componentDidUpdate!="function"||l===e.memoizedProps&&m===e.memoizedState||(t.flags|=4),typeof o.getSnapshotBeforeUpdate!="function"||l===e.memoizedProps&&m===e.memoizedState||(t.flags|=1024),s=!1)}return el(e,t,r,s,i,n)}function el(e,t,r,s,n,i){km(e,t);var o=(t.flags&128)!==0;if(!s&&!o)return n&&yu(t,r,!1),Wt(e,t,i);s=t.stateNode,Jg.current=t;var l=o&&typeof r.getDerivedStateFromError!="function"?null:s.render();return t.flags|=1,e!==null&&o?(t.child=xs(t,e.child,null,i),t.child=xs(t,null,l,i)):Me(e,t,l,i),t.memoizedState=s.state,n&&yu(t,r,!0),t.child}function Sm(e){var t=e.stateNode;t.pendingContext?xu(e,t.pendingContext,t.pendingContext!==t.context):t.context&&xu(e,t.context,!1),ec(e,t.containerInfo)}function Tu(e,t,r,s,n){return gs(),Ql(n),t.flags|=256,Me(e,t,r,s),t.child}var tl={dehydrated:null,treeContext:null,retryLane:0};function rl(e){return{baseLanes:e,cachePool:null,transitions:null}}function Cm(e,t,r){var s=t.pendingProps,n=me.current,i=!1,o=(t.flags&128)!==0,l;if((l=o)||(l=e!==null&&e.memoizedState===null?!1:(n&2)!==0),l?(i=!0,t.flags&=-129):(e===null||e.memoizedState!==null)&&(n|=1),ie(me,n&1),e===null)return Qo(t),e=t.memoizedState,e!==null&&(e=e.dehydrated,e!==null)?(t.mode&1?e.data==="$!"?t.lanes=8:t.lanes=1073741824:t.lanes=1,null):(o=s.children,e=s.fallback,i?(s=t.mode,i=t.child,o={mode:"hidden",children:o},!(s&1)&&i!==null?(i.childLanes=0,i.pendingProps=o):i=gi(o,s,0,null),e=Lr(e,s,r,null),i.return=t,e.return=t,i.sibling=e,t.child=i,t.child.memoizedState=rl(r),t.memoizedState=tl,e):lc(t,o));if(n=e.memoizedState,n!==null&&(l=n.dehydrated,l!==null))return Xg(e,t,o,s,l,n,r);if(i){i=s.fallback,o=t.mode,n=e.child,l=n.sibling;var c={mode:"hidden",children:s.children};return!(o&1)&&t.child!==n?(s=t.child,s.childLanes=0,s.pendingProps=c,t.deletions=null):(s=gr(n,c),s.subtreeFlags=n.subtreeFlags&14680064),l!==null?i=gr(l,i):(i=Lr(i,o,r,null),i.flags|=2),i.return=t,s.return=t,s.sibling=i,t.child=s,s=i,i=t.child,o=e.child.memoizedState,o=o===null?rl(r):{baseLanes:o.baseLanes|r,cachePool:null,transitions:o.transitions},i.memoizedState=o,i.childLanes=e.childLanes&~r,t.memoizedState=tl,s}return i=e.child,e=i.sibling,s=gr(i,{mode:"visible",children:s.children}),!(t.mode&1)&&(s.lanes=r),s.return=t,s.sibling=null,e!==null&&(r=t.deletions,r===null?(t.deletions=[e],t.flags|=16):r.push(e)),t.child=s,t.memoizedState=null,s}function lc(e,t){return t=gi({mode:"visible",children:t},e.mode,0,null),t.return=e,e.child=t}function ta(e,t,r,s){return s!==null&&Ql(s),xs(t,e.child,null,r),e=lc(t,t.pendingProps.children),e.flags|=2,t.memoizedState=null,e}function Xg(e,t,r,s,n,i,o){if(r)return t.flags&256?(t.flags&=-257,s=oo(Error(A(422))),ta(e,t,o,s)):t.memoizedState!==null?(t.child=e.child,t.flags|=128,null):(i=s.fallback,n=t.mode,s=gi({mode:"visible",children:s.children},n,0,null),i=Lr(i,n,o,null),i.flags|=2,s.return=t,i.return=t,s.sibling=i,t.child=s,t.mode&1&&xs(t,e.child,null,o),t.child.memoizedState=rl(o),t.memoizedState=tl,i);if(!(t.mode&1))return ta(e,t,o,null);if(n.data==="$!"){if(s=n.nextSibling&&n.nextSibling.dataset,s)var l=s.dgst;return s=l,i=Error(A(419)),s=oo(i,s,void 0),ta(e,t,o,s)}if(l=(o&e.childLanes)!==0,Ke||l){if(s=Ee,s!==null){switch(o&-o){case 4:n=2;break;case 16:n=8;break;case 64:case 128:case 256:case 512:case 1024:case 2048:case 4096:case 8192:case 16384:case 32768:case 65536:case 131072:case 262144:case 524288:case 1048576:case 2097152:case 4194304:case 8388608:case 16777216:case 33554432:case 67108864:n=32;break;case 536870912:n=268435456;break;default:n=0}n=n&(s.suspendedLanes|o)?0:n,n!==0&&n!==i.retryLane&&(i.retryLane=n,Ht(e,n),Nt(s,e,n,-1))}return hc(),s=oo(Error(A(421))),ta(e,t,o,s)}return n.data==="$?"?(t.flags|=128,t.child=e.child,t=dx.bind(null,e),n._reactRetry=t,null):(e=i.treeContext,et=fr(n.nextSibling),tt=t,fe=!0,xt=null,e!==null&&(lt[ct++]=Mt,lt[ct++]=Ut,lt[ct++]=_r,Mt=e.id,Ut=e.overflow,_r=t),t=lc(t,s.children),t.flags|=4096,t)}function Ru(e,t,r){e.lanes|=t;var s=e.alternate;s!==null&&(s.lanes|=t),Yo(e.return,t,r)}function lo(e,t,r,s,n){var i=e.memoizedState;i===null?e.memoizedState={isBackwards:t,rendering:null,renderingStartTime:0,last:s,tail:r,tailMode:n}:(i.isBackwards=t,i.rendering=null,i.renderingStartTime=0,i.last=s,i.tail=r,i.tailMode=n)}function $m(e,t,r){var s=t.pendingProps,n=s.revealOrder,i=s.tail;if(Me(e,t,s.children,r),s=me.current,s&2)s=s&1|2,t.flags|=128;else{if(e!==null&&e.flags&128)e:for(e=t.child;e!==null;){if(e.tag===13)e.memoizedState!==null&&Ru(e,r,t);else if(e.tag===19)Ru(e,r,t);else if(e.child!==null){e.child.return=e,e=e.child;continue}if(e===t)break e;for(;e.sibling===null;){if(e.return===null||e.return===t)break e;e=e.return}e.sibling.return=e.return,e=e.sibling}s&=1}if(ie(me,s),!(t.mode&1))t.memoizedState=null;else switch(n){case"forwards":for(r=t.child,n=null;r!==null;)e=r.alternate,e!==null&&Ma(e)===null&&(n=r),r=r.sibling;r=n,r===null?(n=t.child,t.child=null):(n=r.sibling,r.sibling=null),lo(t,!1,n,r,i);break;case"backwards":for(r=null,n=t.child,t.child=null;n!==null;){if(e=n.alternate,e!==null&&Ma(e)===null){t.child=n;break}e=n.sibling,n.sibling=r,r=n,n=e}lo(t,!0,r,null,i);break;case"together":lo(t,!1,null,null,void 0);break;default:t.memoizedState=null}return t.child}function ga(e,t){!(t.mode&1)&&e!==null&&(e.alternate=null,t.alternate=null,t.flags|=2)}function Wt(e,t,r){if(e!==null&&(t.dependencies=e.dependencies),Ir|=t.lanes,!(r&t.childLanes))return null;if(e!==null&&t.child!==e.child)throw Error(A(153));if(t.child!==null){for(e=t.child,r=gr(e,e.pendingProps),t.child=r,r.return=t;e.sibling!==null;)e=e.sibling,r=r.sibling=gr(e,e.pendingProps),r.return=t;r.sibling=null}return t.child}function Zg(e,t,r){switch(t.tag){case 3:Sm(t),gs();break;case 5:Zf(t);break;case 1:Ye(t.type)&&La(t);break;case 4:ec(t,t.stateNode.containerInfo);break;case 10:var s=t.type._context,n=t.memoizedProps.value;ie(_a,s._currentValue),s._currentValue=n;break;case 13:if(s=t.memoizedState,s!==null)return s.dehydrated!==null?(ie(me,me.current&1),t.flags|=128,null):r&t.child.childLanes?Cm(e,t,r):(ie(me,me.current&1),e=Wt(e,t,r),e!==null?e.sibling:null);ie(me,me.current&1);break;case 19:if(s=(r&t.childLanes)!==0,e.flags&128){if(s)return $m(e,t,r);t.flags|=128}if(n=t.memoizedState,n!==null&&(n.rendering=null,n.tail=null,n.lastEffect=null),ie(me,me.current),s)break;return null;case 22:case 23:return t.lanes=0,bm(e,t,r)}return Wt(e,t,r)}var Pm,sl,Em,Om;Pm=function(e,t){for(var r=t.child;r!==null;){if(r.tag===5||r.tag===6)e.appendChild(r.stateNode);else if(r.tag!==4&&r.child!==null){r.child.return=r,r=r.child;continue}if(r===t)break;for(;r.sibling===null;){if(r.return===null||r.return===t)return;r=r.return}r.sibling.return=r.return,r=r.sibling}};sl=function(){};Em=function(e,t,r,s){var n=e.memoizedProps;if(n!==s){e=t.stateNode,Er(At.current);var i=null;switch(r){case"input":n=So(e,n),s=So(e,s),i=[];break;case"select":n=xe({},n,{value:void 0}),s=xe({},s,{value:void 0}),i=[];break;case"textarea":n=Po(e,n),s=Po(e,s),i=[];break;default:typeof n.onClick!="function"&&typeof s.onClick=="function"&&(e.onclick=Aa)}Oo(r,s);var o;r=null;for(u in n)if(!s.hasOwnProperty(u)&&n.hasOwnProperty(u)&&n[u]!=null)if(u==="style"){var l=n[u];for(o in l)l.hasOwnProperty(o)&&(r||(r={}),r[o]="")}else u!=="dangerouslySetInnerHTML"&&u!=="children"&&u!=="suppressContentEditableWarning"&&u!=="suppressHydrationWarning"&&u!=="autoFocus"&&(on.hasOwnProperty(u)?i||(i=[]):(i=i||[]).push(u,null));for(u in s){var c=s[u];if(l=n!=null?n[u]:void 0,s.hasOwnProperty(u)&&c!==l&&(c!=null||l!=null))if(u==="style")if(l){for(o in l)!l.hasOwnProperty(o)||c&&c.hasOwnProperty(o)||(r||(r={}),r[o]="");for(o in c)c.hasOwnProperty(o)&&l[o]!==c[o]&&(r||(r={}),r[o]=c[o])}else r||(i||(i=[]),i.push(u,r)),r=c;else u==="dangerouslySetInnerHTML"?(c=c?c.__html:void 0,l=l?l.__html:void 0,c!=null&&l!==c&&(i=i||[]).push(u,c)):u==="children"?typeof c!="string"&&typeof c!="number"||(i=i||[]).push(u,""+c):u!=="suppressContentEditableWarning"&&u!=="suppressHydrationWarning"&&(on.hasOwnProperty(u)?(c!=null&&u==="onScroll"&&le("scroll",e),i||l===c||(i=[])):(i=i||[]).push(u,c))}r&&(i=i||[]).push("style",r);var u=i;(t.updateQueue=u)&&(t.flags|=4)}};Om=function(e,t,r,s){r!==s&&(t.flags|=4)};function _s(e,t){if(!fe)switch(e.tailMode){case"hidden":t=e.tail;for(var r=null;t!==null;)t.alternate!==null&&(r=t),t=t.sibling;r===null?e.tail=null:r.sibling=null;break;case"collapsed":r=e.tail;for(var s=null;r!==null;)r.alternate!==null&&(s=r),r=r.sibling;s===null?t||e.tail===null?e.tail=null:e.tail.sibling=null:s.sibling=null}}function Te(e){var t=e.alternate!==null&&e.alternate.child===e.child,r=0,s=0;if(t)for(var n=e.child;n!==null;)r|=n.lanes|n.childLanes,s|=n.subtreeFlags&14680064,s|=n.flags&14680064,n.return=e,n=n.sibling;else for(n=e.child;n!==null;)r|=n.lanes|n.childLanes,s|=n.subtreeFlags,s|=n.flags,n.return=e,n=n.sibling;return e.subtreeFlags|=s,e.childLanes=r,t}function ex(e,t,r){var s=t.pendingProps;switch(Kl(t),t.tag){case 2:case 16:case 15:case 0:case 11:case 7:case 8:case 12:case 9:case 14:return Te(t),null;case 1:return Ye(t.type)&&Da(),Te(t),null;case 3:return s=t.stateNode,ys(),ce(Qe),ce(Fe),rc(),s.pendingContext&&(s.context=s.pendingContext,s.pendingContext=null),(e===null||e.child===null)&&(Zn(t)?t.flags|=4:e===null||e.memoizedState.isDehydrated&&!(t.flags&256)||(t.flags|=1024,xt!==null&&(dl(xt),xt=null))),sl(e,t),Te(t),null;case 5:tc(t);var n=Er(vn.current);if(r=t.type,e!==null&&t.stateNode!=null)Em(e,t,r,s,n),e.ref!==t.ref&&(t.flags|=512,t.flags|=2097152);else{if(!s){if(t.stateNode===null)throw Error(A(166));return Te(t),null}if(e=Er(At.current),Zn(t)){s=t.stateNode,r=t.type;var i=t.memoizedProps;switch(s[$t]=t,s[xn]=i,e=(t.mode&1)!==0,r){case"dialog":le("cancel",s),le("close",s);break;case"iframe":case"object":case"embed":le("load",s);break;case"video":case"audio":for(n=0;n<Ws.length;n++)le(Ws[n],s);break;case"source":le("error",s);break;case"img":case"image":case"link":le("error",s),le("load",s);break;case"details":le("toggle",s);break;case"input":Hc(s,i),le("invalid",s);break;case"select":s._wrapperState={wasMultiple:!!i.multiple},le("invalid",s);break;case"textarea":qc(s,i),le("invalid",s)}Oo(r,i),n=null;for(var o in i)if(i.hasOwnProperty(o)){var l=i[o];o==="children"?typeof l=="string"?s.textContent!==l&&(i.suppressHydrationWarning!==!0&&Xn(s.textContent,l,e),n=["children",l]):typeof l=="number"&&s.textContent!==""+l&&(i.suppressHydrationWarning!==!0&&Xn(s.textContent,l,e),n=["children",""+l]):on.hasOwnProperty(o)&&l!=null&&o==="onScroll"&&le("scroll",s)}switch(r){case"input":Hn(s),Wc(s,i,!0);break;case"textarea":Hn(s),Kc(s);break;case"select":case"option":break;default:typeof i.onClick=="function"&&(s.onclick=Aa)}s=n,t.updateQueue=s,s!==null&&(t.flags|=4)}else{o=n.nodeType===9?n:n.ownerDocument,e==="http://www.w3.org/1999/xhtml"&&(e=sf(r)),e==="http://www.w3.org/1999/xhtml"?r==="script"?(e=o.createElement("div"),e.innerHTML="<script><\/script>",e=e.removeChild(e.firstChild)):typeof s.is=="string"?e=o.createElement(r,{is:s.is}):(e=o.createElement(r),r==="select"&&(o=e,s.multiple?o.multiple=!0:s.size&&(o.size=s.size))):e=o.createElementNS(e,r),e[$t]=t,e[xn]=s,Pm(e,t,!1,!1),t.stateNode=e;e:{switch(o=Ao(r,s),r){case"dialog":le("cancel",e),le("close",e),n=s;break;case"iframe":case"object":case"embed":le("load",e),n=s;break;case"video":case"audio":for(n=0;n<Ws.length;n++)le(Ws[n],e);n=s;break;case"source":le("error",e),n=s;break;case"img":case"image":case"link":le("error",e),le("load",e),n=s;break;case"details":le("toggle",e),n=s;break;case"input":Hc(e,s),n=So(e,s),le("invalid",e);break;case"option":n=s;break;case"select":e._wrapperState={wasMultiple:!!s.multiple},n=xe({},s,{value:void 0}),le("invalid",e);break;case"textarea":qc(e,s),n=Po(e,s),le("invalid",e);break;default:n=s}Oo(r,n),l=n;for(i in l)if(l.hasOwnProperty(i)){var c=l[i];i==="style"?of(e,c):i==="dangerouslySetInnerHTML"?(c=c?c.__html:void 0,c!=null&&nf(e,c)):i==="children"?typeof c=="string"?(r!=="textarea"||c!=="")&&ln(e,c):typeof c=="number"&&ln(e,""+c):i!=="suppressContentEditableWarning"&&i!=="suppressHydrationWarning"&&i!=="autoFocus"&&(on.hasOwnProperty(i)?c!=null&&i==="onScroll"&&le("scroll",e):c!=null&&Dl(e,i,c,o))}switch(r){case"input":Hn(e),Wc(e,s,!1);break;case"textarea":Hn(e),Kc(e);break;case"option":s.value!=null&&e.setAttribute("value",""+yr(s.value));break;case"select":e.multiple=!!s.multiple,i=s.value,i!=null?os(e,!!s.multiple,i,!1):s.defaultValue!=null&&os(e,!!s.multiple,s.defaultValue,!0);break;default:typeof n.onClick=="function"&&(e.onclick=Aa)}switch(r){case"button":case"input":case"select":case"textarea":s=!!s.autoFocus;break e;case"img":s=!0;break e;default:s=!1}}s&&(t.flags|=4)}t.ref!==null&&(t.flags|=512,t.flags|=2097152)}return Te(t),null;case 6:if(e&&t.stateNode!=null)Om(e,t,e.memoizedProps,s);else{if(typeof s!="string"&&t.stateNode===null)throw Error(A(166));if(r=Er(vn.current),Er(At.current),Zn(t)){if(s=t.stateNode,r=t.memoizedProps,s[$t]=t,(i=s.nodeValue!==r)&&(e=tt,e!==null))switch(e.tag){case 3:Xn(s.nodeValue,r,(e.mode&1)!==0);break;case 5:e.memoizedProps.suppressHydrationWarning!==!0&&Xn(s.nodeValue,r,(e.mode&1)!==0)}i&&(t.flags|=4)}else s=(r.nodeType===9?r:r.ownerDocument).createTextNode(s),s[$t]=t,t.stateNode=s}return Te(t),null;case 13:if(ce(me),s=t.memoizedState,e===null||e.memoizedState!==null&&e.memoizedState.dehydrated!==null){if(fe&&et!==null&&t.mode&1&&!(t.flags&128))Qf(),gs(),t.flags|=98560,i=!1;else if(i=Zn(t),s!==null&&s.dehydrated!==null){if(e===null){if(!i)throw Error(A(318));if(i=t.memoizedState,i=i!==null?i.dehydrated:null,!i)throw Error(A(317));i[$t]=t}else gs(),!(t.flags&128)&&(t.memoizedState=null),t.flags|=4;Te(t),i=!1}else xt!==null&&(dl(xt),xt=null),i=!0;if(!i)return t.flags&65536?t:null}return t.flags&128?(t.lanes=r,t):(s=s!==null,s!==(e!==null&&e.memoizedState!==null)&&s&&(t.child.flags|=8192,t.mode&1&&(e===null||me.current&1?Ce===0&&(Ce=3):hc())),t.updateQueue!==null&&(t.flags|=4),Te(t),null);case 4:return ys(),sl(e,t),e===null&&pn(t.stateNode.containerInfo),Te(t),null;case 10:return Jl(t.type._context),Te(t),null;case 17:return Ye(t.type)&&Da(),Te(t),null;case 19:if(ce(me),i=t.memoizedState,i===null)return Te(t),null;if(s=(t.flags&128)!==0,o=i.rendering,o===null)if(s)_s(i,!1);else{if(Ce!==0||e!==null&&e.flags&128)for(e=t.child;e!==null;){if(o=Ma(e),o!==null){for(t.flags|=128,_s(i,!1),s=o.updateQueue,s!==null&&(t.updateQueue=s,t.flags|=4),t.subtreeFlags=0,s=r,r=t.child;r!==null;)i=r,e=s,i.flags&=14680066,o=i.alternate,o===null?(i.childLanes=0,i.lanes=e,i.child=null,i.subtreeFlags=0,i.memoizedProps=null,i.memoizedState=null,i.updateQueue=null,i.dependencies=null,i.stateNode=null):(i.childLanes=o.childLanes,i.lanes=o.lanes,i.child=o.child,i.subtreeFlags=0,i.deletions=null,i.memoizedProps=o.memoizedProps,i.memoizedState=o.memoizedState,i.updateQueue=o.updateQueue,i.type=o.type,e=o.dependencies,i.dependencies=e===null?null:{lanes:e.lanes,firstContext:e.firstContext}),r=r.sibling;return ie(me,me.current&1|2),t.child}e=e.sibling}i.tail!==null&&ve()>ws&&(t.flags|=128,s=!0,_s(i,!1),t.lanes=4194304)}else{if(!s)if(e=Ma(o),e!==null){if(t.flags|=128,s=!0,r=e.updateQueue,r!==null&&(t.updateQueue=r,t.flags|=4),_s(i,!0),i.tail===null&&i.tailMode==="hidden"&&!o.alternate&&!fe)return Te(t),null}else 2*ve()-i.renderingStartTime>ws&&r!==1073741824&&(t.flags|=128,s=!0,_s(i,!1),t.lanes=4194304);i.isBackwards?(o.sibling=t.child,t.child=o):(r=i.last,r!==null?r.sibling=o:t.child=o,i.last=o)}return i.tail!==null?(t=i.tail,i.rendering=t,i.tail=t.sibling,i.renderingStartTime=ve(),t.sibling=null,r=me.current,ie(me,s?r&1|2:r&1),t):(Te(t),null);case 22:case 23:return mc(),s=t.memoizedState!==null,e!==null&&e.memoizedState!==null!==s&&(t.flags|=8192),s&&t.mode&1?Ze&1073741824&&(Te(t),t.subtreeFlags&6&&(t.flags|=8192)):Te(t),null;case 24:return null;case 25:return null}throw Error(A(156,t.tag))}function tx(e,t){switch(Kl(t),t.tag){case 1:return Ye(t.type)&&Da(),e=t.flags,e&65536?(t.flags=e&-65537|128,t):null;case 3:return ys(),ce(Qe),ce(Fe),rc(),e=t.flags,e&65536&&!(e&128)?(t.flags=e&-65537|128,t):null;case 5:return tc(t),null;case 13:if(ce(me),e=t.memoizedState,e!==null&&e.dehydrated!==null){if(t.alternate===null)throw Error(A(340));gs()}return e=t.flags,e&65536?(t.flags=e&-65537|128,t):null;case 19:return ce(me),null;case 4:return ys(),null;case 10:return Jl(t.type._context),null;case 22:case 23:return mc(),null;case 24:return null;default:return null}}var ra=!1,_e=!1,rx=typeof WeakSet=="function"?WeakSet:Set,F=null;function as(e,t){var r=e.ref;if(r!==null)if(typeof r=="function")try{r(null)}catch(s){ye(e,t,s)}else r.current=null}function nl(e,t,r){try{r()}catch(s){ye(e,t,s)}}var _u=!1;function sx(e,t){if(zo=Pa,e=Rf(),Wl(e)){if("selectionStart"in e)var r={start:e.selectionStart,end:e.selectionEnd};else e:{r=(r=e.ownerDocument)&&r.defaultView||window;var s=r.getSelection&&r.getSelection();if(s&&s.rangeCount!==0){r=s.anchorNode;var n=s.anchorOffset,i=s.focusNode;s=s.focusOffset;try{r.nodeType,i.nodeType}catch{r=null;break e}var o=0,l=-1,c=-1,u=0,d=0,f=e,m=null;t:for(;;){for(var v;f!==r||n!==0&&f.nodeType!==3||(l=o+n),f!==i||s!==0&&f.nodeType!==3||(c=o+s),f.nodeType===3&&(o+=f.nodeValue.length),(v=f.firstChild)!==null;)m=f,f=v;for(;;){if(f===e)break t;if(m===r&&++u===n&&(l=o),m===i&&++d===s&&(c=o),(v=f.nextSibling)!==null)break;f=m,m=f.parentNode}f=v}r=l===-1||c===-1?null:{start:l,end:c}}else r=null}r=r||{start:0,end:0}}else r=null;for(Vo={focusedElem:e,selectionRange:r},Pa=!1,F=t;F!==null;)if(t=F,e=t.child,(t.subtreeFlags&1028)!==0&&e!==null)e.return=t,F=e;else for(;F!==null;){t=F;try{var g=t.alternate;if(t.flags&1024)switch(t.tag){case 0:case 11:case 15:break;case 1:if(g!==null){var w=g.memoizedProps,y=g.memoizedState,p=t.stateNode,h=p.getSnapshotBeforeUpdate(t.elementType===t.type?w:pt(t.type,w),y);p.__reactInternalSnapshotBeforeUpdate=h}break;case 3:var x=t.stateNode.containerInfo;x.nodeType===1?x.textContent="":x.nodeType===9&&x.documentElement&&x.removeChild(x.documentElement);break;case 5:case 6:case 4:case 17:break;default:throw Error(A(163))}}catch(b){ye(t,t.return,b)}if(e=t.sibling,e!==null){e.return=t.return,F=e;break}F=t.return}return g=_u,_u=!1,g}function Xs(e,t,r){var s=t.updateQueue;if(s=s!==null?s.lastEffect:null,s!==null){var n=s=s.next;do{if((n.tag&e)===e){var i=n.destroy;n.destroy=void 0,i!==void 0&&nl(t,r,i)}n=n.next}while(n!==s)}}function hi(e,t){if(t=t.updateQueue,t=t!==null?t.lastEffect:null,t!==null){var r=t=t.next;do{if((r.tag&e)===e){var s=r.create;r.destroy=s()}r=r.next}while(r!==t)}}function al(e){var t=e.ref;if(t!==null){var r=e.stateNode;switch(e.tag){case 5:e=r;break;default:e=r}typeof t=="function"?t(e):t.current=e}}function Am(e){var t=e.alternate;t!==null&&(e.alternate=null,Am(t)),e.child=null,e.deletions=null,e.sibling=null,e.tag===5&&(t=e.stateNode,t!==null&&(delete t[$t],delete t[xn],delete t[Wo],delete t[Mg],delete t[Ug])),e.stateNode=null,e.return=null,e.dependencies=null,e.memoizedProps=null,e.memoizedState=null,e.pendingProps=null,e.stateNode=null,e.updateQueue=null}function Dm(e){return e.tag===5||e.tag===3||e.tag===4}function Fu(e){e:for(;;){for(;e.sibling===null;){if(e.return===null||Dm(e.return))return null;e=e.return}for(e.sibling.return=e.return,e=e.sibling;e.tag!==5&&e.tag!==6&&e.tag!==18;){if(e.flags&2||e.child===null||e.tag===4)continue e;e.child.return=e,e=e.child}if(!(e.flags&2))return e.stateNode}}function il(e,t,r){var s=e.tag;if(s===5||s===6)e=e.stateNode,t?r.nodeType===8?r.parentNode.insertBefore(e,t):r.insertBefore(e,t):(r.nodeType===8?(t=r.parentNode,t.insertBefore(e,r)):(t=r,t.appendChild(e)),r=r._reactRootContainer,r!=null||t.onclick!==null||(t.onclick=Aa));else if(s!==4&&(e=e.child,e!==null))for(il(e,t,r),e=e.sibling;e!==null;)il(e,t,r),e=e.sibling}function ol(e,t,r){var s=e.tag;if(s===5||s===6)e=e.stateNode,t?r.insertBefore(e,t):r.appendChild(e);else if(s!==4&&(e=e.child,e!==null))for(ol(e,t,r),e=e.sibling;e!==null;)ol(e,t,r),e=e.sibling}var Oe=null,gt=!1;function Xt(e,t,r){for(r=r.child;r!==null;)Lm(e,t,r),r=r.sibling}function Lm(e,t,r){if(Ot&&typeof Ot.onCommitFiberUnmount=="function")try{Ot.onCommitFiberUnmount(ii,r)}catch{}switch(r.tag){case 5:_e||as(r,t);case 6:var s=Oe,n=gt;Oe=null,Xt(e,t,r),Oe=s,gt=n,Oe!==null&&(gt?(e=Oe,r=r.stateNode,e.nodeType===8?e.parentNode.removeChild(r):e.removeChild(r)):Oe.removeChild(r.stateNode));break;case 18:Oe!==null&&(gt?(e=Oe,r=r.stateNode,e.nodeType===8?to(e.parentNode,r):e.nodeType===1&&to(e,r),fn(e)):to(Oe,r.stateNode));break;case 4:s=Oe,n=gt,Oe=r.stateNode.containerInfo,gt=!0,Xt(e,t,r),Oe=s,gt=n;break;case 0:case 11:case 14:case 15:if(!_e&&(s=r.updateQueue,s!==null&&(s=s.lastEffect,s!==null))){n=s=s.next;do{var i=n,o=i.destroy;i=i.tag,o!==void 0&&(i&2||i&4)&&nl(r,t,o),n=n.next}while(n!==s)}Xt(e,t,r);break;case 1:if(!_e&&(as(r,t),s=r.stateNode,typeof s.componentWillUnmount=="function"))try{s.props=r.memoizedProps,s.state=r.memoizedState,s.componentWillUnmount()}catch(l){ye(r,t,l)}Xt(e,t,r);break;case 21:Xt(e,t,r);break;case 22:r.mode&1?(_e=(s=_e)||r.memoizedState!==null,Xt(e,t,r),_e=s):Xt(e,t,r);break;default:Xt(e,t,r)}}function Iu(e){var t=e.updateQueue;if(t!==null){e.updateQueue=null;var r=e.stateNode;r===null&&(r=e.stateNode=new rx),t.forEach(function(s){var n=fx.bind(null,e,s);r.has(s)||(r.add(s),s.then(n,n))})}}function ht(e,t){var r=t.deletions;if(r!==null)for(var s=0;s<r.length;s++){var n=r[s];try{var i=e,o=t,l=o;e:for(;l!==null;){switch(l.tag){case 5:Oe=l.stateNode,gt=!1;break e;case 3:Oe=l.stateNode.containerInfo,gt=!0;break e;case 4:Oe=l.stateNode.containerInfo,gt=!0;break e}l=l.return}if(Oe===null)throw Error(A(160));Lm(i,o,n),Oe=null,gt=!1;var c=n.alternate;c!==null&&(c.return=null),n.return=null}catch(u){ye(n,t,u)}}if(t.subtreeFlags&12854)for(t=t.child;t!==null;)Tm(t,e),t=t.sibling}function Tm(e,t){var r=e.alternate,s=e.flags;switch(e.tag){case 0:case 11:case 14:case 15:if(ht(t,e),kt(e),s&4){try{Xs(3,e,e.return),hi(3,e)}catch(w){ye(e,e.return,w)}try{Xs(5,e,e.return)}catch(w){ye(e,e.return,w)}}break;case 1:ht(t,e),kt(e),s&512&&r!==null&&as(r,r.return);break;case 5:if(ht(t,e),kt(e),s&512&&r!==null&&as(r,r.return),e.flags&32){var n=e.stateNode;try{ln(n,"")}catch(w){ye(e,e.return,w)}}if(s&4&&(n=e.stateNode,n!=null)){var i=e.memoizedProps,o=r!==null?r.memoizedProps:i,l=e.type,c=e.updateQueue;if(e.updateQueue=null,c!==null)try{l==="input"&&i.type==="radio"&&i.name!=null&&tf(n,i),Ao(l,o);var u=Ao(l,i);for(o=0;o<c.length;o+=2){var d=c[o],f=c[o+1];d==="style"?of(n,f):d==="dangerouslySetInnerHTML"?nf(n,f):d==="children"?ln(n,f):Dl(n,d,f,u)}switch(l){case"input":Co(n,i);break;case"textarea":rf(n,i);break;case"select":var m=n._wrapperState.wasMultiple;n._wrapperState.wasMultiple=!!i.multiple;var v=i.value;v!=null?os(n,!!i.multiple,v,!1):m!==!!i.multiple&&(i.defaultValue!=null?os(n,!!i.multiple,i.defaultValue,!0):os(n,!!i.multiple,i.multiple?[]:"",!1))}n[xn]=i}catch(w){ye(e,e.return,w)}}break;case 6:if(ht(t,e),kt(e),s&4){if(e.stateNode===null)throw Error(A(162));n=e.stateNode,i=e.memoizedProps;try{n.nodeValue=i}catch(w){ye(e,e.return,w)}}break;case 3:if(ht(t,e),kt(e),s&4&&r!==null&&r.memoizedState.isDehydrated)try{fn(t.containerInfo)}catch(w){ye(e,e.return,w)}break;case 4:ht(t,e),kt(e);break;case 13:ht(t,e),kt(e),n=e.child,n.flags&8192&&(i=n.memoizedState!==null,n.stateNode.isHidden=i,!i||n.alternate!==null&&n.alternate.memoizedState!==null||(dc=ve())),s&4&&Iu(e);break;case 22:if(d=r!==null&&r.memoizedState!==null,e.mode&1?(_e=(u=_e)||d,ht(t,e),_e=u):ht(t,e),kt(e),s&8192){if(u=e.memoizedState!==null,(e.stateNode.isHidden=u)&&!d&&e.mode&1)for(F=e,d=e.child;d!==null;){for(f=F=d;F!==null;){switch(m=F,v=m.child,m.tag){case 0:case 11:case 14:case 15:Xs(4,m,m.return);break;case 1:as(m,m.return);var g=m.stateNode;if(typeof g.componentWillUnmount=="function"){s=m,r=m.return;try{t=s,g.props=t.memoizedProps,g.state=t.memoizedState,g.componentWillUnmount()}catch(w){ye(s,r,w)}}break;case 5:as(m,m.return);break;case 22:if(m.memoizedState!==null){Uu(f);continue}}v!==null?(v.return=m,F=v):Uu(f)}d=d.sibling}e:for(d=null,f=e;;){if(f.tag===5){if(d===null){d=f;try{n=f.stateNode,u?(i=n.style,typeof i.setProperty=="function"?i.setProperty("display","none","important"):i.display="none"):(l=f.stateNode,c=f.memoizedProps.style,o=c!=null&&c.hasOwnProperty("display")?c.display:null,l.style.display=af("display",o))}catch(w){ye(e,e.return,w)}}}else if(f.tag===6){if(d===null)try{f.stateNode.nodeValue=u?"":f.memoizedProps}catch(w){ye(e,e.return,w)}}else if((f.tag!==22&&f.tag!==23||f.memoizedState===null||f===e)&&f.child!==null){f.child.return=f,f=f.child;continue}if(f===e)break e;for(;f.sibling===null;){if(f.return===null||f.return===e)break e;d===f&&(d=null),f=f.return}d===f&&(d=null),f.sibling.return=f.return,f=f.sibling}}break;case 19:ht(t,e),kt(e),s&4&&Iu(e);break;case 21:break;default:ht(t,e),kt(e)}}function kt(e){var t=e.flags;if(t&2){try{e:{for(var r=e.return;r!==null;){if(Dm(r)){var s=r;break e}r=r.return}throw Error(A(160))}switch(s.tag){case 5:var n=s.stateNode;s.flags&32&&(ln(n,""),s.flags&=-33);var i=Fu(e);ol(e,i,n);break;case 3:case 4:var o=s.stateNode.containerInfo,l=Fu(e);il(e,l,o);break;default:throw Error(A(161))}}catch(c){ye(e,e.return,c)}e.flags&=-3}t&4096&&(e.flags&=-4097)}function nx(e,t,r){F=e,Rm(e)}function Rm(e,t,r){for(var s=(e.mode&1)!==0;F!==null;){var n=F,i=n.child;if(n.tag===22&&s){var o=n.memoizedState!==null||ra;if(!o){var l=n.alternate,c=l!==null&&l.memoizedState!==null||_e;l=ra;var u=_e;if(ra=o,(_e=c)&&!u)for(F=n;F!==null;)o=F,c=o.child,o.tag===22&&o.memoizedState!==null?zu(n):c!==null?(c.return=o,F=c):zu(n);for(;i!==null;)F=i,Rm(i),i=i.sibling;F=n,ra=l,_e=u}Mu(e)}else n.subtreeFlags&8772&&i!==null?(i.return=n,F=i):Mu(e)}}function Mu(e){for(;F!==null;){var t=F;if(t.flags&8772){var r=t.alternate;try{if(t.flags&8772)switch(t.tag){case 0:case 11:case 15:_e||hi(5,t);break;case 1:var s=t.stateNode;if(t.flags&4&&!_e)if(r===null)s.componentDidMount();else{var n=t.elementType===t.type?r.memoizedProps:pt(t.type,r.memoizedProps);s.componentDidUpdate(n,r.memoizedState,s.__reactInternalSnapshotBeforeUpdate)}var i=t.updateQueue;i!==null&&bu(t,i,s);break;case 3:var o=t.updateQueue;if(o!==null){if(r=null,t.child!==null)switch(t.child.tag){case 5:r=t.child.stateNode;break;case 1:r=t.child.stateNode}bu(t,o,r)}break;case 5:var l=t.stateNode;if(r===null&&t.flags&4){r=l;var c=t.memoizedProps;switch(t.type){case"button":case"input":case"select":case"textarea":c.autoFocus&&r.focus();break;case"img":c.src&&(r.src=c.src)}}break;case 6:break;case 4:break;case 12:break;case 13:if(t.memoizedState===null){var u=t.alternate;if(u!==null){var d=u.memoizedState;if(d!==null){var f=d.dehydrated;f!==null&&fn(f)}}}break;case 19:case 17:case 21:case 22:case 23:case 25:break;default:throw Error(A(163))}_e||t.flags&512&&al(t)}catch(m){ye(t,t.return,m)}}if(t===e){F=null;break}if(r=t.sibling,r!==null){r.return=t.return,F=r;break}F=t.return}}function Uu(e){for(;F!==null;){var t=F;if(t===e){F=null;break}var r=t.sibling;if(r!==null){r.return=t.return,F=r;break}F=t.return}}function zu(e){for(;F!==null;){var t=F;try{switch(t.tag){case 0:case 11:case 15:var r=t.return;try{hi(4,t)}catch(c){ye(t,r,c)}break;case 1:var s=t.stateNode;if(typeof s.componentDidMount=="function"){var n=t.return;try{s.componentDidMount()}catch(c){ye(t,n,c)}}var i=t.return;try{al(t)}catch(c){ye(t,i,c)}break;case 5:var o=t.return;try{al(t)}catch(c){ye(t,o,c)}}}catch(c){ye(t,t.return,c)}if(t===e){F=null;break}var l=t.sibling;if(l!==null){l.return=t.return,F=l;break}F=t.return}}var ax=Math.ceil,Va=Kt.ReactCurrentDispatcher,cc=Kt.ReactCurrentOwner,dt=Kt.ReactCurrentBatchConfig,Z=0,Ee=null,ke=null,Ae=0,Ze=0,is=jr(0),Ce=0,bn=null,Ir=0,pi=0,uc=0,Zs=null,qe=null,dc=0,ws=1/0,Ft=null,Ba=!1,ll=null,hr=null,sa=!1,or=null,Ha=0,en=0,cl=null,xa=-1,ya=0;function Ve(){return Z&6?ve():xa!==-1?xa:xa=ve()}function pr(e){return e.mode&1?Z&2&&Ae!==0?Ae&-Ae:Vg.transition!==null?(ya===0&&(ya=vf()),ya):(e=se,e!==0||(e=window.event,e=e===void 0?16:Cf(e.type)),e):1}function Nt(e,t,r,s){if(50<en)throw en=0,cl=null,Error(A(185));An(e,r,s),(!(Z&2)||e!==Ee)&&(e===Ee&&(!(Z&2)&&(pi|=r),Ce===4&&rr(e,Ae)),Ge(e,s),r===1&&Z===0&&!(t.mode&1)&&(ws=ve()+500,di&&br()))}function Ge(e,t){var r=e.callbackNode;Vp(e,t);var s=$a(e,e===Ee?Ae:0);if(s===0)r!==null&&Gc(r),e.callbackNode=null,e.callbackPriority=0;else if(t=s&-s,e.callbackPriority!==t){if(r!=null&&Gc(r),t===1)e.tag===0?zg(Vu.bind(null,e)):Wf(Vu.bind(null,e)),Fg(function(){!(Z&6)&&br()}),r=null;else{switch(wf(s)){case 1:r=Fl;break;case 4:r=xf;break;case 16:r=Ca;break;case 536870912:r=yf;break;default:r=Ca}r=Bm(r,_m.bind(null,e))}e.callbackPriority=t,e.callbackNode=r}}function _m(e,t){if(xa=-1,ya=0,Z&6)throw Error(A(327));var r=e.callbackNode;if(fs()&&e.callbackNode!==r)return null;var s=$a(e,e===Ee?Ae:0);if(s===0)return null;if(s&30||s&e.expiredLanes||t)t=Wa(e,s);else{t=s;var n=Z;Z|=2;var i=Im();(Ee!==e||Ae!==t)&&(Ft=null,ws=ve()+500,Dr(e,t));do try{lx();break}catch(l){Fm(e,l)}while(1);Gl(),Va.current=i,Z=n,ke!==null?t=0:(Ee=null,Ae=0,t=Ce)}if(t!==0){if(t===2&&(n=_o(e),n!==0&&(s=n,t=ul(e,n))),t===1)throw r=bn,Dr(e,0),rr(e,s),Ge(e,ve()),r;if(t===6)rr(e,s);else{if(n=e.current.alternate,!(s&30)&&!ix(n)&&(t=Wa(e,s),t===2&&(i=_o(e),i!==0&&(s=i,t=ul(e,i))),t===1))throw r=bn,Dr(e,0),rr(e,s),Ge(e,ve()),r;switch(e.finishedWork=n,e.finishedLanes=s,t){case 0:case 1:throw Error(A(345));case 2:Cr(e,qe,Ft);break;case 3:if(rr(e,s),(s&130023424)===s&&(t=dc+500-ve(),10<t)){if($a(e,0)!==0)break;if(n=e.suspendedLanes,(n&s)!==s){Ve(),e.pingedLanes|=e.suspendedLanes&n;break}e.timeoutHandle=Ho(Cr.bind(null,e,qe,Ft),t);break}Cr(e,qe,Ft);break;case 4:if(rr(e,s),(s&4194240)===s)break;for(t=e.eventTimes,n=-1;0<s;){var o=31-wt(s);i=1<<o,o=t[o],o>n&&(n=o),s&=~i}if(s=n,s=ve()-s,s=(120>s?120:480>s?480:1080>s?1080:1920>s?1920:3e3>s?3e3:4320>s?4320:1960*ax(s/1960))-s,10<s){e.timeoutHandle=Ho(Cr.bind(null,e,qe,Ft),s);break}Cr(e,qe,Ft);break;case 5:Cr(e,qe,Ft);break;default:throw Error(A(329))}}}return Ge(e,ve()),e.callbackNode===r?_m.bind(null,e):null}function ul(e,t){var r=Zs;return e.current.memoizedState.isDehydrated&&(Dr(e,t).flags|=256),e=Wa(e,t),e!==2&&(t=qe,qe=r,t!==null&&dl(t)),e}function dl(e){qe===null?qe=e:qe.push.apply(qe,e)}function ix(e){for(var t=e;;){if(t.flags&16384){var r=t.updateQueue;if(r!==null&&(r=r.stores,r!==null))for(var s=0;s<r.length;s++){var n=r[s],i=n.getSnapshot;n=n.value;try{if(!jt(i(),n))return!1}catch{return!1}}}if(r=t.child,t.subtreeFlags&16384&&r!==null)r.return=t,t=r;else{if(t===e)break;for(;t.sibling===null;){if(t.return===null||t.return===e)return!0;t=t.return}t.sibling.return=t.return,t=t.sibling}}return!0}function rr(e,t){for(t&=~uc,t&=~pi,e.suspendedLanes|=t,e.pingedLanes&=~t,e=e.expirationTimes;0<t;){var r=31-wt(t),s=1<<r;e[r]=-1,t&=~s}}function Vu(e){if(Z&6)throw Error(A(327));fs();var t=$a(e,0);if(!(t&1))return Ge(e,ve()),null;var r=Wa(e,t);if(e.tag!==0&&r===2){var s=_o(e);s!==0&&(t=s,r=ul(e,s))}if(r===1)throw r=bn,Dr(e,0),rr(e,t),Ge(e,ve()),r;if(r===6)throw Error(A(345));return e.finishedWork=e.current.alternate,e.finishedLanes=t,Cr(e,qe,Ft),Ge(e,ve()),null}function fc(e,t){var r=Z;Z|=1;try{return e(t)}finally{Z=r,Z===0&&(ws=ve()+500,di&&br())}}function Mr(e){or!==null&&or.tag===0&&!(Z&6)&&fs();var t=Z;Z|=1;var r=dt.transition,s=se;try{if(dt.transition=null,se=1,e)return e()}finally{se=s,dt.transition=r,Z=t,!(Z&6)&&br()}}function mc(){Ze=is.current,ce(is)}function Dr(e,t){e.finishedWork=null,e.finishedLanes=0;var r=e.timeoutHandle;if(r!==-1&&(e.timeoutHandle=-1,_g(r)),ke!==null)for(r=ke.return;r!==null;){var s=r;switch(Kl(s),s.tag){case 1:s=s.type.childContextTypes,s!=null&&Da();break;case 3:ys(),ce(Qe),ce(Fe),rc();break;case 5:tc(s);break;case 4:ys();break;case 13:ce(me);break;case 19:ce(me);break;case 10:Jl(s.type._context);break;case 22:case 23:mc()}r=r.return}if(Ee=e,ke=e=gr(e.current,null),Ae=Ze=t,Ce=0,bn=null,uc=pi=Ir=0,qe=Zs=null,Pr!==null){for(t=0;t<Pr.length;t++)if(r=Pr[t],s=r.interleaved,s!==null){r.interleaved=null;var n=s.next,i=r.pending;if(i!==null){var o=i.next;i.next=n,s.next=o}r.pending=s}Pr=null}return e}function Fm(e,t){do{var r=ke;try{if(Gl(),ha.current=za,Ua){for(var s=pe.memoizedState;s!==null;){var n=s.queue;n!==null&&(n.pending=null),s=s.next}Ua=!1}if(Fr=0,Pe=Se=pe=null,Js=!1,wn=0,cc.current=null,r===null||r.return===null){Ce=1,bn=t,ke=null;break}e:{var i=e,o=r.return,l=r,c=t;if(t=Ae,l.flags|=32768,c!==null&&typeof c=="object"&&typeof c.then=="function"){var u=c,d=l,f=d.tag;if(!(d.mode&1)&&(f===0||f===11||f===15)){var m=d.alternate;m?(d.updateQueue=m.updateQueue,d.memoizedState=m.memoizedState,d.lanes=m.lanes):(d.updateQueue=null,d.memoizedState=null)}var v=Eu(o);if(v!==null){v.flags&=-257,Ou(v,o,l,i,t),v.mode&1&&Pu(i,u,t),t=v,c=u;var g=t.updateQueue;if(g===null){var w=new Set;w.add(c),t.updateQueue=w}else g.add(c);break e}else{if(!(t&1)){Pu(i,u,t),hc();break e}c=Error(A(426))}}else if(fe&&l.mode&1){var y=Eu(o);if(y!==null){!(y.flags&65536)&&(y.flags|=256),Ou(y,o,l,i,t),Ql(vs(c,l));break e}}i=c=vs(c,l),Ce!==4&&(Ce=2),Zs===null?Zs=[i]:Zs.push(i),i=o;do{switch(i.tag){case 3:i.flags|=65536,t&=-t,i.lanes|=t;var p=wm(i,c,t);ju(i,p);break e;case 1:l=c;var h=i.type,x=i.stateNode;if(!(i.flags&128)&&(typeof h.getDerivedStateFromError=="function"||x!==null&&typeof x.componentDidCatch=="function"&&(hr===null||!hr.has(x)))){i.flags|=65536,t&=-t,i.lanes|=t;var b=Nm(i,l,t);ju(i,b);break e}}i=i.return}while(i!==null)}Um(r)}catch(k){t=k,ke===r&&r!==null&&(ke=r=r.return);continue}break}while(1)}function Im(){var e=Va.current;return Va.current=za,e===null?za:e}function hc(){(Ce===0||Ce===3||Ce===2)&&(Ce=4),Ee===null||!(Ir&268435455)&&!(pi&268435455)||rr(Ee,Ae)}function Wa(e,t){var r=Z;Z|=2;var s=Im();(Ee!==e||Ae!==t)&&(Ft=null,Dr(e,t));do try{ox();break}catch(n){Fm(e,n)}while(1);if(Gl(),Z=r,Va.current=s,ke!==null)throw Error(A(261));return Ee=null,Ae=0,Ce}function ox(){for(;ke!==null;)Mm(ke)}function lx(){for(;ke!==null&&!Lp();)Mm(ke)}function Mm(e){var t=Vm(e.alternate,e,Ze);e.memoizedProps=e.pendingProps,t===null?Um(e):ke=t,cc.current=null}function Um(e){var t=e;do{var r=t.alternate;if(e=t.return,t.flags&32768){if(r=tx(r,t),r!==null){r.flags&=32767,ke=r;return}if(e!==null)e.flags|=32768,e.subtreeFlags=0,e.deletions=null;else{Ce=6,ke=null;return}}else if(r=ex(r,t,Ze),r!==null){ke=r;return}if(t=t.sibling,t!==null){ke=t;return}ke=t=e}while(t!==null);Ce===0&&(Ce=5)}function Cr(e,t,r){var s=se,n=dt.transition;try{dt.transition=null,se=1,cx(e,t,r,s)}finally{dt.transition=n,se=s}return null}function cx(e,t,r,s){do fs();while(or!==null);if(Z&6)throw Error(A(327));r=e.finishedWork;var n=e.finishedLanes;if(r===null)return null;if(e.finishedWork=null,e.finishedLanes=0,r===e.current)throw Error(A(177));e.callbackNode=null,e.callbackPriority=0;var i=r.lanes|r.childLanes;if(Bp(e,i),e===Ee&&(ke=Ee=null,Ae=0),!(r.subtreeFlags&2064)&&!(r.flags&2064)||sa||(sa=!0,Bm(Ca,function(){return fs(),null})),i=(r.flags&15990)!==0,r.subtreeFlags&15990||i){i=dt.transition,dt.transition=null;var o=se;se=1;var l=Z;Z|=4,cc.current=null,sx(e,r),Tm(r,e),Eg(Vo),Pa=!!zo,Vo=zo=null,e.current=r,nx(r),Tp(),Z=l,se=o,dt.transition=i}else e.current=r;if(sa&&(sa=!1,or=e,Ha=n),i=e.pendingLanes,i===0&&(hr=null),Fp(r.stateNode),Ge(e,ve()),t!==null)for(s=e.onRecoverableError,r=0;r<t.length;r++)n=t[r],s(n.value,{componentStack:n.stack,digest:n.digest});if(Ba)throw Ba=!1,e=ll,ll=null,e;return Ha&1&&e.tag!==0&&fs(),i=e.pendingLanes,i&1?e===cl?en++:(en=0,cl=e):en=0,br(),null}function fs(){if(or!==null){var e=wf(Ha),t=dt.transition,r=se;try{if(dt.transition=null,se=16>e?16:e,or===null)var s=!1;else{if(e=or,or=null,Ha=0,Z&6)throw Error(A(331));var n=Z;for(Z|=4,F=e.current;F!==null;){var i=F,o=i.child;if(F.flags&16){var l=i.deletions;if(l!==null){for(var c=0;c<l.length;c++){var u=l[c];for(F=u;F!==null;){var d=F;switch(d.tag){case 0:case 11:case 15:Xs(8,d,i)}var f=d.child;if(f!==null)f.return=d,F=f;else for(;F!==null;){d=F;var m=d.sibling,v=d.return;if(Am(d),d===u){F=null;break}if(m!==null){m.return=v,F=m;break}F=v}}}var g=i.alternate;if(g!==null){var w=g.child;if(w!==null){g.child=null;do{var y=w.sibling;w.sibling=null,w=y}while(w!==null)}}F=i}}if(i.subtreeFlags&2064&&o!==null)o.return=i,F=o;else e:for(;F!==null;){if(i=F,i.flags&2048)switch(i.tag){case 0:case 11:case 15:Xs(9,i,i.return)}var p=i.sibling;if(p!==null){p.return=i.return,F=p;break e}F=i.return}}var h=e.current;for(F=h;F!==null;){o=F;var x=o.child;if(o.subtreeFlags&2064&&x!==null)x.return=o,F=x;else e:for(o=h;F!==null;){if(l=F,l.flags&2048)try{switch(l.tag){case 0:case 11:case 15:hi(9,l)}}catch(k){ye(l,l.return,k)}if(l===o){F=null;break e}var b=l.sibling;if(b!==null){b.return=l.return,F=b;break e}F=l.return}}if(Z=n,br(),Ot&&typeof Ot.onPostCommitFiberRoot=="function")try{Ot.onPostCommitFiberRoot(ii,e)}catch{}s=!0}return s}finally{se=r,dt.transition=t}}return!1}function Bu(e,t,r){t=vs(r,t),t=wm(e,t,1),e=mr(e,t,1),t=Ve(),e!==null&&(An(e,1,t),Ge(e,t))}function ye(e,t,r){if(e.tag===3)Bu(e,e,r);else for(;t!==null;){if(t.tag===3){Bu(t,e,r);break}else if(t.tag===1){var s=t.stateNode;if(typeof t.type.getDerivedStateFromError=="function"||typeof s.componentDidCatch=="function"&&(hr===null||!hr.has(s))){e=vs(r,e),e=Nm(t,e,1),t=mr(t,e,1),e=Ve(),t!==null&&(An(t,1,e),Ge(t,e));break}}t=t.return}}function ux(e,t,r){var s=e.pingCache;s!==null&&s.delete(t),t=Ve(),e.pingedLanes|=e.suspendedLanes&r,Ee===e&&(Ae&r)===r&&(Ce===4||Ce===3&&(Ae&130023424)===Ae&&500>ve()-dc?Dr(e,0):uc|=r),Ge(e,t)}function zm(e,t){t===0&&(e.mode&1?(t=Kn,Kn<<=1,!(Kn&130023424)&&(Kn=4194304)):t=1);var r=Ve();e=Ht(e,t),e!==null&&(An(e,t,r),Ge(e,r))}function dx(e){var t=e.memoizedState,r=0;t!==null&&(r=t.retryLane),zm(e,r)}function fx(e,t){var r=0;switch(e.tag){case 13:var s=e.stateNode,n=e.memoizedState;n!==null&&(r=n.retryLane);break;case 19:s=e.stateNode;break;default:throw Error(A(314))}s!==null&&s.delete(t),zm(e,r)}var Vm;Vm=function(e,t,r){if(e!==null)if(e.memoizedProps!==t.pendingProps||Qe.current)Ke=!0;else{if(!(e.lanes&r)&&!(t.flags&128))return Ke=!1,Zg(e,t,r);Ke=!!(e.flags&131072)}else Ke=!1,fe&&t.flags&1048576&&qf(t,Ra,t.index);switch(t.lanes=0,t.tag){case 2:var s=t.type;ga(e,t),e=t.pendingProps;var n=ps(t,Fe.current);ds(t,r),n=nc(null,t,s,e,n,r);var i=ac();return t.flags|=1,typeof n=="object"&&n!==null&&typeof n.render=="function"&&n.$$typeof===void 0?(t.tag=1,t.memoizedState=null,t.updateQueue=null,Ye(s)?(i=!0,La(t)):i=!1,t.memoizedState=n.state!==null&&n.state!==void 0?n.state:null,Zl(t),n.updater=mi,t.stateNode=n,n._reactInternals=t,Jo(t,s,e,r),t=el(null,t,s,!0,i,r)):(t.tag=0,fe&&i&&ql(t),Me(null,t,n,r),t=t.child),t;case 16:s=t.elementType;e:{switch(ga(e,t),e=t.pendingProps,n=s._init,s=n(s._payload),t.type=s,n=t.tag=hx(s),e=pt(s,e),n){case 0:t=Zo(null,t,s,e,r);break e;case 1:t=Lu(null,t,s,e,r);break e;case 11:t=Au(null,t,s,e,r);break e;case 14:t=Du(null,t,s,pt(s.type,e),r);break e}throw Error(A(306,s,""))}return t;case 0:return s=t.type,n=t.pendingProps,n=t.elementType===s?n:pt(s,n),Zo(e,t,s,n,r);case 1:return s=t.type,n=t.pendingProps,n=t.elementType===s?n:pt(s,n),Lu(e,t,s,n,r);case 3:e:{if(Sm(t),e===null)throw Error(A(387));s=t.pendingProps,i=t.memoizedState,n=i.element,Xf(e,t),Ia(t,s,null,r);var o=t.memoizedState;if(s=o.element,i.isDehydrated)if(i={element:s,isDehydrated:!1,cache:o.cache,pendingSuspenseBoundaries:o.pendingSuspenseBoundaries,transitions:o.transitions},t.updateQueue.baseState=i,t.memoizedState=i,t.flags&256){n=vs(Error(A(423)),t),t=Tu(e,t,s,r,n);break e}else if(s!==n){n=vs(Error(A(424)),t),t=Tu(e,t,s,r,n);break e}else for(et=fr(t.stateNode.containerInfo.firstChild),tt=t,fe=!0,xt=null,r=Gf(t,null,s,r),t.child=r;r;)r.flags=r.flags&-3|4096,r=r.sibling;else{if(gs(),s===n){t=Wt(e,t,r);break e}Me(e,t,s,r)}t=t.child}return t;case 5:return Zf(t),e===null&&Qo(t),s=t.type,n=t.pendingProps,i=e!==null?e.memoizedProps:null,o=n.children,Bo(s,n)?o=null:i!==null&&Bo(s,i)&&(t.flags|=32),km(e,t),Me(e,t,o,r),t.child;case 6:return e===null&&Qo(t),null;case 13:return Cm(e,t,r);case 4:return ec(t,t.stateNode.containerInfo),s=t.pendingProps,e===null?t.child=xs(t,null,s,r):Me(e,t,s,r),t.child;case 11:return s=t.type,n=t.pendingProps,n=t.elementType===s?n:pt(s,n),Au(e,t,s,n,r);case 7:return Me(e,t,t.pendingProps,r),t.child;case 8:return Me(e,t,t.pendingProps.children,r),t.child;case 12:return Me(e,t,t.pendingProps.children,r),t.child;case 10:e:{if(s=t.type._context,n=t.pendingProps,i=t.memoizedProps,o=n.value,ie(_a,s._currentValue),s._currentValue=o,i!==null)if(jt(i.value,o)){if(i.children===n.children&&!Qe.current){t=Wt(e,t,r);break e}}else for(i=t.child,i!==null&&(i.return=t);i!==null;){var l=i.dependencies;if(l!==null){o=i.child;for(var c=l.firstContext;c!==null;){if(c.context===s){if(i.tag===1){c=zt(-1,r&-r),c.tag=2;var u=i.updateQueue;if(u!==null){u=u.shared;var d=u.pending;d===null?c.next=c:(c.next=d.next,d.next=c),u.pending=c}}i.lanes|=r,c=i.alternate,c!==null&&(c.lanes|=r),Yo(i.return,r,t),l.lanes|=r;break}c=c.next}}else if(i.tag===10)o=i.type===t.type?null:i.child;else if(i.tag===18){if(o=i.return,o===null)throw Error(A(341));o.lanes|=r,l=o.alternate,l!==null&&(l.lanes|=r),Yo(o,r,t),o=i.sibling}else o=i.child;if(o!==null)o.return=i;else for(o=i;o!==null;){if(o===t){o=null;break}if(i=o.sibling,i!==null){i.return=o.return,o=i;break}o=o.return}i=o}Me(e,t,n.children,r),t=t.child}return t;case 9:return n=t.type,s=t.pendingProps.children,ds(t,r),n=ft(n),s=s(n),t.flags|=1,Me(e,t,s,r),t.child;case 14:return s=t.type,n=pt(s,t.pendingProps),n=pt(s.type,n),Du(e,t,s,n,r);case 15:return jm(e,t,t.type,t.pendingProps,r);case 17:return s=t.type,n=t.pendingProps,n=t.elementType===s?n:pt(s,n),ga(e,t),t.tag=1,Ye(s)?(e=!0,La(t)):e=!1,ds(t,r),vm(t,s,n),Jo(t,s,n,r),el(null,t,s,!0,e,r);case 19:return $m(e,t,r);case 22:return bm(e,t,r)}throw Error(A(156,t.tag))};function Bm(e,t){return gf(e,t)}function mx(e,t,r,s){this.tag=e,this.key=r,this.sibling=this.child=this.return=this.stateNode=this.type=this.elementType=null,this.index=0,this.ref=null,this.pendingProps=t,this.dependencies=this.memoizedState=this.updateQueue=this.memoizedProps=null,this.mode=s,this.subtreeFlags=this.flags=0,this.deletions=null,this.childLanes=this.lanes=0,this.alternate=null}function ut(e,t,r,s){return new mx(e,t,r,s)}function pc(e){return e=e.prototype,!(!e||!e.isReactComponent)}function hx(e){if(typeof e=="function")return pc(e)?1:0;if(e!=null){if(e=e.$$typeof,e===Tl)return 11;if(e===Rl)return 14}return 2}function gr(e,t){var r=e.alternate;return r===null?(r=ut(e.tag,t,e.key,e.mode),r.elementType=e.elementType,r.type=e.type,r.stateNode=e.stateNode,r.alternate=e,e.alternate=r):(r.pendingProps=t,r.type=e.type,r.flags=0,r.subtreeFlags=0,r.deletions=null),r.flags=e.flags&14680064,r.childLanes=e.childLanes,r.lanes=e.lanes,r.child=e.child,r.memoizedProps=e.memoizedProps,r.memoizedState=e.memoizedState,r.updateQueue=e.updateQueue,t=e.dependencies,r.dependencies=t===null?null:{lanes:t.lanes,firstContext:t.firstContext},r.sibling=e.sibling,r.index=e.index,r.ref=e.ref,r}function va(e,t,r,s,n,i){var o=2;if(s=e,typeof e=="function")pc(e)&&(o=1);else if(typeof e=="string")o=5;else e:switch(e){case Gr:return Lr(r.children,n,i,t);case Ll:o=8,n|=8;break;case No:return e=ut(12,r,t,n|2),e.elementType=No,e.lanes=i,e;case jo:return e=ut(13,r,t,n),e.elementType=jo,e.lanes=i,e;case bo:return e=ut(19,r,t,n),e.elementType=bo,e.lanes=i,e;case Xd:return gi(r,n,i,t);default:if(typeof e=="object"&&e!==null)switch(e.$$typeof){case Gd:o=10;break e;case Jd:o=9;break e;case Tl:o=11;break e;case Rl:o=14;break e;case Zt:o=16,s=null;break e}throw Error(A(130,e==null?e:typeof e,""))}return t=ut(o,r,t,n),t.elementType=e,t.type=s,t.lanes=i,t}function Lr(e,t,r,s){return e=ut(7,e,s,t),e.lanes=r,e}function gi(e,t,r,s){return e=ut(22,e,s,t),e.elementType=Xd,e.lanes=r,e.stateNode={isHidden:!1},e}function co(e,t,r){return e=ut(6,e,null,t),e.lanes=r,e}function uo(e,t,r){return t=ut(4,e.children!==null?e.children:[],e.key,t),t.lanes=r,t.stateNode={containerInfo:e.containerInfo,pendingChildren:null,implementation:e.implementation},t}function px(e,t,r,s,n){this.tag=t,this.containerInfo=e,this.finishedWork=this.pingCache=this.current=this.pendingChildren=null,this.timeoutHandle=-1,this.callbackNode=this.pendingContext=this.context=null,this.callbackPriority=0,this.eventTimes=Hi(0),this.expirationTimes=Hi(-1),this.entangledLanes=this.finishedLanes=this.mutableReadLanes=this.expiredLanes=this.pingedLanes=this.suspendedLanes=this.pendingLanes=0,this.entanglements=Hi(0),this.identifierPrefix=s,this.onRecoverableError=n,this.mutableSourceEagerHydrationData=null}function gc(e,t,r,s,n,i,o,l,c){return e=new px(e,t,r,l,c),t===1?(t=1,i===!0&&(t|=8)):t=0,i=ut(3,null,null,t),e.current=i,i.stateNode=e,i.memoizedState={element:s,isDehydrated:r,cache:null,transitions:null,pendingSuspenseBoundaries:null},Zl(i),e}function gx(e,t,r){var s=3<arguments.length&&arguments[3]!==void 0?arguments[3]:null;return{$$typeof:Yr,key:s==null?null:""+s,children:e,containerInfo:t,implementation:r}}function Hm(e){if(!e)return vr;e=e._reactInternals;e:{if(Vr(e)!==e||e.tag!==1)throw Error(A(170));var t=e;do{switch(t.tag){case 3:t=t.stateNode.context;break e;case 1:if(Ye(t.type)){t=t.stateNode.__reactInternalMemoizedMergedChildContext;break e}}t=t.return}while(t!==null);throw Error(A(171))}if(e.tag===1){var r=e.type;if(Ye(r))return Hf(e,r,t)}return t}function Wm(e,t,r,s,n,i,o,l,c){return e=gc(r,s,!0,e,n,i,o,l,c),e.context=Hm(null),r=e.current,s=Ve(),n=pr(r),i=zt(s,n),i.callback=t??null,mr(r,i,n),e.current.lanes=n,An(e,n,s),Ge(e,s),e}function xi(e,t,r,s){var n=t.current,i=Ve(),o=pr(n);return r=Hm(r),t.context===null?t.context=r:t.pendingContext=r,t=zt(i,o),t.payload={element:e},s=s===void 0?null:s,s!==null&&(t.callback=s),e=mr(n,t,o),e!==null&&(Nt(e,n,o,i),ma(e,n,o)),o}function qa(e){if(e=e.current,!e.child)return null;switch(e.child.tag){case 5:return e.child.stateNode;default:return e.child.stateNode}}function Hu(e,t){if(e=e.memoizedState,e!==null&&e.dehydrated!==null){var r=e.retryLane;e.retryLane=r!==0&&r<t?r:t}}function xc(e,t){Hu(e,t),(e=e.alternate)&&Hu(e,t)}function xx(){return null}var qm=typeof reportError=="function"?reportError:function(e){console.error(e)};function yc(e){this._internalRoot=e}yi.prototype.render=yc.prototype.render=function(e){var t=this._internalRoot;if(t===null)throw Error(A(409));xi(e,t,null,null)};yi.prototype.unmount=yc.prototype.unmount=function(){var e=this._internalRoot;if(e!==null){this._internalRoot=null;var t=e.containerInfo;Mr(function(){xi(null,e,null,null)}),t[Bt]=null}};function yi(e){this._internalRoot=e}yi.prototype.unstable_scheduleHydration=function(e){if(e){var t=bf();e={blockedOn:null,target:e,priority:t};for(var r=0;r<tr.length&&t!==0&&t<tr[r].priority;r++);tr.splice(r,0,e),r===0&&Sf(e)}};function vc(e){return!(!e||e.nodeType!==1&&e.nodeType!==9&&e.nodeType!==11)}function vi(e){return!(!e||e.nodeType!==1&&e.nodeType!==9&&e.nodeType!==11&&(e.nodeType!==8||e.nodeValue!==" react-mount-point-unstable "))}function Wu(){}function yx(e,t,r,s,n){if(n){if(typeof s=="function"){var i=s;s=function(){var u=qa(o);i.call(u)}}var o=Wm(t,s,e,0,null,!1,!1,"",Wu);return e._reactRootContainer=o,e[Bt]=o.current,pn(e.nodeType===8?e.parentNode:e),Mr(),o}for(;n=e.lastChild;)e.removeChild(n);if(typeof s=="function"){var l=s;s=function(){var u=qa(c);l.call(u)}}var c=gc(e,0,!1,null,null,!1,!1,"",Wu);return e._reactRootContainer=c,e[Bt]=c.current,pn(e.nodeType===8?e.parentNode:e),Mr(function(){xi(t,c,r,s)}),c}function wi(e,t,r,s,n){var i=r._reactRootContainer;if(i){var o=i;if(typeof n=="function"){var l=n;n=function(){var c=qa(o);l.call(c)}}xi(t,o,e,n)}else o=yx(r,t,e,n,s);return qa(o)}Nf=function(e){switch(e.tag){case 3:var t=e.stateNode;if(t.current.memoizedState.isDehydrated){var r=Hs(t.pendingLanes);r!==0&&(Il(t,r|1),Ge(t,ve()),!(Z&6)&&(ws=ve()+500,br()))}break;case 13:Mr(function(){var s=Ht(e,1);if(s!==null){var n=Ve();Nt(s,e,1,n)}}),xc(e,1)}};Ml=function(e){if(e.tag===13){var t=Ht(e,134217728);if(t!==null){var r=Ve();Nt(t,e,134217728,r)}xc(e,134217728)}};jf=function(e){if(e.tag===13){var t=pr(e),r=Ht(e,t);if(r!==null){var s=Ve();Nt(r,e,t,s)}xc(e,t)}};bf=function(){return se};kf=function(e,t){var r=se;try{return se=e,t()}finally{se=r}};Lo=function(e,t,r){switch(t){case"input":if(Co(e,r),t=r.name,r.type==="radio"&&t!=null){for(r=e;r.parentNode;)r=r.parentNode;for(r=r.querySelectorAll("input[name="+JSON.stringify(""+t)+'][type="radio"]'),t=0;t<r.length;t++){var s=r[t];if(s!==e&&s.form===e.form){var n=ui(s);if(!n)throw Error(A(90));ef(s),Co(s,n)}}}break;case"textarea":rf(e,r);break;case"select":t=r.value,t!=null&&os(e,!!r.multiple,t,!1)}};uf=fc;df=Mr;var vx={usingClientEntryPoint:!1,Events:[Ln,es,ui,lf,cf,fc]},Fs={findFiberByHostInstance:$r,bundleType:0,version:"18.3.1",rendererPackageName:"react-dom"},wx={bundleType:Fs.bundleType,version:Fs.version,rendererPackageName:Fs.rendererPackageName,rendererConfig:Fs.rendererConfig,overrideHookState:null,overrideHookStateDeletePath:null,overrideHookStateRenamePath:null,overrideProps:null,overridePropsDeletePath:null,overridePropsRenamePath:null,setErrorHandler:null,setSuspenseHandler:null,scheduleUpdate:null,currentDispatcherRef:Kt.ReactCurrentDispatcher,findHostInstanceByFiber:function(e){return e=hf(e),e===null?null:e.stateNode},findFiberByHostInstance:Fs.findFiberByHostInstance||xx,findHostInstancesForRefresh:null,scheduleRefresh:null,scheduleRoot:null,setRefreshHandler:null,getCurrentFiber:null,reconcilerVersion:"18.3.1-next-f1338f8080-20240426"};if(typeof __REACT_DEVTOOLS_GLOBAL_HOOK__<"u"){var na=__REACT_DEVTOOLS_GLOBAL_HOOK__;if(!na.isDisabled&&na.supportsFiber)try{ii=na.inject(wx),Ot=na}catch{}}st.__SECRET_INTERNALS_DO_NOT_USE_OR_YOU_WILL_BE_FIRED=vx;st.createPortal=function(e,t){var r=2<arguments.length&&arguments[2]!==void 0?arguments[2]:null;if(!vc(t))throw Error(A(200));return gx(e,t,null,r)};st.createRoot=function(e,t){if(!vc(e))throw Error(A(299));var r=!1,s="",n=qm;return t!=null&&(t.unstable_strictMode===!0&&(r=!0),t.identifierPrefix!==void 0&&(s=t.identifierPrefix),t.onRecoverableError!==void 0&&(n=t.onRecoverableError)),t=gc(e,1,!1,null,null,r,!1,s,n),e[Bt]=t.current,pn(e.nodeType===8?e.parentNode:e),new yc(t)};st.findDOMNode=function(e){if(e==null)return null;if(e.nodeType===1)return e;var t=e._reactInternals;if(t===void 0)throw typeof e.render=="function"?Error(A(188)):(e=Object.keys(e).join(","),Error(A(268,e)));return e=hf(t),e=e===null?null:e.stateNode,e};st.flushSync=function(e){return Mr(e)};st.hydrate=function(e,t,r){if(!vi(t))throw Error(A(200));return wi(null,e,t,!0,r)};st.hydrateRoot=function(e,t,r){if(!vc(e))throw Error(A(405));var s=r!=null&&r.hydratedSources||null,n=!1,i="",o=qm;if(r!=null&&(r.unstable_strictMode===!0&&(n=!0),r.identifierPrefix!==void 0&&(i=r.identifierPrefix),r.onRecoverableError!==void 0&&(o=r.onRecoverableError)),t=Wm(t,null,e,1,r??null,n,!1,i,o),e[Bt]=t.current,pn(e),s)for(e=0;e<s.length;e++)r=s[e],n=r._getVersion,n=n(r._source),t.mutableSourceEagerHydrationData==null?t.mutableSourceEagerHydrationData=[r,n]:t.mutableSourceEagerHydrationData.push(r,n);return new yi(t)};st.render=function(e,t,r){if(!vi(t))throw Error(A(200));return wi(null,e,t,!1,r)};st.unmountComponentAtNode=function(e){if(!vi(e))throw Error(A(40));return e._reactRootContainer?(Mr(function(){wi(null,null,e,!1,function(){e._reactRootContainer=null,e[Bt]=null})}),!0):!1};st.unstable_batchedUpdates=fc;st.unstable_renderSubtreeIntoContainer=function(e,t,r,s){if(!vi(r))throw Error(A(200));if(e==null||e._reactInternals===void 0)throw Error(A(38));return wi(e,t,r,!1,s)};st.version="18.3.1-next-f1338f8080-20240426";function Km(){if(!(typeof __REACT_DEVTOOLS_GLOBAL_HOOK__>"u"||typeof __REACT_DEVTOOLS_GLOBAL_HOOK__.checkDCE!="function"))try{__REACT_DEVTOOLS_GLOBAL_HOOK__.checkDCE(Km)}catch(e){console.error(e)}}Km(),qd.exports=st;var Nx=qd.exports,qu=Nx;vo.createRoot=qu.createRoot,vo.hydrateRoot=qu.hydrateRoot;/**
 * @remix-run/router v1.23.0
 *
 * Copyright (c) Remix Software Inc.
 *
 * This source code is licensed under the MIT license found in the
 * LICENSE.md file in the root directory of this source tree.
 *
 * @license MIT
 */function kn(){return kn=Object.assign?Object.assign.bind():function(e){for(var t=1;t<arguments.length;t++){var r=arguments[t];for(var s in r)Object.prototype.hasOwnProperty.call(r,s)&&(e[s]=r[s])}return e},kn.apply(this,arguments)}var lr;(function(e){e.Pop="POP",e.Push="PUSH",e.Replace="REPLACE"})(lr||(lr={}));const Ku="popstate";function jx(e){e===void 0&&(e={});function t(s,n){let{pathname:i,search:o,hash:l}=s.location;return fl("",{pathname:i,search:o,hash:l},n.state&&n.state.usr||null,n.state&&n.state.key||"default")}function r(s,n){return typeof n=="string"?n:Ka(n)}return kx(t,r,null,e)}function ge(e,t){if(e===!1||e===null||typeof e>"u")throw new Error(t)}function Qm(e,t){if(!e){typeof console<"u"&&console.warn(t);try{throw new Error(t)}catch{}}}function bx(){return Math.random().toString(36).substr(2,8)}function Qu(e,t){return{usr:e.state,key:e.key,idx:t}}function fl(e,t,r,s){return r===void 0&&(r=null),kn({pathname:typeof e=="string"?e:e.pathname,search:"",hash:""},typeof t=="string"?Cs(t):t,{state:r,key:t&&t.key||s||bx()})}function Ka(e){let{pathname:t="/",search:r="",hash:s=""}=e;return r&&r!=="?"&&(t+=r.charAt(0)==="?"?r:"?"+r),s&&s!=="#"&&(t+=s.charAt(0)==="#"?s:"#"+s),t}function Cs(e){let t={};if(e){let r=e.indexOf("#");r>=0&&(t.hash=e.substr(r),e=e.substr(0,r));let s=e.indexOf("?");s>=0&&(t.search=e.substr(s),e=e.substr(0,s)),e&&(t.pathname=e)}return t}function kx(e,t,r,s){s===void 0&&(s={});let{window:n=document.defaultView,v5Compat:i=!1}=s,o=n.history,l=lr.Pop,c=null,u=d();u==null&&(u=0,o.replaceState(kn({},o.state,{idx:u}),""));function d(){return(o.state||{idx:null}).idx}function f(){l=lr.Pop;let y=d(),p=y==null?null:y-u;u=y,c&&c({action:l,location:w.location,delta:p})}function m(y,p){l=lr.Push;let h=fl(w.location,y,p);r&&r(h,y),u=d()+1;let x=Qu(h,u),b=w.createHref(h);try{o.pushState(x,"",b)}catch(k){if(k instanceof DOMException&&k.name==="DataCloneError")throw k;n.location.assign(b)}i&&c&&c({action:l,location:w.location,delta:1})}function v(y,p){l=lr.Replace;let h=fl(w.location,y,p);r&&r(h,y),u=d();let x=Qu(h,u),b=w.createHref(h);o.replaceState(x,"",b),i&&c&&c({action:l,location:w.location,delta:0})}function g(y){let p=n.location.origin!=="null"?n.location.origin:n.location.href,h=typeof y=="string"?y:Ka(y);return h=h.replace(/ $/,"%20"),ge(p,"No window.location.(origin|href) available to create URL for href: "+h),new URL(h,p)}let w={get action(){return l},get location(){return e(n,o)},listen(y){if(c)throw new Error("A history only accepts one active listener");return n.addEventListener(Ku,f),c=y,()=>{n.removeEventListener(Ku,f),c=null}},createHref(y){return t(n,y)},createURL:g,encodeLocation(y){let p=g(y);return{pathname:p.pathname,search:p.search,hash:p.hash}},push:m,replace:v,go(y){return o.go(y)}};return w}var Yu;(function(e){e.data="data",e.deferred="deferred",e.redirect="redirect",e.error="error"})(Yu||(Yu={}));function Sx(e,t,r){return r===void 0&&(r="/"),Cx(e,t,r,!1)}function Cx(e,t,r,s){let n=typeof t=="string"?Cs(t):t,i=Ns(n.pathname||"/",r);if(i==null)return null;let o=Ym(e);$x(o);let l=null;for(let c=0;l==null&&c<o.length;++c){let u=Ix(i);l=_x(o[c],u,s)}return l}function Ym(e,t,r,s){t===void 0&&(t=[]),r===void 0&&(r=[]),s===void 0&&(s="");let n=(i,o,l)=>{let c={relativePath:l===void 0?i.path||"":l,caseSensitive:i.caseSensitive===!0,childrenIndex:o,route:i};c.relativePath.startsWith("/")&&(ge(c.relativePath.startsWith(s),'Absolute route path "'+c.relativePath+'" nested under path '+('"'+s+'" is not valid. An absolute child route path ')+"must start with the combined path of all its parent routes."),c.relativePath=c.relativePath.slice(s.length));let u=xr([s,c.relativePath]),d=r.concat(c);i.children&&i.children.length>0&&(ge(i.index!==!0,"Index routes must not have child routes. Please remove "+('all child routes from route path "'+u+'".')),Ym(i.children,t,d,u)),!(i.path==null&&!i.index)&&t.push({path:u,score:Tx(u,i.index),routesMeta:d})};return e.forEach((i,o)=>{var l;if(i.path===""||!((l=i.path)!=null&&l.includes("?")))n(i,o);else for(let c of Gm(i.path))n(i,o,c)}),t}function Gm(e){let t=e.split("/");if(t.length===0)return[];let[r,...s]=t,n=r.endsWith("?"),i=r.replace(/\?$/,"");if(s.length===0)return n?[i,""]:[i];let o=Gm(s.join("/")),l=[];return l.push(...o.map(c=>c===""?i:[i,c].join("/"))),n&&l.push(...o),l.map(c=>e.startsWith("/")&&c===""?"/":c)}function $x(e){e.sort((t,r)=>t.score!==r.score?r.score-t.score:Rx(t.routesMeta.map(s=>s.childrenIndex),r.routesMeta.map(s=>s.childrenIndex)))}const Px=/^:[\w-]+$/,Ex=3,Ox=2,Ax=1,Dx=10,Lx=-2,Gu=e=>e==="*";function Tx(e,t){let r=e.split("/"),s=r.length;return r.some(Gu)&&(s+=Lx),t&&(s+=Ox),r.filter(n=>!Gu(n)).reduce((n,i)=>n+(Px.test(i)?Ex:i===""?Ax:Dx),s)}function Rx(e,t){return e.length===t.length&&e.slice(0,-1).every((s,n)=>s===t[n])?e[e.length-1]-t[t.length-1]:0}function _x(e,t,r){r===void 0&&(r=!1);let{routesMeta:s}=e,n={},i="/",o=[];for(let l=0;l<s.length;++l){let c=s[l],u=l===s.length-1,d=i==="/"?t:t.slice(i.length)||"/",f=Qa({path:c.relativePath,caseSensitive:c.caseSensitive,end:u},d),m=c.route;if(!f&&u&&r&&!s[s.length-1].route.index&&(f=Qa({path:c.relativePath,caseSensitive:c.caseSensitive,end:!1},d)),!f)return null;Object.assign(n,f.params),o.push({params:n,pathname:xr([i,f.pathname]),pathnameBase:Vx(xr([i,f.pathnameBase])),route:m}),f.pathnameBase!=="/"&&(i=xr([i,f.pathnameBase]))}return o}function Qa(e,t){typeof e=="string"&&(e={path:e,caseSensitive:!1,end:!0});let[r,s]=Fx(e.path,e.caseSensitive,e.end),n=t.match(r);if(!n)return null;let i=n[0],o=i.replace(/(.)\/+$/,"$1"),l=n.slice(1);return{params:s.reduce((u,d,f)=>{let{paramName:m,isOptional:v}=d;if(m==="*"){let w=l[f]||"";o=i.slice(0,i.length-w.length).replace(/(.)\/+$/,"$1")}const g=l[f];return v&&!g?u[m]=void 0:u[m]=(g||"").replace(/%2F/g,"/"),u},{}),pathname:i,pathnameBase:o,pattern:e}}function Fx(e,t,r){t===void 0&&(t=!1),r===void 0&&(r=!0),Qm(e==="*"||!e.endsWith("*")||e.endsWith("/*"),'Route path "'+e+'" will be treated as if it were '+('"'+e.replace(/\*$/,"/*")+'" because the `*` character must ')+"always follow a `/` in the pattern. To get rid of this warning, "+('please change the route path to "'+e.replace(/\*$/,"/*")+'".'));let s=[],n="^"+e.replace(/\/*\*?$/,"").replace(/^\/*/,"/").replace(/[\\.*+^${}|()[\]]/g,"\\$&").replace(/\/:([\w-]+)(\?)?/g,(o,l,c)=>(s.push({paramName:l,isOptional:c!=null}),c?"/?([^\\/]+)?":"/([^\\/]+)"));return e.endsWith("*")?(s.push({paramName:"*"}),n+=e==="*"||e==="/*"?"(.*)$":"(?:\\/(.+)|\\/*)$"):r?n+="\\/*$":e!==""&&e!=="/"&&(n+="(?:(?=\\/|$))"),[new RegExp(n,t?void 0:"i"),s]}function Ix(e){try{return e.split("/").map(t=>decodeURIComponent(t).replace(/\//g,"%2F")).join("/")}catch(t){return Qm(!1,'The URL path "'+e+'" could not be decoded because it is is a malformed URL segment. This is probably due to a bad percent '+("encoding ("+t+").")),e}}function Ns(e,t){if(t==="/")return e;if(!e.toLowerCase().startsWith(t.toLowerCase()))return null;let r=t.endsWith("/")?t.length-1:t.length,s=e.charAt(r);return s&&s!=="/"?null:e.slice(r)||"/"}function Mx(e,t){t===void 0&&(t="/");let{pathname:r,search:s="",hash:n=""}=typeof e=="string"?Cs(e):e;return{pathname:r?r.startsWith("/")?r:Ux(r,t):t,search:Bx(s),hash:Hx(n)}}function Ux(e,t){let r=t.replace(/\/+$/,"").split("/");return e.split("/").forEach(n=>{n===".."?r.length>1&&r.pop():n!=="."&&r.push(n)}),r.length>1?r.join("/"):"/"}function fo(e,t,r,s){return"Cannot include a '"+e+"' character in a manually specified "+("`to."+t+"` field ["+JSON.stringify(s)+"].  Please separate it out to the ")+("`to."+r+"` field. Alternatively you may provide the full path as ")+'a string in <Link to="..."> and the router will parse it for you.'}function zx(e){return e.filter((t,r)=>r===0||t.route.path&&t.route.path.length>0)}function wc(e,t){let r=zx(e);return t?r.map((s,n)=>n===r.length-1?s.pathname:s.pathnameBase):r.map(s=>s.pathnameBase)}function Nc(e,t,r,s){s===void 0&&(s=!1);let n;typeof e=="string"?n=Cs(e):(n=kn({},e),ge(!n.pathname||!n.pathname.includes("?"),fo("?","pathname","search",n)),ge(!n.pathname||!n.pathname.includes("#"),fo("#","pathname","hash",n)),ge(!n.search||!n.search.includes("#"),fo("#","search","hash",n)));let i=e===""||n.pathname==="",o=i?"/":n.pathname,l;if(o==null)l=r;else{let f=t.length-1;if(!s&&o.startsWith("..")){let m=o.split("/");for(;m[0]==="..";)m.shift(),f-=1;n.pathname=m.join("/")}l=f>=0?t[f]:"/"}let c=Mx(n,l),u=o&&o!=="/"&&o.endsWith("/"),d=(i||o===".")&&r.endsWith("/");return!c.pathname.endsWith("/")&&(u||d)&&(c.pathname+="/"),c}const xr=e=>e.join("/").replace(/\/\/+/g,"/"),Vx=e=>e.replace(/\/+$/,"").replace(/^\/*/,"/"),Bx=e=>!e||e==="?"?"":e.startsWith("?")?e:"?"+e,Hx=e=>!e||e==="#"?"":e.startsWith("#")?e:"#"+e;function Wx(e){return e!=null&&typeof e.status=="number"&&typeof e.statusText=="string"&&typeof e.internal=="boolean"&&"data"in e}const Jm=["post","put","patch","delete"];new Set(Jm);const qx=["get",...Jm];new Set(qx);/**
 * React Router v6.30.1
 *
 * Copyright (c) Remix Software Inc.
 *
 * This source code is licensed under the MIT license found in the
 * LICENSE.md file in the root directory of this source tree.
 *
 * @license MIT
 */function Sn(){return Sn=Object.assign?Object.assign.bind():function(e){for(var t=1;t<arguments.length;t++){var r=arguments[t];for(var s in r)Object.prototype.hasOwnProperty.call(r,s)&&(e[s]=r[s])}return e},Sn.apply(this,arguments)}const Ni=j.createContext(null),Xm=j.createContext(null),Qt=j.createContext(null),ji=j.createContext(null),Yt=j.createContext({outlet:null,matches:[],isDataRoute:!1}),Zm=j.createContext(null);function Kx(e,t){let{relative:r}=t===void 0?{}:t;$s()||ge(!1);let{basename:s,navigator:n}=j.useContext(Qt),{hash:i,pathname:o,search:l}=bi(e,{relative:r}),c=o;return s!=="/"&&(c=o==="/"?s:xr([s,o])),n.createHref({pathname:c,search:l,hash:i})}function $s(){return j.useContext(ji)!=null}function bt(){return $s()||ge(!1),j.useContext(ji).location}function eh(e){j.useContext(Qt).static||j.useLayoutEffect(e)}function Dt(){let{isDataRoute:e}=j.useContext(Yt);return e?o0():Qx()}function Qx(){$s()||ge(!1);let e=j.useContext(Ni),{basename:t,future:r,navigator:s}=j.useContext(Qt),{matches:n}=j.useContext(Yt),{pathname:i}=bt(),o=JSON.stringify(wc(n,r.v7_relativeSplatPath)),l=j.useRef(!1);return eh(()=>{l.current=!0}),j.useCallback(function(u,d){if(d===void 0&&(d={}),!l.current)return;if(typeof u=="number"){s.go(u);return}let f=Nc(u,JSON.parse(o),i,d.relative==="path");e==null&&t!=="/"&&(f.pathname=f.pathname==="/"?t:xr([t,f.pathname])),(d.replace?s.replace:s.push)(f,d.state,d)},[t,s,o,i,e])}function Yx(){let{matches:e}=j.useContext(Yt),t=e[e.length-1];return t?t.params:{}}function bi(e,t){let{relative:r}=t===void 0?{}:t,{future:s}=j.useContext(Qt),{matches:n}=j.useContext(Yt),{pathname:i}=bt(),o=JSON.stringify(wc(n,s.v7_relativeSplatPath));return j.useMemo(()=>Nc(e,JSON.parse(o),i,r==="path"),[e,o,i,r])}function Gx(e,t){return Jx(e,t)}function Jx(e,t,r,s){$s()||ge(!1);let{navigator:n}=j.useContext(Qt),{matches:i}=j.useContext(Yt),o=i[i.length-1],l=o?o.params:{};o&&o.pathname;let c=o?o.pathnameBase:"/";o&&o.route;let u=bt(),d;if(t){var f;let y=typeof t=="string"?Cs(t):t;c==="/"||(f=y.pathname)!=null&&f.startsWith(c)||ge(!1),d=y}else d=u;let m=d.pathname||"/",v=m;if(c!=="/"){let y=c.replace(/^\//,"").split("/");v="/"+m.replace(/^\//,"").split("/").slice(y.length).join("/")}let g=Sx(e,{pathname:v}),w=r0(g&&g.map(y=>Object.assign({},y,{params:Object.assign({},l,y.params),pathname:xr([c,n.encodeLocation?n.encodeLocation(y.pathname).pathname:y.pathname]),pathnameBase:y.pathnameBase==="/"?c:xr([c,n.encodeLocation?n.encodeLocation(y.pathnameBase).pathname:y.pathnameBase])})),i,r,s);return t&&w?j.createElement(ji.Provider,{value:{location:Sn({pathname:"/",search:"",hash:"",state:null,key:"default"},d),navigationType:lr.Pop}},w):w}function Xx(){let e=i0(),t=Wx(e)?e.status+" "+e.statusText:e instanceof Error?e.message:JSON.stringify(e),r=e instanceof Error?e.stack:null,n={padding:"0.5rem",backgroundColor:"rgba(200,200,200, 0.5)"},i=null;return j.createElement(j.Fragment,null,j.createElement("h2",null,"Unexpected Application Error!"),j.createElement("h3",{style:{fontStyle:"italic"}},t),r?j.createElement("pre",{style:n},r):null,i)}const Zx=j.createElement(Xx,null);class e0 extends j.Component{constructor(t){super(t),this.state={location:t.location,revalidation:t.revalidation,error:t.error}}static getDerivedStateFromError(t){return{error:t}}static getDerivedStateFromProps(t,r){return r.location!==t.location||r.revalidation!=="idle"&&t.revalidation==="idle"?{error:t.error,location:t.location,revalidation:t.revalidation}:{error:t.error!==void 0?t.error:r.error,location:r.location,revalidation:t.revalidation||r.revalidation}}componentDidCatch(t,r){console.error("React Router caught the following error during render",t,r)}render(){return this.state.error!==void 0?j.createElement(Yt.Provider,{value:this.props.routeContext},j.createElement(Zm.Provider,{value:this.state.error,children:this.props.component})):this.props.children}}function t0(e){let{routeContext:t,match:r,children:s}=e,n=j.useContext(Ni);return n&&n.static&&n.staticContext&&(r.route.errorElement||r.route.ErrorBoundary)&&(n.staticContext._deepestRenderedBoundaryId=r.route.id),j.createElement(Yt.Provider,{value:t},s)}function r0(e,t,r,s){var n;if(t===void 0&&(t=[]),r===void 0&&(r=null),s===void 0&&(s=null),e==null){var i;if(!r)return null;if(r.errors)e=r.matches;else if((i=s)!=null&&i.v7_partialHydration&&t.length===0&&!r.initialized&&r.matches.length>0)e=r.matches;else return null}let o=e,l=(n=r)==null?void 0:n.errors;if(l!=null){let d=o.findIndex(f=>f.route.id&&(l==null?void 0:l[f.route.id])!==void 0);d>=0||ge(!1),o=o.slice(0,Math.min(o.length,d+1))}let c=!1,u=-1;if(r&&s&&s.v7_partialHydration)for(let d=0;d<o.length;d++){let f=o[d];if((f.route.HydrateFallback||f.route.hydrateFallbackElement)&&(u=d),f.route.id){let{loaderData:m,errors:v}=r,g=f.route.loader&&m[f.route.id]===void 0&&(!v||v[f.route.id]===void 0);if(f.route.lazy||g){c=!0,u>=0?o=o.slice(0,u+1):o=[o[0]];break}}}return o.reduceRight((d,f,m)=>{let v,g=!1,w=null,y=null;r&&(v=l&&f.route.id?l[f.route.id]:void 0,w=f.route.errorElement||Zx,c&&(u<0&&m===0?(l0("route-fallback",!1),g=!0,y=null):u===m&&(g=!0,y=f.route.hydrateFallbackElement||null)));let p=t.concat(o.slice(0,m+1)),h=()=>{let x;return v?x=w:g?x=y:f.route.Component?x=j.createElement(f.route.Component,null):f.route.element?x=f.route.element:x=d,j.createElement(t0,{match:f,routeContext:{outlet:d,matches:p,isDataRoute:r!=null},children:x})};return r&&(f.route.ErrorBoundary||f.route.errorElement||m===0)?j.createElement(e0,{location:r.location,revalidation:r.revalidation,component:w,error:v,children:h(),routeContext:{outlet:null,matches:p,isDataRoute:!0}}):h()},null)}var th=function(e){return e.UseBlocker="useBlocker",e.UseRevalidator="useRevalidator",e.UseNavigateStable="useNavigate",e}(th||{}),Ya=function(e){return e.UseBlocker="useBlocker",e.UseLoaderData="useLoaderData",e.UseActionData="useActionData",e.UseRouteError="useRouteError",e.UseNavigation="useNavigation",e.UseRouteLoaderData="useRouteLoaderData",e.UseMatches="useMatches",e.UseRevalidator="useRevalidator",e.UseNavigateStable="useNavigate",e.UseRouteId="useRouteId",e}(Ya||{});function s0(e){let t=j.useContext(Ni);return t||ge(!1),t}function n0(e){let t=j.useContext(Xm);return t||ge(!1),t}function a0(e){let t=j.useContext(Yt);return t||ge(!1),t}function rh(e){let t=a0(),r=t.matches[t.matches.length-1];return r.route.id||ge(!1),r.route.id}function i0(){var e;let t=j.useContext(Zm),r=n0(Ya.UseRouteError),s=rh(Ya.UseRouteError);return t!==void 0?t:(e=r.errors)==null?void 0:e[s]}function o0(){let{router:e}=s0(th.UseNavigateStable),t=rh(Ya.UseNavigateStable),r=j.useRef(!1);return eh(()=>{r.current=!0}),j.useCallback(function(n,i){i===void 0&&(i={}),r.current&&(typeof n=="number"?e.navigate(n):e.navigate(n,Sn({fromRouteId:t},i)))},[e,t])}const Ju={};function l0(e,t,r){!t&&!Ju[e]&&(Ju[e]=!0)}function c0(e,t){e==null||e.v7_startTransition,(e==null?void 0:e.v7_relativeSplatPath)===void 0&&(!t||t.v7_relativeSplatPath),t&&(t.v7_fetcherPersist,t.v7_normalizeFormMethod,t.v7_partialHydration,t.v7_skipActionErrorRevalidation)}function wa(e){let{to:t,replace:r,state:s,relative:n}=e;$s()||ge(!1);let{future:i,static:o}=j.useContext(Qt),{matches:l}=j.useContext(Yt),{pathname:c}=bt(),u=Dt(),d=Nc(t,wc(l,i.v7_relativeSplatPath),c,n==="path"),f=JSON.stringify(d);return j.useEffect(()=>u(JSON.parse(f),{replace:r,state:s,relative:n}),[u,f,n,r,s]),null}function ee(e){ge(!1)}function u0(e){let{basename:t="/",children:r=null,location:s,navigationType:n=lr.Pop,navigator:i,static:o=!1,future:l}=e;$s()&&ge(!1);let c=t.replace(/^\/*/,"/"),u=j.useMemo(()=>({basename:c,navigator:i,static:o,future:Sn({v7_relativeSplatPath:!1},l)}),[c,l,i,o]);typeof s=="string"&&(s=Cs(s));let{pathname:d="/",search:f="",hash:m="",state:v=null,key:g="default"}=s,w=j.useMemo(()=>{let y=Ns(d,c);return y==null?null:{location:{pathname:y,search:f,hash:m,state:v,key:g},navigationType:n}},[c,d,f,m,v,g,n]);return w==null?null:j.createElement(Qt.Provider,{value:u},j.createElement(ji.Provider,{children:r,value:w}))}function Ps(e){let{children:t,location:r}=e;return Gx(ml(t),r)}new Promise(()=>{});function ml(e,t){t===void 0&&(t=[]);let r=[];return j.Children.forEach(e,(s,n)=>{if(!j.isValidElement(s))return;let i=[...t,n];if(s.type===j.Fragment){r.push.apply(r,ml(s.props.children,i));return}s.type!==ee&&ge(!1),!s.props.index||!s.props.children||ge(!1);let o={id:s.props.id||i.join("-"),caseSensitive:s.props.caseSensitive,element:s.props.element,Component:s.props.Component,index:s.props.index,path:s.props.path,loader:s.props.loader,action:s.props.action,errorElement:s.props.errorElement,ErrorBoundary:s.props.ErrorBoundary,hasErrorBoundary:s.props.ErrorBoundary!=null||s.props.errorElement!=null,shouldRevalidate:s.props.shouldRevalidate,handle:s.props.handle,lazy:s.props.lazy};s.props.children&&(o.children=ml(s.props.children,i)),r.push(o)}),r}/**
 * React Router DOM v6.30.1
 *
 * Copyright (c) Remix Software Inc.
 *
 * This source code is licensed under the MIT license found in the
 * LICENSE.md file in the root directory of this source tree.
 *
 * @license MIT
 */function Ga(){return Ga=Object.assign?Object.assign.bind():function(e){for(var t=1;t<arguments.length;t++){var r=arguments[t];for(var s in r)Object.prototype.hasOwnProperty.call(r,s)&&(e[s]=r[s])}return e},Ga.apply(this,arguments)}function sh(e,t){if(e==null)return{};var r={},s=Object.keys(e),n,i;for(i=0;i<s.length;i++)n=s[i],!(t.indexOf(n)>=0)&&(r[n]=e[n]);return r}function d0(e){return!!(e.metaKey||e.altKey||e.ctrlKey||e.shiftKey)}function f0(e,t){return e.button===0&&(!t||t==="_self")&&!d0(e)}function hl(e){return e===void 0&&(e=""),new URLSearchParams(typeof e=="string"||Array.isArray(e)||e instanceof URLSearchParams?e:Object.keys(e).reduce((t,r)=>{let s=e[r];return t.concat(Array.isArray(s)?s.map(n=>[r,n]):[[r,s]])},[]))}function m0(e,t){let r=hl(e);return t&&t.forEach((s,n)=>{r.has(n)||t.getAll(n).forEach(i=>{r.append(n,i)})}),r}const h0=["onClick","relative","reloadDocument","replace","state","target","to","preventScrollReset","viewTransition"],p0=["aria-current","caseSensitive","className","end","style","to","viewTransition","children"],g0="6";try{window.__reactRouterVersion=g0}catch{}const x0=j.createContext({isTransitioning:!1}),y0="startTransition",Xu=up[y0];function v0(e){let{basename:t,children:r,future:s,window:n}=e,i=j.useRef();i.current==null&&(i.current=jx({window:n,v5Compat:!0}));let o=i.current,[l,c]=j.useState({action:o.action,location:o.location}),{v7_startTransition:u}=s||{},d=j.useCallback(f=>{u&&Xu?Xu(()=>c(f)):c(f)},[c,u]);return j.useLayoutEffect(()=>o.listen(d),[o,d]),j.useEffect(()=>c0(s),[s]),j.createElement(u0,{basename:t,children:r,location:l.location,navigationType:l.action,navigator:o,future:s})}const w0=typeof window<"u"&&typeof window.document<"u"&&typeof window.document.createElement<"u",N0=/^(?:[a-z][a-z0-9+.-]*:|\/\/)/i,G=j.forwardRef(function(t,r){let{onClick:s,relative:n,reloadDocument:i,replace:o,state:l,target:c,to:u,preventScrollReset:d,viewTransition:f}=t,m=sh(t,h0),{basename:v}=j.useContext(Qt),g,w=!1;if(typeof u=="string"&&N0.test(u)&&(g=u,w0))try{let x=new URL(window.location.href),b=u.startsWith("//")?new URL(x.protocol+u):new URL(u),k=Ns(b.pathname,v);b.origin===x.origin&&k!=null?u=k+b.search+b.hash:w=!0}catch{}let y=Kx(u,{relative:n}),p=k0(u,{replace:o,state:l,target:c,preventScrollReset:d,relative:n,viewTransition:f});function h(x){s&&s(x),x.defaultPrevented||p(x)}return j.createElement("a",Ga({},m,{href:g||y,onClick:w||i?s:h,ref:r,target:c}))}),j0=j.forwardRef(function(t,r){let{"aria-current":s="page",caseSensitive:n=!1,className:i="",end:o=!1,style:l,to:c,viewTransition:u,children:d}=t,f=sh(t,p0),m=bi(c,{relative:f.relative}),v=bt(),g=j.useContext(Xm),{navigator:w,basename:y}=j.useContext(Qt),p=g!=null&&C0(m)&&u===!0,h=w.encodeLocation?w.encodeLocation(m).pathname:m.pathname,x=v.pathname,b=g&&g.navigation&&g.navigation.location?g.navigation.location.pathname:null;n||(x=x.toLowerCase(),b=b?b.toLowerCase():null,h=h.toLowerCase()),b&&y&&(b=Ns(b,y)||b);const k=h!=="/"&&h.endsWith("/")?h.length-1:h.length;let E=x===h||!o&&x.startsWith(h)&&x.charAt(k)==="/",O=b!=null&&(b===h||!o&&b.startsWith(h)&&b.charAt(h.length)==="/"),$={isActive:E,isPending:O,isTransitioning:p},C=E?s:void 0,D;typeof i=="function"?D=i($):D=[i,E?"active":null,O?"pending":null,p?"transitioning":null].filter(Boolean).join(" ");let U=typeof l=="function"?l($):l;return j.createElement(G,Ga({},f,{"aria-current":C,className:D,ref:r,style:U,to:c,viewTransition:u}),typeof d=="function"?d($):d)});var pl;(function(e){e.UseScrollRestoration="useScrollRestoration",e.UseSubmit="useSubmit",e.UseSubmitFetcher="useSubmitFetcher",e.UseFetcher="useFetcher",e.useViewTransitionState="useViewTransitionState"})(pl||(pl={}));var Zu;(function(e){e.UseFetcher="useFetcher",e.UseFetchers="useFetchers",e.UseScrollRestoration="useScrollRestoration"})(Zu||(Zu={}));function b0(e){let t=j.useContext(Ni);return t||ge(!1),t}function k0(e,t){let{target:r,replace:s,state:n,preventScrollReset:i,relative:o,viewTransition:l}=t===void 0?{}:t,c=Dt(),u=bt(),d=bi(e,{relative:o});return j.useCallback(f=>{if(f0(f,r)){f.preventDefault();let m=s!==void 0?s:Ka(u)===Ka(d);c(e,{replace:m,state:n,preventScrollReset:i,relative:o,viewTransition:l})}},[u,c,d,s,n,r,e,i,o,l])}function S0(e){let t=j.useRef(hl(e)),r=j.useRef(!1),s=bt(),n=j.useMemo(()=>m0(s.search,r.current?null:t.current),[s.search]),i=Dt(),o=j.useCallback((l,c)=>{const u=hl(typeof l=="function"?l(n):l);r.current=!0,i("?"+u,c)},[i,n]);return[n,o]}function C0(e,t){t===void 0&&(t={});let r=j.useContext(x0);r==null&&ge(!1);let{basename:s}=b0(pl.useViewTransitionState),n=bi(e,{relative:t.relative});if(!r.isTransitioning)return!1;let i=Ns(r.currentLocation.pathname,s)||r.currentLocation.pathname,o=Ns(r.nextLocation.pathname,s)||r.nextLocation.pathname;return Qa(n.pathname,o)!=null||Qa(n.pathname,i)!=null}let $0={data:""},P0=e=>typeof window=="object"?((e?e.querySelector("#_goober"):window._goober)||Object.assign((e||document.head).appendChild(document.createElement("style")),{innerHTML:" ",id:"_goober"})).firstChild:e||$0,E0=/(?:([\u0080-\uFFFF\w-%@]+) *:? *([^{;]+?);|([^;}{]*?) *{)|(}\s*)/g,O0=/\/\*[^]*?\*\/|  +/g,ed=/\n+/g,sr=(e,t)=>{let r="",s="",n="";for(let i in e){let o=e[i];i[0]=="@"?i[1]=="i"?r=i+" "+o+";":s+=i[1]=="f"?sr(o,i):i+"{"+sr(o,i[1]=="k"?"":t)+"}":typeof o=="object"?s+=sr(o,t?t.replace(/([^,])+/g,l=>i.replace(/([^,]*:\S+\([^)]*\))|([^,])+/g,c=>/&/.test(c)?c.replace(/&/g,l):l?l+" "+c:c)):i):o!=null&&(i=/^--/.test(i)?i:i.replace(/[A-Z]/g,"-$&").toLowerCase(),n+=sr.p?sr.p(i,o):i+":"+o+";")}return r+(t&&n?t+"{"+n+"}":n)+s},Rt={},nh=e=>{if(typeof e=="object"){let t="";for(let r in e)t+=r+nh(e[r]);return t}return e},A0=(e,t,r,s,n)=>{let i=nh(e),o=Rt[i]||(Rt[i]=(c=>{let u=0,d=11;for(;u<c.length;)d=101*d+c.charCodeAt(u++)>>>0;return"go"+d})(i));if(!Rt[o]){let c=i!==e?e:(u=>{let d,f,m=[{}];for(;d=E0.exec(u.replace(O0,""));)d[4]?m.shift():d[3]?(f=d[3].replace(ed," ").trim(),m.unshift(m[0][f]=m[0][f]||{})):m[0][d[1]]=d[2].replace(ed," ").trim();return m[0]})(e);Rt[o]=sr(n?{["@keyframes "+o]:c}:c,r?"":"."+o)}let l=r&&Rt.g?Rt.g:null;return r&&(Rt.g=Rt[o]),((c,u,d,f)=>{f?u.data=u.data.replace(f,c):u.data.indexOf(c)===-1&&(u.data=d?c+u.data:u.data+c)})(Rt[o],t,s,l),o},D0=(e,t,r)=>e.reduce((s,n,i)=>{let o=t[i];if(o&&o.call){let l=o(r),c=l&&l.props&&l.props.className||/^go/.test(l)&&l;o=c?"."+c:l&&typeof l=="object"?l.props?"":sr(l,""):l===!1?"":l}return s+n+(o??"")},"");function ki(e){let t=this||{},r=e.call?e(t.p):e;return A0(r.unshift?r.raw?D0(r,[].slice.call(arguments,1),t.p):r.reduce((s,n)=>Object.assign(s,n&&n.call?n(t.p):n),{}):r,P0(t.target),t.g,t.o,t.k)}let ah,gl,xl;ki.bind({g:1});let qt=ki.bind({k:1});function L0(e,t,r,s){sr.p=t,ah=e,gl=r,xl=s}function kr(e,t){let r=this||{};return function(){let s=arguments;function n(i,o){let l=Object.assign({},i),c=l.className||n.className;r.p=Object.assign({theme:gl&&gl()},l),r.o=/ *go\d+/.test(c),l.className=ki.apply(r,s)+(c?" "+c:""),t&&(l.ref=o);let u=e;return e[0]&&(u=l.as||e,delete l.as),xl&&u[0]&&xl(l),ah(u,l)}return t?t(n):n}}var T0=e=>typeof e=="function",Ja=(e,t)=>T0(e)?e(t):e,R0=(()=>{let e=0;return()=>(++e).toString()})(),ih=(()=>{let e;return()=>{if(e===void 0&&typeof window<"u"){let t=matchMedia("(prefers-reduced-motion: reduce)");e=!t||t.matches}return e}})(),_0=20,oh=(e,t)=>{switch(t.type){case 0:return{...e,toasts:[t.toast,...e.toasts].slice(0,_0)};case 1:return{...e,toasts:e.toasts.map(i=>i.id===t.toast.id?{...i,...t.toast}:i)};case 2:let{toast:r}=t;return oh(e,{type:e.toasts.find(i=>i.id===r.id)?1:0,toast:r});case 3:let{toastId:s}=t;return{...e,toasts:e.toasts.map(i=>i.id===s||s===void 0?{...i,dismissed:!0,visible:!1}:i)};case 4:return t.toastId===void 0?{...e,toasts:[]}:{...e,toasts:e.toasts.filter(i=>i.id!==t.toastId)};case 5:return{...e,pausedAt:t.time};case 6:let n=t.time-(e.pausedAt||0);return{...e,pausedAt:void 0,toasts:e.toasts.map(i=>({...i,pauseDuration:i.pauseDuration+n}))}}},Na=[],Or={toasts:[],pausedAt:void 0},Br=e=>{Or=oh(Or,e),Na.forEach(t=>{t(Or)})},F0={blank:4e3,error:4e3,success:2e3,loading:1/0,custom:4e3},I0=(e={})=>{let[t,r]=j.useState(Or),s=j.useRef(Or);j.useEffect(()=>(s.current!==Or&&r(Or),Na.push(r),()=>{let i=Na.indexOf(r);i>-1&&Na.splice(i,1)}),[]);let n=t.toasts.map(i=>{var o,l,c;return{...e,...e[i.type],...i,removeDelay:i.removeDelay||((o=e[i.type])==null?void 0:o.removeDelay)||(e==null?void 0:e.removeDelay),duration:i.duration||((l=e[i.type])==null?void 0:l.duration)||(e==null?void 0:e.duration)||F0[i.type],style:{...e.style,...(c=e[i.type])==null?void 0:c.style,...i.style}}});return{...t,toasts:n}},M0=(e,t="blank",r)=>({createdAt:Date.now(),visible:!0,dismissed:!1,type:t,ariaProps:{role:"status","aria-live":"polite"},message:e,pauseDuration:0,...r,id:(r==null?void 0:r.id)||R0()}),Rn=e=>(t,r)=>{let s=M0(t,e,r);return Br({type:2,toast:s}),s.id},ze=(e,t)=>Rn("blank")(e,t);ze.error=Rn("error");ze.success=Rn("success");ze.loading=Rn("loading");ze.custom=Rn("custom");ze.dismiss=e=>{Br({type:3,toastId:e})};ze.remove=e=>Br({type:4,toastId:e});ze.promise=(e,t,r)=>{let s=ze.loading(t.loading,{...r,...r==null?void 0:r.loading});return typeof e=="function"&&(e=e()),e.then(n=>{let i=t.success?Ja(t.success,n):void 0;return i?ze.success(i,{id:s,...r,...r==null?void 0:r.success}):ze.dismiss(s),n}).catch(n=>{let i=t.error?Ja(t.error,n):void 0;i?ze.error(i,{id:s,...r,...r==null?void 0:r.error}):ze.dismiss(s)}),e};var U0=(e,t)=>{Br({type:1,toast:{id:e,height:t}})},z0=()=>{Br({type:5,time:Date.now()})},tn=new Map,V0=1e3,B0=(e,t=V0)=>{if(tn.has(e))return;let r=setTimeout(()=>{tn.delete(e),Br({type:4,toastId:e})},t);tn.set(e,r)},H0=e=>{let{toasts:t,pausedAt:r}=I0(e);j.useEffect(()=>{if(r)return;let i=Date.now(),o=t.map(l=>{if(l.duration===1/0)return;let c=(l.duration||0)+l.pauseDuration-(i-l.createdAt);if(c<0){l.visible&&ze.dismiss(l.id);return}return setTimeout(()=>ze.dismiss(l.id),c)});return()=>{o.forEach(l=>l&&clearTimeout(l))}},[t,r]);let s=j.useCallback(()=>{r&&Br({type:6,time:Date.now()})},[r]),n=j.useCallback((i,o)=>{let{reverseOrder:l=!1,gutter:c=8,defaultPosition:u}=o||{},d=t.filter(v=>(v.position||u)===(i.position||u)&&v.height),f=d.findIndex(v=>v.id===i.id),m=d.filter((v,g)=>g<f&&v.visible).length;return d.filter(v=>v.visible).slice(...l?[m+1]:[0,m]).reduce((v,g)=>v+(g.height||0)+c,0)},[t]);return j.useEffect(()=>{t.forEach(i=>{if(i.dismissed)B0(i.id,i.removeDelay);else{let o=tn.get(i.id);o&&(clearTimeout(o),tn.delete(i.id))}})},[t]),{toasts:t,handlers:{updateHeight:U0,startPause:z0,endPause:s,calculateOffset:n}}},W0=qt`
from {
  transform: scale(0) rotate(45deg);
	opacity: 0;
}
to {
 transform: scale(1) rotate(45deg);
  opacity: 1;
}`,q0=qt`
from {
  transform: scale(0);
  opacity: 0;
}
to {
  transform: scale(1);
  opacity: 1;
}`,K0=qt`
from {
  transform: scale(0) rotate(90deg);
	opacity: 0;
}
to {
  transform: scale(1) rotate(90deg);
	opacity: 1;
}`,Q0=kr("div")`
  width: 20px;
  opacity: 0;
  height: 20px;
  border-radius: 10px;
  background: ${e=>e.primary||"#ff4b4b"};
  position: relative;
  transform: rotate(45deg);

  animation: ${W0} 0.3s cubic-bezier(0.175, 0.885, 0.32, 1.275)
    forwards;
  animation-delay: 100ms;

  &:after,
  &:before {
    content: '';
    animation: ${q0} 0.15s ease-out forwards;
    animation-delay: 150ms;
    position: absolute;
    border-radius: 3px;
    opacity: 0;
    background: ${e=>e.secondary||"#fff"};
    bottom: 9px;
    left: 4px;
    height: 2px;
    width: 12px;
  }

  &:before {
    animation: ${K0} 0.15s ease-out forwards;
    animation-delay: 180ms;
    transform: rotate(90deg);
  }
`,Y0=qt`
  from {
    transform: rotate(0deg);
  }
  to {
    transform: rotate(360deg);
  }
`,G0=kr("div")`
  width: 12px;
  height: 12px;
  box-sizing: border-box;
  border: 2px solid;
  border-radius: 100%;
  border-color: ${e=>e.secondary||"#e0e0e0"};
  border-right-color: ${e=>e.primary||"#616161"};
  animation: ${Y0} 1s linear infinite;
`,J0=qt`
from {
  transform: scale(0) rotate(45deg);
	opacity: 0;
}
to {
  transform: scale(1) rotate(45deg);
	opacity: 1;
}`,X0=qt`
0% {
	height: 0;
	width: 0;
	opacity: 0;
}
40% {
  height: 0;
	width: 6px;
	opacity: 1;
}
100% {
  opacity: 1;
  height: 10px;
}`,Z0=kr("div")`
  width: 20px;
  opacity: 0;
  height: 20px;
  border-radius: 10px;
  background: ${e=>e.primary||"#61d345"};
  position: relative;
  transform: rotate(45deg);

  animation: ${J0} 0.3s cubic-bezier(0.175, 0.885, 0.32, 1.275)
    forwards;
  animation-delay: 100ms;
  &:after {
    content: '';
    box-sizing: border-box;
    animation: ${X0} 0.2s ease-out forwards;
    opacity: 0;
    animation-delay: 200ms;
    position: absolute;
    border-right: 2px solid;
    border-bottom: 2px solid;
    border-color: ${e=>e.secondary||"#fff"};
    bottom: 6px;
    left: 6px;
    height: 10px;
    width: 6px;
  }
`,ey=kr("div")`
  position: absolute;
`,ty=kr("div")`
  position: relative;
  display: flex;
  justify-content: center;
  align-items: center;
  min-width: 20px;
  min-height: 20px;
`,ry=qt`
from {
  transform: scale(0.6);
  opacity: 0.4;
}
to {
  transform: scale(1);
  opacity: 1;
}`,sy=kr("div")`
  position: relative;
  transform: scale(0.6);
  opacity: 0.4;
  min-width: 20px;
  animation: ${ry} 0.3s 0.12s cubic-bezier(0.175, 0.885, 0.32, 1.275)
    forwards;
`,ny=({toast:e})=>{let{icon:t,type:r,iconTheme:s}=e;return t!==void 0?typeof t=="string"?j.createElement(sy,null,t):t:r==="blank"?null:j.createElement(ty,null,j.createElement(G0,{...s}),r!=="loading"&&j.createElement(ey,null,r==="error"?j.createElement(Q0,{...s}):j.createElement(Z0,{...s})))},ay=e=>`
0% {transform: translate3d(0,${e*-200}%,0) scale(.6); opacity:.5;}
100% {transform: translate3d(0,0,0) scale(1); opacity:1;}
`,iy=e=>`
0% {transform: translate3d(0,0,-1px) scale(1); opacity:1;}
100% {transform: translate3d(0,${e*-150}%,-1px) scale(.6); opacity:0;}
`,oy="0%{opacity:0;} 100%{opacity:1;}",ly="0%{opacity:1;} 100%{opacity:0;}",cy=kr("div")`
  display: flex;
  align-items: center;
  background: #fff;
  color: #363636;
  line-height: 1.3;
  will-change: transform;
  box-shadow: 0 3px 10px rgba(0, 0, 0, 0.1), 0 3px 3px rgba(0, 0, 0, 0.05);
  max-width: 350px;
  pointer-events: auto;
  padding: 8px 10px;
  border-radius: 8px;
`,uy=kr("div")`
  display: flex;
  justify-content: center;
  margin: 4px 10px;
  color: inherit;
  flex: 1 1 auto;
  white-space: pre-line;
`,dy=(e,t)=>{let r=e.includes("top")?1:-1,[s,n]=ih()?[oy,ly]:[ay(r),iy(r)];return{animation:t?`${qt(s)} 0.35s cubic-bezier(.21,1.02,.73,1) forwards`:`${qt(n)} 0.4s forwards cubic-bezier(.06,.71,.55,1)`}},fy=j.memo(({toast:e,position:t,style:r,children:s})=>{let n=e.height?dy(e.position||t||"top-center",e.visible):{opacity:0},i=j.createElement(ny,{toast:e}),o=j.createElement(uy,{...e.ariaProps},Ja(e.message,e));return j.createElement(cy,{className:e.className,style:{...n,...r,...e.style}},typeof s=="function"?s({icon:i,message:o}):j.createElement(j.Fragment,null,i,o))});L0(j.createElement);var my=({id:e,className:t,style:r,onHeightUpdate:s,children:n})=>{let i=j.useCallback(o=>{if(o){let l=()=>{let c=o.getBoundingClientRect().height;s(e,c)};l(),new MutationObserver(l).observe(o,{subtree:!0,childList:!0,characterData:!0})}},[e,s]);return j.createElement("div",{ref:i,className:t,style:r},n)},hy=(e,t)=>{let r=e.includes("top"),s=r?{top:0}:{bottom:0},n=e.includes("center")?{justifyContent:"center"}:e.includes("right")?{justifyContent:"flex-end"}:{};return{left:0,right:0,display:"flex",position:"absolute",transition:ih()?void 0:"all 230ms cubic-bezier(.21,1.02,.73,1)",transform:`translateY(${t*(r?1:-1)}px)`,...s,...n}},py=ki`
  z-index: 9999;
  > * {
    pointer-events: auto;
  }
`,aa=16,gy=({reverseOrder:e,position:t="top-center",toastOptions:r,gutter:s,children:n,containerStyle:i,containerClassName:o})=>{let{toasts:l,handlers:c}=H0(r);return j.createElement("div",{id:"_rht_toaster",style:{position:"fixed",zIndex:9999,top:aa,left:aa,right:aa,bottom:aa,pointerEvents:"none",...i},className:o,onMouseEnter:c.startPause,onMouseLeave:c.endPause},l.map(u=>{let d=u.position||t,f=c.calculateOffset(u,{reverseOrder:e,gutter:s,defaultPosition:t}),m=hy(d,f);return j.createElement(my,{id:u.id,key:u.id,onHeightUpdate:c.updateHeight,className:u.visible?py:"",style:m},u.type==="custom"?Ja(u.message,u):n?n(u):j.createElement(fy,{toast:u,position:d}))}))},Q=ze;class xy{constructor(){zn(this,"USERS_KEY","ecommerce_users");zn(this,"CURRENT_USER_KEY","ecommerce_current_user")}initializeDefaultUsers(){if(this.getStoredUsers().length===0){const r={id:"admin-001",email:"<EMAIL>",firstName:"مدير",lastName:"المتجر",role:"admin",createdAt:new Date().toISOString(),updatedAt:new Date().toISOString(),password:"admin123"},s={id:"customer-001",email:"<EMAIL>",firstName:"أحمد",lastName:"محمد",role:"customer",phone:"+************",createdAt:new Date().toISOString(),updatedAt:new Date().toISOString(),password:"customer123"};this.saveUser(r),this.saveUser(s)}}getStoredUsers(){try{const t=localStorage.getItem(this.USERS_KEY);return t?JSON.parse(t):[]}catch{return[]}}saveUser(t){const r=this.getStoredUsers(),s=r.findIndex(n=>n.email===t.email);s>=0?r[s]=t:r.push(t),localStorage.setItem(this.USERS_KEY,JSON.stringify(r))}findUserByEmail(t){return this.getStoredUsers().find(s=>s.email===t)||null}async login(t,r){return this.initializeDefaultUsers(),new Promise((s,n)=>{setTimeout(()=>{const i=this.findUserByEmail(t);if(!i||i.password!==r){n(new Error("بيانات الاعتماد غير صحيحة"));return}const o={id:i.id,email:i.email,firstName:i.firstName,lastName:i.lastName,role:i.role,phone:i.phone,dateOfBirth:i.dateOfBirth,gender:i.gender,createdAt:i.createdAt,updatedAt:new Date().toISOString()},l=`token_${i.id}_${Date.now()}`;localStorage.setItem(this.CURRENT_USER_KEY,JSON.stringify({user:o,token:l})),s({user:o,token:l})},800)})}async register(t){return new Promise((r,s)=>{setTimeout(()=>{if(this.findUserByEmail(t.email)){s(new Error("البريد الإلكتروني مستخدم بالفعل"));return}const i={id:`user_${Date.now()}`,email:t.email,firstName:t.firstName,lastName:t.lastName,role:"customer",password:t.password,createdAt:new Date().toISOString(),updatedAt:new Date().toISOString()};this.saveUser(i);const o={id:i.id,email:i.email,firstName:i.firstName,lastName:i.lastName,role:i.role,createdAt:i.createdAt,updatedAt:i.updatedAt},l=`token_${i.id}_${Date.now()}`;localStorage.setItem(this.CURRENT_USER_KEY,JSON.stringify({user:o,token:l})),r({user:o,token:l})},800)})}async getCurrentUser(){return new Promise((t,r)=>{try{const s=localStorage.getItem(this.CURRENT_USER_KEY);if(!s){r(new Error("لا توجد جلسة نشطة"));return}const{user:n,token:i}=JSON.parse(s);if(!n||!i){r(new Error("بيانات الجلسة غير صحيحة"));return}if(!this.findUserByEmail(n.email)){localStorage.removeItem(this.CURRENT_USER_KEY),r(new Error("المستخدم غير موجود"));return}t(n)}catch{r(new Error("خطأ في استرجاع بيانات المستخدم"))}})}async updateProfile(t){return new Promise(async(r,s)=>{try{const n=await this.getCurrentUser(),i=this.findUserByEmail(n.email);if(!i){s(new Error("المستخدم غير موجود"));return}const o={...i,...t,id:i.id,email:i.email,role:i.role,updatedAt:new Date().toISOString()};this.saveUser(o);const l={id:o.id,email:o.email,firstName:o.firstName,lastName:o.lastName,role:o.role,phone:o.phone,dateOfBirth:o.dateOfBirth,gender:o.gender,createdAt:o.createdAt,updatedAt:o.updatedAt},c=localStorage.getItem(this.CURRENT_USER_KEY);if(c){const{token:u}=JSON.parse(c);localStorage.setItem(this.CURRENT_USER_KEY,JSON.stringify({user:l,token:u}))}r(l)}catch{s(new Error("فشل في تحديث الملف الشخصي"))}})}async changePassword(t,r){return new Promise(async(s,n)=>{try{const i=await this.getCurrentUser(),o=this.findUserByEmail(i.email);if(!o||o.password!==t){n(new Error("كلمة المرور الحالية غير صحيحة"));return}const l={...o,password:r,updatedAt:new Date().toISOString()};this.saveUser(l),s()}catch{n(new Error("فشل في تغيير كلمة المرور"))}})}logout(){localStorage.removeItem(this.CURRENT_USER_KEY)}async getAllUsers(){return new Promise(t=>{const r=this.getStoredUsers().map(s=>({id:s.id,email:s.email,firstName:s.firstName,lastName:s.lastName,role:s.role,phone:s.phone,dateOfBirth:s.dateOfBirth,gender:s.gender,createdAt:s.createdAt,updatedAt:s.updatedAt}));t(r)})}}const ia=new xy,yy={user:null,isAuthenticated:!1,isLoading:!1,error:null},vy=(e,t)=>{switch(t.type){case"AUTH_START":return{...e,isLoading:!0,error:null};case"AUTH_SUCCESS":return{...e,user:t.payload,isAuthenticated:!0,isLoading:!1,error:null};case"AUTH_ERROR":return{...e,user:null,isAuthenticated:!1,isLoading:!1,error:t.payload};case"AUTH_LOGOUT":return{...e,user:null,isAuthenticated:!1,isLoading:!1,error:null};case"CLEAR_ERROR":return{...e,error:null};default:return e}},lh=j.createContext(void 0),at=()=>{const e=j.useContext(lh);if(e===void 0)throw new Error("useAuth must be used within an AuthProvider");return e},wy=({children:e})=>{const[t,r]=j.useReducer(vy,yy);j.useEffect(()=>{r({type:"AUTH_START"}),ia.getCurrentUser().then(c=>{r({type:"AUTH_SUCCESS",payload:c})}).catch(()=>{r({type:"AUTH_LOGOUT"})})},[]);const l={...t,login:async(c,u)=>{try{r({type:"AUTH_START"});const{user:d}=await ia.login(c,u);r({type:"AUTH_SUCCESS",payload:d})}catch(d){const f=d instanceof Error?d.message:"فشل في تسجيل الدخول";throw r({type:"AUTH_ERROR",payload:f}),d}},register:async c=>{try{r({type:"AUTH_START"});const{user:u}=await ia.register(c);r({type:"AUTH_SUCCESS",payload:u})}catch(u){throw r({type:"AUTH_ERROR",payload:u instanceof Error?u.message:"فشل في إنشاء الحساب"}),u}},logout:()=>{ia.logout(),r({type:"AUTH_LOGOUT"})},clearError:()=>{r({type:"CLEAR_ERROR"})}};return a.jsx(lh.Provider,{value:l,children:e})},ch={items:[],total:0,itemCount:0},Is=e=>{const t=e.reduce((s,n)=>s+n.price*n.quantity,0),r=e.reduce((s,n)=>s+n.quantity,0);return{total:t,itemCount:r}},Ny=(e,t)=>{switch(t.type){case"ADD_TO_CART":{const{product:r,variant:s,quantity:n}=t.payload,i=e.items.findIndex(u=>u.productId===r.id&&u.variantId===s.id);let o;if(i>=0)o=e.items.map((u,d)=>d===i?{...u,quantity:u.quantity+n}:u);else{const u={id:`${r.id}-${s.id}`,productId:r.id,product:r,variantId:s.id,variant:s,quantity:n,price:r.price};o=[...e.items,u]}const{total:l,itemCount:c}=Is(o);return{items:o,total:l,itemCount:c}}case"REMOVE_FROM_CART":{const r=e.items.filter(i=>i.id!==t.payload),{total:s,itemCount:n}=Is(r);return{items:r,total:s,itemCount:n}}case"UPDATE_QUANTITY":{const{itemId:r,quantity:s}=t.payload;if(s<=0){const l=e.items.filter(d=>d.id!==r),{total:c,itemCount:u}=Is(l);return{items:l,total:c,itemCount:u}}const n=e.items.map(l=>l.id===r?{...l,quantity:s}:l),{total:i,itemCount:o}=Is(n);return{items:n,total:i,itemCount:o}}case"CLEAR_CART":return ch;case"LOAD_CART":{const{total:r,itemCount:s}=Is(t.payload);return{items:t.payload,total:r,itemCount:s}}default:return e}},uh=j.createContext(void 0),Si=()=>{const e=j.useContext(uh);if(e===void 0)throw new Error("useCart must be used within a CartProvider");return e},jy=({children:e})=>{const[t,r]=j.useReducer(Ny,ch);j.useEffect(()=>{const u=localStorage.getItem("cart");if(u)try{const d=JSON.parse(u);r({type:"LOAD_CART",payload:d})}catch(d){console.error("Error loading cart from localStorage:",d)}},[]),j.useEffect(()=>{localStorage.setItem("cart",JSON.stringify(t.items))},[t.items]);const c={...t,addToCart:(u,d,f=1)=>{r({type:"ADD_TO_CART",payload:{product:u,variant:d,quantity:f}})},removeFromCart:u=>{r({type:"REMOVE_FROM_CART",payload:u})},updateQuantity:(u,d)=>{r({type:"UPDATE_QUANTITY",payload:{itemId:u,quantity:d}})},clearCart:()=>{r({type:"CLEAR_CART"})},getCartItem:(u,d)=>t.items.find(f=>f.productId===u&&f.variantId===d)};return a.jsx(uh.Provider,{value:c,children:e})};class by{constructor(){zn(this,"storageKey","ecommerce_data")}getAllData(){const t=localStorage.getItem(this.storageKey);if(!t)return{admin:this.getDefaultAdminData(),customers:{}};try{const r=JSON.parse(t);return{admin:r.admin||this.getDefaultAdminData(),customers:r.customers||{}}}catch{return{admin:this.getDefaultAdminData(),customers:{}}}}saveAllData(t){localStorage.setItem(this.storageKey,JSON.stringify(t))}getDefaultAdminData(){return{products:[],orders:[],customers:[],analytics:{revenue:{current:0,previous:0,change:0},orders:{current:0,previous:0,change:0},customers:{current:0,previous:0,change:0},avgOrderValue:{current:0,previous:0,change:0}},inventory:{totalValue:0,lowStockItems:0,outOfStockItems:0},settings:{general:{storeNameAr:"اسم المتجر",storeNameEn:"Store Name",email:"<EMAIL>",phone:"+966 XX XXX XXXX",descriptionAr:"وصف المتجر",descriptionEn:"Store Description"},payment:{creditCards:!0,cashOnDelivery:!0,taxRate:15},shipping:{freeShippingThreshold:500,localShippingCost:25,processingTime:2,shippingTime:5},notifications:{orderConfirmation:!0,lowStockAlerts:!0}}}}initializeCustomerData(t){const r=this.getAllData();if(!r.customers[t]){const s={profile:{},orders:[],wishlist:[],addresses:[],paymentCards:[],settings:{notifications:{orderUpdates:!0,promotions:!0,newsletter:!1,smsNotifications:!0,emailNotifications:!0},preferences:{language:"ar",currency:"SAR",timezone:"Asia/Riyadh"}}};return r.customers[t]=s,this.saveAllData(r),s}return r.customers[t]}getCustomerData(t){return this.getAllData().customers[t]||this.initializeCustomerData(t)}updateCustomerData(t,r){const s=this.getAllData();s.customers[t]={...s.customers[t],...r},this.saveAllData(s)}getAdminData(){return this.getAllData().admin}updateAdminData(t){const r=this.getAllData();r.admin={...r.admin,...t},this.saveAllData(r)}addToWishlist(t,r){const s=this.getCustomerData(t);s.wishlist.findIndex(i=>i.productId===r.productId)===-1&&(s.wishlist.push(r),this.updateCustomerData(t,{wishlist:s.wishlist}))}removeFromWishlist(t,r){const s=this.getCustomerData(t);s.wishlist=s.wishlist.filter(n=>n.productId!==r),this.updateCustomerData(t,{wishlist:s.wishlist})}addAddress(t,r){const s=this.getCustomerData(t);(s.addresses.length===0||r.isDefault)&&(s.addresses.forEach(n=>n.isDefault=!1),r.isDefault=!0),s.addresses.push(r),this.updateCustomerData(t,{addresses:s.addresses})}updateAddress(t,r,s){const n=this.getCustomerData(t),i=n.addresses.findIndex(o=>o.id===r);i!==-1&&(n.addresses[i]={...n.addresses[i],...s},this.updateCustomerData(t,{addresses:n.addresses}))}deleteAddress(t,r){const s=this.getCustomerData(t);s.addresses=s.addresses.filter(n=>n.id!==r),this.updateCustomerData(t,{addresses:s.addresses})}addOrder(t,r){const s=this.getCustomerData(t);s.orders.unshift(r),this.updateCustomerData(t,{orders:s.orders});const n=this.getAdminData();n.orders.unshift(r),this.updateAdminData({orders:n.orders})}addProduct(t){const r=this.getAdminData();r.products.push(t),this.updateAdminData({products:r.products})}updateProduct(t,r){const s=this.getAdminData(),n=s.products.findIndex(i=>i.id===t);n!==-1&&(s.products[n]={...s.products[n],...r},this.updateAdminData({products:s.products}))}deleteProduct(t){const r=this.getAdminData();r.products=r.products.filter(s=>s.id!==t),this.updateAdminData({products:r.products})}getAllProducts(){return this.getAdminData().products||[]}getProductById(t){return this.getAllProducts().find(s=>s.id===t)||null}updateOrderStatus(t,r){const s=this.getAdminData(),n=s.orders.findIndex(i=>i.id===t);n!==-1&&(s.orders[n].status=r,s.orders[n].updatedAt=new Date().toISOString(),this.updateAdminData({orders:s.orders}))}getAnalytics(){const t=this.getAdminData(),r=t.orders||[],s=t.customers||[],n=r.reduce((c,u)=>c+u.total,0),i=r.length,o=s.length,l=i>0?n/i:0;return{revenue:{current:n,previous:0,change:0},orders:{current:i,previous:0,change:0},customers:{current:o,previous:0,change:0},avgOrderValue:{current:l,previous:0,change:0}}}}const H=new by;class ky{getAllProducts(){return H.getAdminData().products||[]}getProductById(t){return this.getAllProducts().find(s=>s.id===t)||null}getFeaturedProducts(){return this.getAllProducts().filter(r=>r.featured)}getProductsByCategory(t){return this.getAllProducts().filter(s=>s.category===t)}searchProducts(t){const r=this.getAllProducts(),s=t.toLowerCase();return r.filter(n=>n.name.toLowerCase().includes(s)||n.nameAr.toLowerCase().includes(s)||n.description.toLowerCase().includes(s)||n.descriptionAr.toLowerCase().includes(s)||n.category.toLowerCase().includes(s))}getCategories(){const t=this.getAllProducts(),r=t.map(n=>n.category);return[...new Set(r)].map(n=>({id:n,name:n,nameAr:this.getCategoryNameAr(n),slug:n.toLowerCase().replace(/\s+/g,"-"),description:"",descriptionAr:"",image:"",productCount:t.filter(i=>i.category===n).length}))}getCategoryNameAr(t){return{shirts:"قمصان",pants:"بناطيل",dresses:"فساتين",accessories:"إكسسوارات",shoes:"أحذية",jackets:"جاكيتات"}[t]||t}addProduct(t){const s=H.getAdminData().products||[],n={id:this.generateId(),name:t.name,nameAr:t.nameAr,description:t.description,descriptionAr:t.descriptionAr,price:t.price,originalPrice:t.originalPrice,category:t.category,images:t.images,stock:t.stock,variants:t.variants.map(i=>({id:i.id,size:i.size,color:i.color,stock:i.stock,sku:i.sku||`${i.id}-${i.size}-${i.color}`.replace(/\s+/g,"-").toLowerCase()})),featured:t.featured,tags:t.tags||[],rating:t.rating||0,reviewCount:t.reviewCount||0,createdAt:new Date().toISOString(),updatedAt:new Date().toISOString()};return s.push(n),H.updateAdminData({products:s}),n}updateProduct(t,r){const s=this.getAllProducts(),n=s.findIndex(i=>i.id===t);return n===-1?null:(s[n]={...s[n],...r,updatedAt:new Date().toISOString()},H.updateAdminData({products:s}),s[n])}deleteProduct(t){const r=this.getAllProducts(),s=r.filter(n=>n.id!==t);return s.length===r.length?!1:(H.updateAdminData({products:s}),!0)}updateStock(t,r,s){const n=this.getProductById(t);if(!n)return!1;const i=n.variants.find(o=>o.id===r);return i?(i.stock=s,n.stock=n.variants.reduce((o,l)=>o+l.stock,0),this.updateProduct(t,{variants:n.variants,stock:n.stock}),!0):!1}addVariant(t,r){const s=this.getProductById(t);if(!s)return null;const n={...r,id:this.generateId()};return s.variants.push(n),s.stock=s.variants.reduce((i,o)=>i+o.stock,0),this.updateProduct(t,{variants:s.variants,stock:s.stock}),n}removeVariant(t,r){const s=this.getProductById(t);if(!s)return!1;const n=s.variants.filter(i=>i.id!==r);return n.length===s.variants.length?!1:(s.variants=n,s.stock=s.variants.reduce((i,o)=>i+o.stock,0),this.updateProduct(t,{variants:s.variants,stock:s.stock}),!0)}getLowStockProducts(t=5){return this.getAllProducts().filter(s=>s.stock<=t)}getOutOfStockProducts(){return this.getAllProducts().filter(r=>r.stock===0)}getProductsStats(){const t=this.getAllProducts(),r=this.getLowStockProducts(),s=this.getOutOfStockProducts(),n=t.reduce((l,c)=>l+c.price*c.stock,0),o=[...new Set(t.map(l=>l.category))].map(l=>{const c=t.filter(u=>u.category===l);return{category:l,count:c.length,totalStock:c.reduce((u,d)=>u+d.stock,0),totalValue:c.reduce((u,d)=>u+d.price*d.stock,0)}});return{totalProducts:t.length,totalValue:n,lowStockCount:r.length,outOfStockCount:s.length,categories:o,averagePrice:t.length>0?t.reduce((l,c)=>l+c.price,0)/t.length:0}}bulkUpdateProducts(t){let r=0;return t.forEach(({id:s,updates:n})=>{this.updateProduct(s,n)&&r++}),r}generateId(){return Date.now().toString(36)+Math.random().toString(36).substring(2)}validateProduct(t){const r=[];return(!t.name||t.name.trim().length===0)&&r.push("Product name is required"),(!t.nameAr||t.nameAr.trim().length===0)&&r.push("Arabic product name is required"),(!t.price||t.price<=0)&&r.push("Valid price is required"),(!t.category||t.category.trim().length===0)&&r.push("Category is required"),(!t.images||t.images.length===0)&&r.push("At least one image is required"),(!t.variants||t.variants.length===0)&&r.push("At least one variant is required"),{isValid:r.length===0,errors:r}}}const rn=new ky,Sy={products:[],categories:[],featuredProducts:[],isLoading:!1,error:null},Cy=(e,t)=>{switch(t.type){case"FETCH_START":return{...e,isLoading:!0,error:null};case"FETCH_PRODUCTS_SUCCESS":return{...e,products:t.payload,featuredProducts:t.payload.filter(r=>r.featured),isLoading:!1,error:null};case"FETCH_CATEGORIES_SUCCESS":return{...e,categories:t.payload,isLoading:!1,error:null};case"FETCH_ERROR":return{...e,isLoading:!1,error:t.payload};case"CLEAR_ERROR":return{...e,error:null};default:return e}},dh=j.createContext(void 0),Ci=()=>{const e=j.useContext(dh);if(e===void 0)throw new Error("useProducts must be used within a ProductProvider");return e},$y=({children:e})=>{const[t,r]=j.useReducer(Cy,Sy);j.useEffect(()=>{s(),n()},[]);const s=async()=>{try{r({type:"FETCH_START"});const d=rn.getAllProducts();r({type:"FETCH_PRODUCTS_SUCCESS",payload:d})}catch(d){r({type:"FETCH_ERROR",payload:d instanceof Error?d.message:"Failed to fetch products"})}},n=async()=>{try{const d=rn.getCategories();r({type:"FETCH_CATEGORIES_SUCCESS",payload:d})}catch(d){r({type:"FETCH_ERROR",payload:d instanceof Error?d.message:"Failed to fetch categories"})}},u={...t,fetchProducts:s,fetchCategories:n,getProduct:d=>t.products.find(f=>f.id===d),getProductsByCategory:d=>t.products.filter(f=>f.category===d),searchProducts:d=>{const f=d.toLowerCase();return t.products.filter(m=>m.name.toLowerCase().includes(f)||m.description.toLowerCase().includes(f)||m.tags.some(v=>v.toLowerCase().includes(f)))},clearError:()=>{r({type:"CLEAR_ERROR"})}};return a.jsx(dh.Provider,{value:u,children:e})},Py=(e,t,r,s)=>{var i,o,l,c;const n=[r,{code:t,...s||{}}];if((o=(i=e==null?void 0:e.services)==null?void 0:i.logger)!=null&&o.forward)return e.services.logger.forward(n,"warn","react-i18next::",!0);Tr(n[0])&&(n[0]=`react-i18next:: ${n[0]}`),(c=(l=e==null?void 0:e.services)==null?void 0:l.logger)!=null&&c.warn?e.services.logger.warn(...n):console!=null&&console.warn&&console.warn(...n)},td={},yl=(e,t,r,s)=>{Tr(r)&&td[r]||(Tr(r)&&(td[r]=new Date),Py(e,t,r,s))},fh=(e,t)=>()=>{if(e.isInitialized)t();else{const r=()=>{setTimeout(()=>{e.off("initialized",r)},0),t()};e.on("initialized",r)}},vl=(e,t,r)=>{e.loadNamespaces(t,fh(e,r))},rd=(e,t,r,s)=>{if(Tr(r)&&(r=[r]),e.options.preload&&e.options.preload.indexOf(t)>-1)return vl(e,r,s);r.forEach(n=>{e.options.ns.indexOf(n)<0&&e.options.ns.push(n)}),e.loadLanguages(t,fh(e,s))},Ey=(e,t,r={})=>!t.languages||!t.languages.length?(yl(t,"NO_LANGUAGES","i18n.languages were undefined or empty",{languages:t.languages}),!0):t.hasLoadedNamespace(e,{lng:r.lng,precheck:(s,n)=>{if(r.bindI18n&&r.bindI18n.indexOf("languageChanging")>-1&&s.services.backendConnector.backend&&s.isLanguageChangingTo&&!n(s.isLanguageChangingTo,e))return!1}}),Tr=e=>typeof e=="string",Oy=e=>typeof e=="object"&&e!==null,Ay=/&(?:amp|#38|lt|#60|gt|#62|apos|#39|quot|#34|nbsp|#160|copy|#169|reg|#174|hellip|#8230|#x2F|#47);/g,Dy={"&amp;":"&","&#38;":"&","&lt;":"<","&#60;":"<","&gt;":">","&#62;":">","&apos;":"'","&#39;":"'","&quot;":'"',"&#34;":'"',"&nbsp;":" ","&#160;":" ","&copy;":"©","&#169;":"©","&reg;":"®","&#174;":"®","&hellip;":"…","&#8230;":"…","&#x2F;":"/","&#47;":"/"},Ly=e=>Dy[e],Ty=e=>e.replace(Ay,Ly);let wl={bindI18n:"languageChanged",bindI18nStore:"",transEmptyNodeValue:"",transSupportBasicHtmlNodes:!0,transWrapTextNodes:"",transKeepBasicHtmlNodesFor:["br","strong","i","p"],useSuspense:!0,unescape:Ty};const Ry=(e={})=>{wl={...wl,...e}},_y=()=>wl;let mh;const Fy=e=>{mh=e},Iy=()=>mh,My={type:"3rdParty",init(e){Ry(e.options.react),Fy(e)}},Uy=j.createContext();class zy{constructor(){this.usedNamespaces={}}addUsedNamespaces(t){t.forEach(r=>{this.usedNamespaces[r]||(this.usedNamespaces[r]=!0)})}getUsedNamespaces(){return Object.keys(this.usedNamespaces)}}const Vy=(e,t)=>{const r=j.useRef();return j.useEffect(()=>{r.current=t?r.current:e},[e,t]),r.current},hh=(e,t,r,s)=>e.getFixedT(t,r,s),By=(e,t,r,s)=>j.useCallback(hh(e,t,r,s),[e,t,r,s]),Hy=(e,t={})=>{var b,k,E,O;const{i18n:r}=t,{i18n:s,defaultNS:n}=j.useContext(Uy)||{},i=r||s||Iy();if(i&&!i.reportNamespaces&&(i.reportNamespaces=new zy),!i){yl(i,"NO_I18NEXT_INSTANCE","useTranslation: You will need to pass in an i18next instance by using initReactI18next");const $=(D,U)=>Tr(U)?U:Oy(U)&&Tr(U.defaultValue)?U.defaultValue:Array.isArray(D)?D[D.length-1]:D,C=[$,{},!1];return C.t=$,C.i18n={},C.ready=!1,C}(b=i.options.react)!=null&&b.wait&&yl(i,"DEPRECATED_OPTION","useTranslation: It seems you are still using the old wait option, you may migrate to the new useSuspense behaviour.");const o={..._y(),...i.options.react,...t},{useSuspense:l,keyPrefix:c}=o;let u=e||n||((k=i.options)==null?void 0:k.defaultNS);u=Tr(u)?[u]:u||["translation"],(O=(E=i.reportNamespaces).addUsedNamespaces)==null||O.call(E,u);const d=(i.isInitialized||i.initializedStoreOnce)&&u.every($=>Ey($,i,o)),f=By(i,t.lng||null,o.nsMode==="fallback"?u:u[0],c),m=()=>f,v=()=>hh(i,t.lng||null,o.nsMode==="fallback"?u:u[0],c),[g,w]=j.useState(m);let y=u.join();t.lng&&(y=`${t.lng}${y}`);const p=Vy(y),h=j.useRef(!0);j.useEffect(()=>{const{bindI18n:$,bindI18nStore:C}=o;h.current=!0,!d&&!l&&(t.lng?rd(i,t.lng,u,()=>{h.current&&w(v)}):vl(i,u,()=>{h.current&&w(v)})),d&&p&&p!==y&&h.current&&w(v);const D=()=>{h.current&&w(v)};return $&&(i==null||i.on($,D)),C&&(i==null||i.store.on(C,D)),()=>{h.current=!1,i&&$&&($==null||$.split(" ").forEach(U=>i.off(U,D))),C&&i&&C.split(" ").forEach(U=>i.store.off(U,D))}},[i,y]),j.useEffect(()=>{h.current&&d&&w(m)},[i,c,d]);const x=[g,i,d];if(x.t=g,x.i18n=i,x.ready=d,d||!d&&!l)return x;throw new Promise($=>{t.lng?rd(i,t.lng,u,()=>$()):vl(i,u,()=>$())})},ph=j.createContext(void 0),oe=()=>{const e=j.useContext(ph);if(!e)throw new Error("useLanguage must be used within a LanguageProvider");return e},Wy=({children:e})=>{const{i18n:t,t:r}=Hy(),[s,n]=j.useState(t.language||"ar"),[i,o]=j.useState(s==="ar"),l=async u=>{try{await t.changeLanguage(u),n(u),o(u==="ar"),document.documentElement.dir=u==="ar"?"rtl":"ltr",document.documentElement.lang=u,u==="ar"?(document.body.classList.add("rtl"),document.body.classList.add("font-arabic"),document.body.classList.remove("ltr")):(document.body.classList.add("ltr"),document.body.classList.remove("rtl"),document.body.classList.remove("font-arabic")),localStorage.setItem("i18nextLng",u)}catch(d){console.error("Error changing language:",d)}};j.useEffect(()=>{document.documentElement.dir=i?"rtl":"ltr",document.documentElement.lang=s,i?(document.body.classList.add("rtl"),document.body.classList.add("font-arabic"),document.body.classList.remove("ltr")):(document.body.classList.add("ltr"),document.body.classList.remove("rtl"),document.body.classList.remove("font-arabic"))},[i,s]),j.useEffect(()=>{const u=d=>{n(d),o(d==="ar")};return t.on("languageChanged",u),()=>{t.off("languageChanged",u)}},[t]);const c={language:s,isRTL:i,changeLanguage:l,t:r};return a.jsx(ph.Provider,{value:c,children:e})},mo=({children:e,requireAdmin:t=!1,requiredRole:r})=>{const{isAuthenticated:s,user:n,isLoading:i}=at(),o=bt();return i?a.jsx("div",{className:"min-h-screen flex items-center justify-center",children:a.jsx("div",{className:"animate-spin rounded-full h-32 w-32 border-b-2 border-black"})}):s?t&&(n==null?void 0:n.role)!=="admin"?a.jsx(wa,{to:"/",replace:!0}):r&&(n==null?void 0:n.role)!==r?a.jsx(wa,{to:"/",replace:!0}):a.jsx(a.Fragment,{children:e}):a.jsx(wa,{to:"/login",state:{from:o},replace:!0})};var qy={xmlns:"http://www.w3.org/2000/svg",width:24,height:24,viewBox:"0 0 24 24",fill:"none",stroke:"currentColor",strokeWidth:2,strokeLinecap:"round",strokeLinejoin:"round"};const Ky=e=>e.replace(/([a-z0-9])([A-Z])/g,"$1-$2").toLowerCase(),Qy=(e,t)=>{const r=j.forwardRef(({color:s="currentColor",size:n=24,strokeWidth:i=2,absoluteStrokeWidth:o,children:l,...c},u)=>j.createElement("svg",{ref:u,...qy,width:n,height:n,stroke:s,strokeWidth:o?Number(i)*24/Number(n):i,className:`lucide lucide-${Ky(e)}`,...c},[...t.map(([d,f])=>j.createElement(d,f)),...(Array.isArray(l)?l:[l])||[]]));return r.displayName=`${e}`,r};var B=Qy;const Cn=B("AlertTriangle",[["path",{d:"m21.73 18-8-14a2 2 0 0 0-3.48 0l-8 14A2 2 0 0 0 4 21h16a2 2 0 0 0 1.73-3Z",key:"c3ski4"}],["path",{d:"M12 9v4",key:"juzpu7"}],["path",{d:"M12 17h.01",key:"p32p05"}]]),Xa=B("ArrowLeft",[["path",{d:"m12 19-7-7 7-7",key:"1l729n"}],["path",{d:"M19 12H5",key:"x3x0zl"}]]),sd=B("ArrowRight",[["path",{d:"M5 12h14",key:"1ays0h"}],["path",{d:"m12 5 7 7-7 7",key:"xquz4c"}]]),Nl=B("BarChart3",[["path",{d:"M3 3v18h18",key:"1s2lah"}],["path",{d:"M18 17V9",key:"2bz60n"}],["path",{d:"M13 17V5",key:"1frdt8"}],["path",{d:"M8 17v-3",key:"17ska0"}]]),gh=B("Bell",[["path",{d:"M6 8a6 6 0 0 1 12 0c0 7 3 9 3 9H3s3-2 3-9",key:"1qo2s2"}],["path",{d:"M10.3 21a1.94 1.94 0 0 0 3.4 0",key:"qgo35s"}]]),Yy=B("Building",[["rect",{width:"16",height:"20",x:"4",y:"2",rx:"2",ry:"2",key:"76otgf"}],["path",{d:"M9 22v-4h6v4",key:"r93iot"}],["path",{d:"M8 6h.01",key:"1dz90k"}],["path",{d:"M16 6h.01",key:"1x0f13"}],["path",{d:"M12 6h.01",key:"1vi96p"}],["path",{d:"M12 10h.01",key:"1nrarc"}],["path",{d:"M12 14h.01",key:"1etili"}],["path",{d:"M16 10h.01",key:"1m94wz"}],["path",{d:"M16 14h.01",key:"1gbofw"}],["path",{d:"M8 10h.01",key:"19clt8"}],["path",{d:"M8 14h.01",key:"6423bh"}]]),jl=B("Calendar",[["rect",{width:"18",height:"18",x:"3",y:"4",rx:"2",ry:"2",key:"eu3xkr"}],["line",{x1:"16",x2:"16",y1:"2",y2:"6",key:"m3sa8f"}],["line",{x1:"8",x2:"8",y1:"2",y2:"6",key:"18kwsl"}],["line",{x1:"3",x2:"21",y1:"10",y2:"10",key:"xt86sb"}]]),Gy=B("CheckCircle",[["path",{d:"M22 11.08V12a10 10 0 1 1-5.93-9.14",key:"g774vq"}],["polyline",{points:"22 4 12 14.01 9 11.01",key:"6xbx8j"}]]),nd=B("Check",[["polyline",{points:"20 6 9 17 4 12",key:"10jjfj"}]]),Jy=B("ChevronDown",[["path",{d:"m6 9 6 6 6-6",key:"qrunsl"}]]),ho=B("Clock",[["circle",{cx:"12",cy:"12",r:"10",key:"1mglay"}],["polyline",{points:"12 6 12 12 16 14",key:"68esgv"}]]),_n=B("CreditCard",[["rect",{width:"20",height:"14",x:"2",y:"5",rx:"2",key:"ynyp8z"}],["line",{x1:"2",x2:"22",y1:"10",y2:"10",key:"1b3vmo"}]]),$i=B("DollarSign",[["line",{x1:"12",x2:"12",y1:"2",y2:"22",key:"7eqyqh"}],["path",{d:"M17 5H9.5a3.5 3.5 0 0 0 0 7h5a3.5 3.5 0 0 1 0 7H6",key:"1b0p4s"}]]),po=B("EyeOff",[["path",{d:"M9.88 9.88a3 3 0 1 0 4.24 4.24",key:"1jxqfv"}],["path",{d:"M10.73 5.08A10.43 10.43 0 0 1 12 5c7 0 10 7 10 7a13.16 13.16 0 0 1-1.67 2.68",key:"9wicm4"}],["path",{d:"M6.61 6.61A13.526 13.526 0 0 0 2 12s3 7 10 7a9.74 9.74 0 0 0 5.39-1.61",key:"1jreej"}],["line",{x1:"2",x2:"22",y1:"2",y2:"22",key:"a6p6uj"}]]),ms=B("Eye",[["path",{d:"M2 12s3-7 10-7 10 7 10 7-3 7-10 7-10-7-10-7Z",key:"rwhkz3"}],["circle",{cx:"12",cy:"12",r:"3",key:"1v7zrd"}]]),Xy=B("Facebook",[["path",{d:"M18 2h-3a5 5 0 0 0-5 5v3H7v4h3v8h4v-8h3l1-4h-4V7a1 1 0 0 1 1-1h3z",key:"1jg4f8"}]]),xh=B("Globe",[["circle",{cx:"12",cy:"12",r:"10",key:"1mglay"}],["line",{x1:"2",x2:"22",y1:"12",y2:"12",key:"1dnqot"}],["path",{d:"M12 2a15.3 15.3 0 0 1 4 10 15.3 15.3 0 0 1-4 10 15.3 15.3 0 0 1-4-10 15.3 15.3 0 0 1 4-10z",key:"nb9nel"}]]),Zy=B("Grid",[["rect",{width:"18",height:"18",x:"3",y:"3",rx:"2",ry:"2",key:"1m3agn"}],["line",{x1:"3",x2:"21",y1:"9",y2:"9",key:"1vqk6q"}],["line",{x1:"3",x2:"21",y1:"15",y2:"15",key:"o2sbyz"}],["line",{x1:"9",x2:"9",y1:"3",y2:"21",key:"13tij5"}],["line",{x1:"15",x2:"15",y1:"3",y2:"21",key:"1hpv9i"}]]),ev=B("Headphones",[["path",{d:"M3 14h3a2 2 0 0 1 2 2v3a2 2 0 0 1-2 2H5a2 2 0 0 1-2-2v-7a9 9 0 0 1 18 0v7a2 2 0 0 1-2 2h-1a2 2 0 0 1-2-2v-3a2 2 0 0 1 2-2h3",key:"1xhozi"}]]),Fn=B("Heart",[["path",{d:"M19 14c1.49-1.46 3-3.21 3-5.5A5.5 5.5 0 0 0 16.5 3c-1.76 0-3 .5-4.5 2-1.5-1.5-2.74-2-4.5-2A5.5 5.5 0 0 0 2 8.5c0 2.3 1.5 4.05 3 5.5l7 7Z",key:"c3ymky"}]]),tv=B("Home",[["path",{d:"m3 9 9-7 9 7v11a2 2 0 0 1-2 2H5a2 2 0 0 1-2-2z",key:"y5dka4"}],["polyline",{points:"9 22 9 12 15 12 15 22",key:"e2us08"}]]),rv=B("Instagram",[["rect",{width:"20",height:"20",x:"2",y:"2",rx:"5",ry:"5",key:"2e1cvw"}],["path",{d:"M16 11.37A4 4 0 1 1 12.63 8 4 4 0 0 1 16 11.37z",key:"9exkf1"}],["line",{x1:"17.5",x2:"17.51",y1:"6.5",y2:"6.5",key:"r4j83e"}]]),sv=B("LayoutDashboard",[["rect",{width:"7",height:"9",x:"3",y:"3",rx:"1",key:"10lvy0"}],["rect",{width:"7",height:"5",x:"14",y:"3",rx:"1",key:"16une8"}],["rect",{width:"7",height:"9",x:"14",y:"12",rx:"1",key:"1hutg5"}],["rect",{width:"7",height:"5",x:"3",y:"16",rx:"1",key:"ldoo1y"}]]),nv=B("List",[["line",{x1:"8",x2:"21",y1:"6",y2:"6",key:"7ey8pc"}],["line",{x1:"8",x2:"21",y1:"12",y2:"12",key:"rjfblc"}],["line",{x1:"8",x2:"21",y1:"18",y2:"18",key:"c3b1m8"}],["line",{x1:"3",x2:"3.01",y1:"6",y2:"6",key:"1g7gq3"}],["line",{x1:"3",x2:"3.01",y1:"12",y2:"12",key:"1pjlvk"}],["line",{x1:"3",x2:"3.01",y1:"18",y2:"18",key:"28t2mc"}]]),av=B("Lock",[["rect",{width:"18",height:"11",x:"3",y:"11",rx:"2",ry:"2",key:"1w4ew1"}],["path",{d:"M7 11V7a5 5 0 0 1 10 0v4",key:"fwvmzm"}]]),yh=B("LogOut",[["path",{d:"M9 21H5a2 2 0 0 1-2-2V5a2 2 0 0 1 2-2h4",key:"1uf3rs"}],["polyline",{points:"16 17 21 12 16 7",key:"1gabdz"}],["line",{x1:"21",x2:"9",y1:"12",y2:"12",key:"1uyos4"}]]),vh=B("Mail",[["rect",{width:"20",height:"16",x:"2",y:"4",rx:"2",key:"18n3k1"}],["path",{d:"m22 7-8.97 5.7a1.94 1.94 0 0 1-2.06 0L2 7",key:"1ocrg3"}]]),wr=B("MapPin",[["path",{d:"M20 10c0 6-8 12-8 12s-8-6-8-12a8 8 0 0 1 16 0Z",key:"2oe9fu"}],["circle",{cx:"12",cy:"10",r:"3",key:"ilqhr7"}]]),jc=B("Menu",[["line",{x1:"4",x2:"20",y1:"12",y2:"12",key:"1e0a9i"}],["line",{x1:"4",x2:"20",y1:"6",y2:"6",key:"1owob3"}],["line",{x1:"4",x2:"20",y1:"18",y2:"18",key:"yk5zj1"}]]),iv=B("MessageCircle",[["path",{d:"m3 21 1.9-5.7a8.5 8.5 0 1 1 3.8 3.8z",key:"v2veuj"}]]),wh=B("Minus",[["path",{d:"M5 12h14",key:"1ays0h"}]]),Je=B("Package",[["path",{d:"M16.5 9.4 7.55 4.24",key:"10qotr"}],["path",{d:"M21 16V8a2 2 0 0 0-1-1.73l-7-4a2 2 0 0 0-2 0l-7 4A2 2 0 0 0 3 8v8a2 2 0 0 0 1 1.73l7 4a2 2 0 0 0 2 0l7-4A2 2 0 0 0 21 16z",key:"yt0hxn"}],["polyline",{points:"3.29 7 12 12 20.71 7",key:"ousv84"}],["line",{x1:"12",x2:"12",y1:"22",y2:"12",key:"a4e8g8"}]]),Pi=B("PenSquare",[["path",{d:"M11 4H4a2 2 0 0 0-2 2v14a2 2 0 0 0 2 2h14a2 2 0 0 0 2-2v-7",key:"1qinfi"}],["path",{d:"M18.5 2.5a2.12 2.12 0 0 1 3 3L12 15l-4 1 1-4Z",key:"w2jsv5"}]]),ov=B("Phone",[["path",{d:"M22 16.92v3a2 2 0 0 1-2.18 2 19.79 19.79 0 0 1-8.63-3.07 19.5 19.5 0 0 1-6-6 19.79 19.79 0 0 1-3.07-8.67A2 2 0 0 1 4.11 2h3a2 2 0 0 1 2 1.72 12.84 12.84 0 0 0 .7 2.81 2 2 0 0 1-.45 2.11L8.09 9.91a16 16 0 0 0 6 6l1.27-1.27a2 2 0 0 1 2.11-.45 12.84 12.84 0 0 0 2.81.7A2 2 0 0 1 22 16.92z",key:"foiqr5"}]]),Ur=B("Plus",[["path",{d:"M5 12h14",key:"1ays0h"}],["path",{d:"M12 5v14",key:"s699le"}]]),lv=B("RotateCcw",[["path",{d:"M3 12a9 9 0 1 0 9-9 9.75 9.75 0 0 0-6.74 2.74L3 8",key:"1357e3"}],["path",{d:"M3 3v5h5",key:"1xhq8a"}]]),cv=B("Save",[["path",{d:"M19 21H5a2 2 0 0 1-2-2V5a2 2 0 0 1 2-2h11l5 5v11a2 2 0 0 1-2 2z",key:"1owoqh"}],["polyline",{points:"17 21 17 13 7 13 7 21",key:"1md35c"}],["polyline",{points:"7 3 7 8 15 8",key:"8nz8an"}]]),js=B("Search",[["circle",{cx:"11",cy:"11",r:"8",key:"4ej97u"}],["path",{d:"m21 21-4.3-4.3",key:"1qie3q"}]]),bc=B("Settings",[["path",{d:"M12.22 2h-.44a2 2 0 0 0-2 2v.18a2 2 0 0 1-1 1.73l-.43.25a2 2 0 0 1-2 0l-.15-.08a2 2 0 0 0-2.73.73l-.22.38a2 2 0 0 0 .73 2.73l.15.1a2 2 0 0 1 1 1.72v.51a2 2 0 0 1-1 1.74l-.15.09a2 2 0 0 0-.73 2.73l.22.38a2 2 0 0 0 2.73.73l.15-.08a2 2 0 0 1 2 0l.43.25a2 2 0 0 1 1 1.73V20a2 2 0 0 0 2 2h.44a2 2 0 0 0 2-2v-.18a2 2 0 0 1 1-1.73l.43-.25a2 2 0 0 1 2 0l.15.08a2 2 0 0 0 2.73-.73l.22-.39a2 2 0 0 0-.73-2.73l-.15-.08a2 2 0 0 1-1-1.74v-.5a2 2 0 0 1 1-1.74l.15-.09a2 2 0 0 0 .73-2.73l-.22-.38a2 2 0 0 0-2.73-.73l-.15.08a2 2 0 0 1-2 0l-.43-.25a2 2 0 0 1-1-1.73V4a2 2 0 0 0-2-2z",key:"1qme2f"}],["circle",{cx:"12",cy:"12",r:"3",key:"1v7zrd"}]]),Nh=B("Shield",[["path",{d:"M12 22s8-4 8-10V5l-8-3-8 3v7c0 6 8 10 8 10z",key:"3xmgem"}]]),kc=B("ShoppingBag",[["path",{d:"M6 2 3 6v14a2 2 0 0 0 2 2h14a2 2 0 0 0 2-2V6l-3-4Z",key:"hou9p0"}],["path",{d:"M3 6h18",key:"d0wm0j"}],["path",{d:"M16 10a4 4 0 0 1-8 0",key:"1ltviw"}]]),Es=B("ShoppingCart",[["circle",{cx:"8",cy:"21",r:"1",key:"jimo8o"}],["circle",{cx:"19",cy:"21",r:"1",key:"13723u"}],["path",{d:"M2.05 2.05h2l2.66 12.42a2 2 0 0 0 2 1.58h9.78a2 2 0 0 0 1.95-1.57l1.65-7.43H5.12",key:"9zh506"}]]),jh=B("Star",[["polygon",{points:"12 2 15.09 8.26 22 9.27 17 14.14 18.18 21.02 12 17.77 5.82 21.02 7 14.14 2 9.27 8.91 8.26 12 2",key:"8f66p6"}]]),uv=B("Store",[["path",{d:"m2 7 4.41-4.41A2 2 0 0 1 7.83 2h8.34a2 2 0 0 1 1.42.59L22 7",key:"ztvudi"}],["path",{d:"M4 12v8a2 2 0 0 0 2 2h12a2 2 0 0 0 2-2v-8",key:"1b2hhj"}],["path",{d:"M15 22v-4a2 2 0 0 0-2-2h-2a2 2 0 0 0-2 2v4",key:"2ebpfo"}],["path",{d:"M2 7h20",key:"1fcdvo"}],["path",{d:"M22 7v3a2 2 0 0 1-2 2v0a2.7 2.7 0 0 1-1.59-.63.7.7 0 0 0-.82 0A2.7 2.7 0 0 1 16 12a2.7 2.7 0 0 1-1.59-.63.7.7 0 0 0-.82 0A2.7 2.7 0 0 1 12 12a2.7 2.7 0 0 1-1.59-.63.7.7 0 0 0-.82 0A2.7 2.7 0 0 1 8 12a2.7 2.7 0 0 1-1.59-.63.7.7 0 0 0-.82 0A2.7 2.7 0 0 1 4 12v0a2 2 0 0 1-2-2V7",key:"jon5kx"}]]),$n=B("Trash2",[["path",{d:"M3 6h18",key:"d0wm0j"}],["path",{d:"M19 6v14c0 1-1 2-2 2H7c-1 0-2-1-2-2V6",key:"4alrt4"}],["path",{d:"M8 6V4c0-1 1-2 2-2h4c1 0 2 1 2 2v2",key:"v07s0e"}],["line",{x1:"10",x2:"10",y1:"11",y2:"17",key:"1uufr5"}],["line",{x1:"14",x2:"14",y1:"11",y2:"17",key:"xtxkd"}]]),bl=B("TrendingDown",[["polyline",{points:"22 17 13.5 8.5 8.5 13.5 2 7",key:"1r2t7k"}],["polyline",{points:"16 17 22 17 22 11",key:"11uiuu"}]]),In=B("TrendingUp",[["polyline",{points:"22 7 13.5 15.5 8.5 10.5 2 17",key:"126l90"}],["polyline",{points:"16 7 22 7 22 13",key:"kwv8wd"}]]),Ei=B("Truck",[["path",{d:"M10 17h4V5H2v12h3",key:"1jq12e"}],["path",{d:"M20 17h2v-3.34a4 4 0 0 0-1.17-2.83L19 9h-5",key:"1xb3ft"}],["path",{d:"M14 17h1",key:"nufu4t"}],["circle",{cx:"7.5",cy:"17.5",r:"2.5",key:"a7aife"}],["circle",{cx:"17.5",cy:"17.5",r:"2.5",key:"1mdrzq"}]]),dv=B("Twitter",[["path",{d:"M22 4s-.7 2.1-2 3.4c1.6 10-9.4 17.3-18 11.6 2.2.1 4.4-.6 6-2C3 15.5.5 9.6 3 5c2.2 2.6 5.6 4.1 9 4-.9-4.2 4-6.6 7-3.8 1.1 0 3-1.2 3-1.2z",key:"pff0z6"}]]),Sc=B("User",[["path",{d:"M19 21v-2a4 4 0 0 0-4-4H9a4 4 0 0 0-4 4v2",key:"975kel"}],["circle",{cx:"12",cy:"7",r:"4",key:"17ys0d"}]]),Oi=B("Users",[["path",{d:"M16 21v-2a4 4 0 0 0-4-4H6a4 4 0 0 0-4 4v2",key:"1yyitq"}],["circle",{cx:"9",cy:"7",r:"4",key:"nufk8"}],["path",{d:"M22 21v-2a4 4 0 0 0-3-3.87",key:"kshegd"}],["path",{d:"M16 3.13a4 4 0 0 1 0 7.75",key:"1da9ce"}]]),fv=B("Warehouse",[["path",{d:"M22 8.35V20a2 2 0 0 1-2 2H4a2 2 0 0 1-2-2V8.35A2 2 0 0 1 3.26 6.5l8-3.2a2 2 0 0 1 1.48 0l8 3.2A2 2 0 0 1 22 8.35Z",key:"gksnxg"}],["path",{d:"M6 18h12",key:"9pbo8z"}],["path",{d:"M6 14h12",key:"4cwo0f"}],["rect",{width:"12",height:"12",x:"6",y:"10",key:"apd30q"}]]),Ai=B("X",[["path",{d:"M18 6 6 18",key:"1bl5f8"}],["path",{d:"m6 6 12 12",key:"d8bk6v"}]]),mv=()=>{const{language:e,changeLanguage:t,t:r}=oe(),[s,n]=j.useState(!1),i=[{code:"ar",name:"العربية",flag:"🇸🇦"},{code:"en",name:"English",flag:"🇺🇸"}],o=i.find(c=>c.code===e)||i[0],l=c=>{t(c),n(!1)};return a.jsxs("div",{className:"relative",children:[a.jsxs("button",{onClick:()=>n(!s),className:"flex items-center space-x-2 rtl:space-x-reverse px-3 py-2 text-sm font-medium text-gray-700 hover:text-gray-900 hover:bg-gray-100 rounded-md transition-colors duration-200","aria-label":r("header.language"),children:[a.jsx(xh,{className:"h-4 w-4"}),a.jsx("span",{className:"hidden sm:inline",children:o.flag}),a.jsx("span",{className:"hidden md:inline",children:o.name}),a.jsx(Jy,{className:`h-4 w-4 transition-transform duration-200 ${s?"rotate-180":""}`})]}),s&&a.jsxs(a.Fragment,{children:[a.jsx("div",{className:"fixed inset-0 z-10",onClick:()=>n(!1)}),a.jsx("div",{className:"absolute top-full mt-1 w-48 bg-white rounded-md shadow-lg border border-gray-200 z-20 rtl:right-0 ltr:left-0",children:a.jsx("div",{className:"py-1",children:i.map(c=>a.jsxs("button",{onClick:()=>l(c.code),className:`w-full text-left px-4 py-2 text-sm hover:bg-gray-100 transition-colors duration-200 flex items-center space-x-3 rtl:space-x-reverse ${e===c.code?"bg-gray-50 text-gray-900 font-medium":"text-gray-700"}`,children:[a.jsx("span",{className:"text-lg",children:c.flag}),a.jsx("span",{children:c.name}),e===c.code&&a.jsx("span",{className:"mr-auto rtl:mr-0 rtl:ml-auto text-green-600",children:"✓"})]},c.code))})})]})]})},hv=()=>{const[e,t]=j.useState(!1),[r,s]=j.useState(""),{isAuthenticated:n,user:i,logout:o}=at(),{itemCount:l}=Si(),{t:c,isRTL:u}=oe(),d=Dt(),f=v=>{v.preventDefault(),r.trim()&&(d(`/products?search=${encodeURIComponent(r.trim())}`),s(""))},m=()=>{o(),d("/")};return a.jsx("header",{className:"bg-white border-b border-gray-200 sticky top-0 z-50",children:a.jsxs("div",{className:"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8",children:[a.jsxs("div",{className:"flex items-center justify-between h-16",children:[a.jsx(G,{to:"/",className:"flex items-center header-logo flex-shrink-0",children:a.jsx("h1",{className:`text-2xl font-bold text-black ${u?"font-arabic":""}`,children:c("footer.companyName")})}),a.jsxs("nav",{className:`hidden md:flex items-center nav-spacing ${u?"mr-auto ml-8":"ml-auto mr-8"}`,children:[a.jsx(G,{to:"/",className:"text-gray-700 hover:text-black transition-colors",children:c("navigation.home")}),a.jsx(G,{to:"/products",className:"text-gray-700 hover:text-black transition-colors",children:c("navigation.products")}),a.jsx(G,{to:"/products?category=shirts",className:"text-gray-700 hover:text-black transition-colors",children:c("home.categories.shirts")}),a.jsx(G,{to:"/products?category=pants",className:"text-gray-700 hover:text-black transition-colors",children:c("home.categories.pants")}),a.jsx(G,{to:"/products?category=dresses",className:"text-gray-700 hover:text-black transition-colors",children:c("home.categories.dresses")})]}),a.jsx("form",{onSubmit:f,className:"hidden lg:flex items-center flex-1 max-w-md mx-4",children:a.jsxs("div",{className:"relative w-full",children:[a.jsx("input",{type:"text",value:r,onChange:v=>s(v.target.value),placeholder:c("header.searchPlaceholder"),className:`w-full py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-black focus:border-transparent transition-all ${u?"pr-10 pl-4 text-right font-arabic":"pl-10 pr-4 text-left"}`}),a.jsx(js,{className:`absolute top-2.5 h-5 w-5 text-gray-400 ${u?"right-3":"left-3"}`})]})}),a.jsxs("div",{className:`flex items-center flex-shrink-0 ${u?"space-x-reverse space-x-3":"space-x-3"}`,children:[a.jsx(mv,{}),a.jsxs("div",{className:"relative group",children:[a.jsx("button",{className:"flex items-center text-gray-700 hover:text-black transition-colors",children:a.jsx(Sc,{className:"h-6 w-6"})}),a.jsx("div",{className:`absolute mt-2 w-48 bg-white border border-gray-200 rounded-md shadow-lg opacity-0 invisible group-hover:opacity-100 group-hover:visible transition-all duration-200 ${u?"left-0":"right-0"}`,children:n?a.jsxs("div",{className:"py-1",children:[a.jsxs("div",{className:"px-4 py-2 text-sm text-gray-700 border-b",children:[i==null?void 0:i.firstName," ",i==null?void 0:i.lastName]}),a.jsx(G,{to:"/profile",className:"block px-4 py-2 text-sm text-gray-700 hover:bg-gray-100",children:c("navigation.profile")}),a.jsx(G,{to:"/orders",className:"block px-4 py-2 text-sm text-gray-700 hover:bg-gray-100",children:c("navigation.orders")}),(i==null?void 0:i.role)==="customer"&&a.jsx(G,{to:"/customer",className:"block px-4 py-2 text-sm text-gray-700 hover:bg-gray-100",children:c("navigation.dashboard")}),(i==null?void 0:i.role)==="admin"&&a.jsx(G,{to:"/admin",className:"block px-4 py-2 text-sm text-gray-700 hover:bg-gray-100",children:c("navigation.dashboard")}),a.jsx("button",{onClick:m,className:`block w-full px-4 py-2 text-sm text-gray-700 hover:bg-gray-100 ${u?"text-right":"text-left"}`,children:c("navigation.logout")})]}):a.jsxs("div",{className:"py-1",children:[a.jsx(G,{to:"/login",className:"block px-4 py-2 text-sm text-gray-700 hover:bg-gray-100",children:c("navigation.login")}),a.jsx(G,{to:"/register",className:"block px-4 py-2 text-sm text-gray-700 hover:bg-gray-100",children:c("navigation.register")})]})})]}),n&&a.jsx(G,{to:"/wishlist",className:"text-gray-700 hover:text-black transition-colors",children:a.jsx(Fn,{className:"h-6 w-6"})}),a.jsxs(G,{to:"/cart",className:"relative text-gray-700 hover:text-black transition-colors",children:[a.jsx(Es,{className:"h-6 w-6"}),l>0&&a.jsx("span",{className:`absolute -top-2 bg-black text-white text-xs rounded-full h-5 w-5 flex items-center justify-center ${u?"-left-2":"-right-2"}`,children:l})]}),a.jsx("button",{onClick:()=>t(!e),className:"md:hidden text-gray-700 hover:text-black transition-colors",children:e?a.jsx(Ai,{className:"h-6 w-6"}):a.jsx(jc,{className:"h-6 w-6"})})]})]}),e&&a.jsxs("div",{className:"md:hidden border-t border-gray-200",children:[a.jsxs("div",{className:"px-2 pt-2 pb-3 space-y-1",children:[a.jsx(G,{to:"/",className:"block px-3 py-2 text-gray-700 hover:text-black transition-colors",onClick:()=>t(!1),children:c("navigation.home")}),a.jsx(G,{to:"/products",className:"block px-3 py-2 text-gray-700 hover:text-black transition-colors",onClick:()=>t(!1),children:c("navigation.products")}),a.jsx(G,{to:"/products?category=shirts",className:"block px-3 py-2 text-gray-700 hover:text-black transition-colors",onClick:()=>t(!1),children:c("home.categories.shirts")}),a.jsx(G,{to:"/products?category=pants",className:"block px-3 py-2 text-gray-700 hover:text-black transition-colors",onClick:()=>t(!1),children:c("home.categories.pants")}),a.jsx(G,{to:"/products?category=dresses",className:"block px-3 py-2 text-gray-700 hover:text-black transition-colors",onClick:()=>t(!1),children:c("home.categories.dresses")})]}),a.jsx("div",{className:"px-2 pb-3",children:a.jsx("form",{onSubmit:f,children:a.jsxs("div",{className:"relative",children:[a.jsx("input",{type:"text",value:r,onChange:v=>s(v.target.value),placeholder:c("header.searchPlaceholder"),className:`w-full py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-black focus:border-transparent ${u?"pr-10 pl-4 text-right":"pl-10 pr-4 text-left"}`}),a.jsx(js,{className:`absolute top-2.5 h-5 w-5 text-gray-400 ${u?"right-3":"left-3"}`})]})})})]})]})})},pv=()=>{const{t:e,isRTL:t}=oe();return a.jsx("footer",{className:"bg-black text-white",children:a.jsxs("div",{className:"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-12",children:[a.jsxs("div",{className:"grid grid-cols-1 md:grid-cols-4 gap-8",children:[a.jsxs("div",{className:"col-span-1 md:col-span-2",children:[a.jsx("h3",{className:"text-2xl font-bold mb-4",children:e("footer.companyName")}),a.jsx("p",{className:"text-gray-300 mb-4 max-w-md",children:e("footer.description")}),a.jsxs("div",{className:`flex space-x-4 ${t?"rtl:space-x-reverse":""}`,children:[a.jsx("a",{href:"#",className:"text-gray-300 hover:text-white transition-colors",children:a.jsx(Xy,{className:"h-6 w-6"})}),a.jsx("a",{href:"#",className:"text-gray-300 hover:text-white transition-colors",children:a.jsx(dv,{className:"h-6 w-6"})}),a.jsx("a",{href:"#",className:"text-gray-300 hover:text-white transition-colors",children:a.jsx(rv,{className:"h-6 w-6"})})]})]}),a.jsxs("div",{children:[a.jsx("h4",{className:"text-lg font-semibold mb-4",children:e("footer.quickLinks")}),a.jsxs("ul",{className:"space-y-2",children:[a.jsx("li",{children:a.jsx(G,{to:"/",className:"text-gray-300 hover:text-white transition-colors",children:e("navigation.home")})}),a.jsx("li",{children:a.jsx(G,{to:"/products",className:"text-gray-300 hover:text-white transition-colors",children:e("navigation.products")})}),a.jsx("li",{children:a.jsx(G,{to:"/products?category=shirts",className:"text-gray-300 hover:text-white transition-colors",children:e("home.categories.shirts")})}),a.jsx("li",{children:a.jsx(G,{to:"/products?category=pants",className:"text-gray-300 hover:text-white transition-colors",children:e("home.categories.pants")})}),a.jsx("li",{children:a.jsx(G,{to:"/products?category=dresses",className:"text-gray-300 hover:text-white transition-colors",children:e("home.categories.dresses")})})]})]}),a.jsxs("div",{children:[a.jsx("h4",{className:"text-lg font-semibold mb-4",children:e("footer.customerService")}),a.jsxs("ul",{className:"space-y-2",children:[a.jsx("li",{children:a.jsx("a",{href:"#",className:"text-gray-300 hover:text-white transition-colors",children:e("common.contact")})}),a.jsx("li",{children:a.jsx("a",{href:"#",className:"text-gray-300 hover:text-white transition-colors",children:e("productDetail.shipping")})}),a.jsx("li",{children:a.jsx("a",{href:"#",className:"text-gray-300 hover:text-white transition-colors",children:e("footer.returnsExchanges")})}),a.jsx("li",{children:a.jsx("a",{href:"#",className:"text-gray-300 hover:text-white transition-colors",children:e("footer.sizeGuide")})}),a.jsx("li",{children:a.jsx("a",{href:"#",className:"text-gray-300 hover:text-white transition-colors",children:e("footer.faq")})})]})]})]}),a.jsx("div",{className:"border-t border-gray-800 mt-8 pt-8",children:a.jsxs("div",{className:"grid grid-cols-1 md:grid-cols-3 gap-4",children:[a.jsxs("div",{className:`flex items-center ${t?"rtl:space-x-reverse":""}`,children:[a.jsx(vh,{className:`h-5 w-5 text-gray-300 ${t?"ml-2":"mr-2"}`}),a.jsx("span",{className:"text-gray-300",children:e("footer.email")})]}),a.jsxs("div",{className:`flex items-center ${t?"rtl:space-x-reverse":""}`,children:[a.jsx(ov,{className:`h-5 w-5 text-gray-300 ${t?"ml-2":"mr-2"}`}),a.jsx("span",{className:"text-gray-300",children:e("footer.phone")})]}),a.jsxs("div",{className:`flex items-center ${t?"rtl:space-x-reverse":""}`,children:[a.jsx(wr,{className:`h-5 w-5 text-gray-300 ${t?"ml-2":"mr-2"}`}),a.jsx("span",{className:"text-gray-300",children:e("footer.address")})]})]})}),a.jsxs("div",{className:`border-t border-gray-800 mt-8 pt-8 flex flex-col md:flex-row justify-between items-center ${t?"rtl:space-x-reverse":""}`,children:[a.jsxs("p",{className:"text-gray-300 text-sm",children:["© 2024 ",e("footer.companyName"),". ",e("footer.allRightsReserved")]}),a.jsxs("div",{className:"flex space-x-6 mt-4 md:mt-0",children:[a.jsx("a",{href:"#",className:"text-gray-300 hover:text-white text-sm transition-colors",children:"Privacy Policy"}),a.jsx("a",{href:"#",className:"text-gray-300 hover:text-white text-sm transition-colors",children:"Terms of Service"}),a.jsx("a",{href:"#",className:"text-gray-300 hover:text-white text-sm transition-colors",children:"Cookie Policy"})]})]})]})})},he=({variant:e="primary",size:t="md",isLoading:r=!1,children:s,className:n="",disabled:i,...o})=>{const l="inline-flex items-center justify-center font-medium transition-colors duration-200 focus:outline-none focus:ring-2 focus:ring-offset-2 disabled:opacity-50 disabled:cursor-not-allowed",c={primary:"bg-black text-white hover:bg-gray-800 focus:ring-black",secondary:"bg-white text-black border border-black hover:bg-gray-50 focus:ring-black",outline:"bg-transparent text-black border border-gray-300 hover:bg-gray-50 focus:ring-gray-500",ghost:"bg-transparent text-gray-700 hover:bg-gray-100 focus:ring-gray-500"},u={sm:"px-3 py-2 text-sm rounded-md",md:"px-4 py-2 text-base rounded-md",lg:"px-6 py-3 text-lg rounded-lg"},d=`${l} ${c[e]} ${u[t]} ${n}`;return a.jsx("button",{className:d,disabled:i||r,...o,children:r?a.jsxs(a.Fragment,{children:[a.jsxs("svg",{className:"animate-spin -ml-1 mr-3 h-5 w-5",xmlns:"http://www.w3.org/2000/svg",fill:"none",viewBox:"0 0 24 24",children:[a.jsx("circle",{className:"opacity-25",cx:"12",cy:"12",r:"10",stroke:"currentColor",strokeWidth:"4"}),a.jsx("path",{className:"opacity-75",fill:"currentColor",d:"M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"})]}),"Loading..."]}):s})},bh=({product:e,onAddToWishlist:t,isInWishlist:r=!1})=>{const{t:s,isRTL:n}=oe(),i=l=>`${l.toLocaleString()} ${s("currency.sar")}`,o=l=>Array.from({length:5},(c,u)=>a.jsx(jh,{className:`h-4 w-4 ${u<Math.floor(l)?"text-yellow-400 fill-current":"text-gray-300"}`},u));return a.jsxs("div",{className:`group relative bg-white border border-gray-200 rounded-lg overflow-hidden hover:shadow-lg transition-shadow duration-300 w-full ${n?"text-right":"text-left"}`,children:[a.jsxs("div",{className:"relative aspect-square overflow-hidden",children:[a.jsx(G,{to:`/products/${e.id}`,children:a.jsx("img",{src:e.images[0],alt:e.name,className:"w-full h-full object-cover group-hover:scale-105 transition-transform duration-300"})}),e.originalPrice&&e.originalPrice>e.price&&a.jsxs("div",{className:"absolute top-2 left-2 bg-black text-white px-2 py-1 text-xs font-medium rounded",children:[Math.round((e.originalPrice-e.price)/e.originalPrice*100),"% OFF"]}),t&&a.jsx("button",{onClick:()=>t(e.id),className:`absolute top-2 right-2 p-2 rounded-full transition-colors ${r?"bg-red-500 text-white":"bg-white text-gray-600 hover:bg-gray-100"}`,children:a.jsx(Fn,{className:`h-4 w-4 ${r?"fill-current":""}`})}),a.jsx("div",{className:"absolute bottom-2 left-2 right-2 opacity-0 group-hover:opacity-100 transition-opacity duration-300",children:a.jsx(G,{to:`/products/${e.id}`,children:a.jsx(he,{className:"w-full",size:"sm",children:s("products.viewDetails")})})})]}),a.jsxs("div",{className:"p-4",children:[a.jsx(G,{to:`/products/${e.id}`,children:a.jsx("h3",{className:`text-lg font-medium text-gray-900 hover:text-black transition-colors line-clamp-2 ${n?"font-arabic":""}`,children:n&&e.nameAr?e.nameAr:e.name})}),a.jsx("p",{className:`text-sm text-gray-600 mt-1 line-clamp-2 ${n?"font-arabic":""}`,children:n&&e.descriptionAr?e.descriptionAr:e.description}),a.jsxs("div",{className:"flex items-center mt-2",children:[a.jsx("div",{className:"flex items-center",children:o(e.rating)}),a.jsxs("span",{className:"ml-2 text-sm text-gray-600",children:["(",e.reviewCount,")"]})]}),a.jsxs("div",{className:"flex items-center justify-between mt-3",children:[a.jsxs("div",{className:"flex items-center space-x-2",children:[a.jsx("span",{className:"text-lg font-bold text-black",children:i(e.price)}),e.originalPrice&&e.originalPrice>e.price&&a.jsx("span",{className:"text-sm text-gray-500 line-through",children:i(e.originalPrice)})]}),a.jsx("div",{className:"text-sm",children:e.stock>0?a.jsx("span",{className:"text-green-600",children:s("products.inStock")}):a.jsx("span",{className:"text-red-600",children:s("products.outOfStock")})})]}),a.jsx("div",{className:"mt-3",children:a.jsxs("div",{className:"flex flex-wrap gap-1",children:[e.variants.slice(0,4).map(l=>a.jsx("span",{className:"px-2 py-1 text-xs border border-gray-300 rounded",children:l.size},l.id)),e.variants.length>4&&a.jsxs("span",{className:"px-2 py-1 text-xs text-gray-500",children:["+",e.variants.length-4," ",s("products.more")]})]})})]})]})},gv=()=>{const{featuredProducts:e,isLoading:t}=Ci(),{t:r,isRTL:s}=oe();return a.jsxs("div",{className:"min-h-screen",children:[a.jsx("section",{className:"relative bg-gray-50 py-20",children:a.jsx("div",{className:"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8",children:a.jsxs("div",{className:`text-center ${s?"font-arabic":""}`,children:[a.jsxs("h1",{className:"text-4xl md:text-6xl font-bold text-black mb-6",children:[r("home.heroTitle"),a.jsx("br",{}),a.jsx("span",{className:"text-gray-600",children:r("home.heroSubtitle")})]}),a.jsx("p",{className:"text-xl text-gray-600 mb-8 max-w-2xl mx-auto",children:r("home.heroDescription")}),a.jsxs("div",{className:`flex flex-col sm:flex-row gap-4 justify-center ${s?"rtl:space-x-reverse":""}`,children:[a.jsx(G,{to:"/products",children:a.jsxs(he,{size:"lg",className:`w-full sm:w-auto flex items-center justify-center ${s?"flex-row-reverse":""}`,children:[r("home.shopNow"),a.jsx(sd,{className:`h-5 w-5 ${s?"mr-2 rotate-180":"ml-2"}`})]})}),a.jsx(G,{to:"/products?featured=true",children:a.jsx(he,{variant:"secondary",size:"lg",className:"w-full sm:w-auto",children:r("home.viewFeatured")})})]})]})})}),a.jsx("section",{className:"py-16",children:a.jsxs("div",{className:"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8",children:[a.jsxs("div",{className:`text-center mb-12 ${s?"font-arabic":""}`,children:[a.jsx("h2",{className:"text-3xl font-bold text-black mb-4",children:r("home.featuredProducts")}),a.jsx("p",{className:"text-gray-600 max-w-2xl mx-auto",children:r("home.featuredDescription")})]}),t?a.jsx("div",{className:"grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-4 gap-6 justify-items-center",children:Array.from({length:4}).map((n,i)=>a.jsxs("div",{className:"animate-pulse w-full max-w-sm",children:[a.jsx("div",{className:"bg-gray-300 aspect-square rounded-lg mb-4"}),a.jsx("div",{className:"h-4 bg-gray-300 rounded mb-2"}),a.jsx("div",{className:"h-4 bg-gray-300 rounded w-3/4"})]},i))}):a.jsx("div",{className:`grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-4 gap-6 justify-items-center ${s?"rtl":"ltr"}`,children:e.slice(0,4).map(n=>a.jsx("div",{className:"w-full max-w-sm",children:a.jsx(bh,{product:n})},n.id))}),a.jsx("div",{className:"text-center mt-12",children:a.jsx(G,{to:"/products",children:a.jsxs(he,{variant:"outline",size:"lg",children:[r("home.viewAllProducts"),a.jsx(sd,{className:"ml-2 h-5 w-5"})]})})})]})}),a.jsx("section",{className:"py-16 bg-gray-50",children:a.jsxs("div",{className:"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8",children:[a.jsxs("div",{className:`text-center mb-12 ${s?"font-arabic":""}`,children:[a.jsx("h2",{className:"text-3xl font-bold text-black mb-4",children:r("home.shopByCategory")}),a.jsx("p",{className:"text-gray-600",children:r("home.categoryDescription")})]}),a.jsxs("div",{className:"grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 gap-6 lg:gap-8",children:[a.jsx(G,{to:"/products?category=shirts",className:"group",children:a.jsxs("div",{className:"relative overflow-hidden rounded-lg bg-white shadow-md hover:shadow-lg transition-shadow",children:[a.jsx("img",{src:"https://images.unsplash.com/photo-1596755094514-f87e34085b2c?w=400&h=300&fit=crop",alt:"Shirts",className:"w-full h-64 object-cover group-hover:scale-105 transition-transform duration-300"}),a.jsx("div",{className:"absolute inset-0 bg-black bg-opacity-40 flex items-center justify-center",children:a.jsx("h3",{className:`text-white text-2xl font-bold ${s?"font-arabic":""}`,children:r("home.categories.shirts")})})]})}),a.jsx(G,{to:"/products?category=pants",className:"group",children:a.jsxs("div",{className:"relative overflow-hidden rounded-lg bg-white shadow-md hover:shadow-lg transition-shadow",children:[a.jsx("img",{src:"https://images.unsplash.com/photo-1542272604-787c3835535d?w=400&h=300&fit=crop",alt:"Pants",className:"w-full h-64 object-cover group-hover:scale-105 transition-transform duration-300"}),a.jsx("div",{className:"absolute inset-0 bg-black bg-opacity-40 flex items-center justify-center",children:a.jsx("h3",{className:`text-white text-2xl font-bold ${s?"font-arabic":""}`,children:r("home.categories.pants")})})]})}),a.jsx(G,{to:"/products?category=dresses",className:"group",children:a.jsxs("div",{className:"relative overflow-hidden rounded-lg bg-white shadow-md hover:shadow-lg transition-shadow",children:[a.jsx("img",{src:"https://images.unsplash.com/photo-1595777457583-95e059d581b8?w=400&h=300&fit=crop",alt:"Dresses",className:"w-full h-64 object-cover group-hover:scale-105 transition-transform duration-300"}),a.jsx("div",{className:"absolute inset-0 bg-black bg-opacity-40 flex items-center justify-center",children:a.jsx("h3",{className:`text-white text-2xl font-bold ${s?"font-arabic":""}`,children:r("home.categories.dresses")})})]})})]})]})}),a.jsx("section",{className:"py-16",children:a.jsx("div",{className:"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8",children:a.jsxs("div",{className:"grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-4 gap-6 lg:gap-8",children:[a.jsxs("div",{className:`text-center ${s?"font-arabic":""}`,children:[a.jsx("div",{className:"bg-black text-white w-16 h-16 rounded-full flex items-center justify-center mx-auto mb-4",children:a.jsx(Ei,{className:"h-8 w-8"})}),a.jsx("h3",{className:"text-lg font-semibold mb-2",children:r("home.features.freeShipping")}),a.jsx("p",{className:"text-gray-600",children:r("home.features.freeShippingDesc")})]}),a.jsxs("div",{className:`text-center ${s?"font-arabic":""}`,children:[a.jsx("div",{className:"bg-black text-white w-16 h-16 rounded-full flex items-center justify-center mx-auto mb-4",children:a.jsx(Nh,{className:"h-8 w-8"})}),a.jsx("h3",{className:"text-lg font-semibold mb-2",children:r("home.features.securePayment")}),a.jsx("p",{className:"text-gray-600",children:r("home.features.securePaymentDesc")})]}),a.jsxs("div",{className:`text-center ${s?"font-arabic":""}`,children:[a.jsx("div",{className:"bg-black text-white w-16 h-16 rounded-full flex items-center justify-center mx-auto mb-4",children:a.jsx(lv,{className:"h-8 w-8"})}),a.jsx("h3",{className:"text-lg font-semibold mb-2",children:r("home.features.easyReturns")}),a.jsx("p",{className:"text-gray-600",children:r("home.features.easyReturnsDesc")})]}),a.jsxs("div",{className:`text-center ${s?"font-arabic":""}`,children:[a.jsx("div",{className:"bg-black text-white w-16 h-16 rounded-full flex items-center justify-center mx-auto mb-4",children:a.jsx(ev,{className:"h-8 w-8"})}),a.jsx("h3",{className:"text-lg font-semibold mb-2",children:r("home.features.support")}),a.jsx("p",{className:"text-gray-600",children:r("home.features.supportDesc")})]})]})})})]})},xv=()=>{const[e]=S0(),[t,r]=j.useState("grid"),[s,n]=j.useState("name"),[i,o]=j.useState([]),{products:l,isLoading:c}=Ci(),u=e.get("category"),d=e.get("search"),f=e.get("featured");j.useEffect(()=>{let v=[...l];if(u&&(v=v.filter(g=>g.category===u)),d){const g=d.toLowerCase();v=v.filter(w=>w.name.toLowerCase().includes(g)||w.description.toLowerCase().includes(g)||w.tags.some(y=>y.toLowerCase().includes(g)))}f==="true"&&(v=v.filter(g=>g.featured)),v.sort((g,w)=>{switch(s){case"price-low":return g.price-w.price;case"price-high":return w.price-g.price;case"rating":return w.rating-g.rating;case"newest":return new Date(w.createdAt).getTime()-new Date(g.createdAt).getTime();default:return g.name.localeCompare(w.name)}}),o(v)},[l,u,d,f,s]);const m=()=>d?`Search results for "${d}"`:u?u.charAt(0).toUpperCase()+u.slice(1):f?"Featured Products":"All Products";return c?a.jsx("div",{className:"min-h-screen flex items-center justify-center",children:a.jsx("div",{className:"animate-spin rounded-full h-32 w-32 border-b-2 border-black"})}):a.jsxs("div",{className:"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8",children:[a.jsxs("div",{className:"flex flex-col md:flex-row md:items-center md:justify-between mb-8",children:[a.jsxs("div",{children:[a.jsx("h1",{className:"text-3xl font-bold text-black mb-2",children:m()}),a.jsxs("p",{className:"text-gray-600",children:[i.length," product",i.length!==1?"s":""," found"]})]}),a.jsxs("div",{className:"flex items-center space-x-4 mt-4 md:mt-0",children:[a.jsxs("select",{value:s,onChange:v=>n(v.target.value),className:"border border-gray-300 rounded-md px-3 py-2 focus:outline-none focus:ring-2 focus:ring-black",children:[a.jsx("option",{value:"name",children:"Sort by Name"}),a.jsx("option",{value:"price-low",children:"Price: Low to High"}),a.jsx("option",{value:"price-high",children:"Price: High to Low"}),a.jsx("option",{value:"rating",children:"Highest Rated"}),a.jsx("option",{value:"newest",children:"Newest First"})]}),a.jsxs("div",{className:"flex border border-gray-300 rounded-md",children:[a.jsx("button",{onClick:()=>r("grid"),className:`p-2 ${t==="grid"?"bg-black text-white":"text-gray-600"}`,children:a.jsx(Zy,{className:"h-5 w-5"})}),a.jsx("button",{onClick:()=>r("list"),className:`p-2 ${t==="list"?"bg-black text-white":"text-gray-600"}`,children:a.jsx(nv,{className:"h-5 w-5"})})]})]})]}),i.length===0?a.jsxs("div",{className:"text-center py-12",children:[a.jsx("p",{className:"text-gray-500 text-lg mb-4",children:"No products found"}),a.jsx(he,{variant:"outline",children:a.jsx("a",{href:"/products",children:"View All Products"})})]}):a.jsx("div",{className:`grid gap-6 ${t==="grid"?"grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 xl:grid-cols-4":"grid-cols-1"}`,children:i.map(v=>a.jsx(bh,{product:v},v.id))})]})},yv=({isOpen:e,onClose:t,product:r,selectedVariant:s,quantity:n,onAddToCart:i,onBuyNow:o})=>{const{t:l,isRTL:c}=oe();if(!e)return null;const u=f=>`${f.toLocaleString()} ${l("currency.sar")}`,d=()=>{const f=c&&r.nameAr?r.nameAr:r.name,m=s?`${s.size} - ${s.color}`:"",g=`https://wa.me/966111234567?text=${encodeURIComponent(`${l("whatsapp.inquiry")}

${l("products.product")}: ${f}
${l("products.variant")}: ${m}
${l("products.quantity")}: ${n}
${l("products.price")}: ${u(r.price*n)}

${l("whatsapp.pleaseProvideInfo")}`)}`;window.open(g,"_blank"),t()};return a.jsx("div",{className:"fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50 p-4",children:a.jsxs("div",{className:`bg-white rounded-lg max-w-md w-full p-6 relative modal-content ${c?"font-arabic text-right":"text-left"}`,children:[a.jsx("button",{onClick:t,className:`absolute top-4 ${c?"left-4":"right-4"} text-gray-400 hover:text-gray-600`,children:a.jsx(Ai,{className:"h-6 w-6"})}),a.jsxs("div",{className:"mb-6",children:[a.jsxs("div",{className:`flex items-center mb-4 ${c?"space-x-reverse space-x-4":"space-x-4"}`,children:[a.jsx("img",{src:r.images[0],alt:r.name,className:"w-16 h-16 object-cover rounded-lg flex-shrink-0"}),a.jsxs("div",{className:"flex-1 min-w-0",children:[a.jsx("h3",{className:`font-semibold text-lg truncate ${c?"font-arabic":""}`,children:c&&r.nameAr?r.nameAr:r.name}),s&&a.jsxs("p",{className:"text-sm text-gray-600",children:[s.size," - ",s.color]}),a.jsxs("p",{className:"text-sm text-gray-600",children:[l("products.quantity"),": ",n]})]})]}),a.jsx("div",{className:"text-xl font-bold text-black",children:u(r.price*n)})]}),a.jsxs("div",{className:"space-y-3",children:[a.jsx("h4",{className:`font-semibold mb-4 ${c?"font-arabic":""}`,children:l("purchase.chooseOption")}),a.jsxs(he,{onClick:()=>{i(),t()},className:`w-full flex items-center justify-center space-x-2 ${c?"flex-row-reverse space-x-reverse":""}`,variant:"secondary",children:[a.jsx(Es,{className:"h-5 w-5"}),a.jsx("span",{children:l("cart.addToCart")})]}),a.jsxs(he,{onClick:d,className:`w-full flex items-center justify-center space-x-2 bg-green-600 hover:bg-green-700 text-white ${c?"flex-row-reverse space-x-reverse":""}`,children:[a.jsx(iv,{className:"h-5 w-5"}),a.jsx("span",{children:l("whatsapp.inquiry")})]}),a.jsxs(he,{onClick:()=>{o(),t()},className:`w-full flex items-center justify-center space-x-2 ${c?"flex-row-reverse space-x-reverse":""}`,children:[a.jsx(_n,{className:"h-5 w-5"}),a.jsx("span",{children:l("purchase.buyNow")})]})]})]})})},vv=()=>{var k;const{id:e}=Yx(),t=Dt(),{products:r}=Ci(),{addToCart:s}=Si(),{t:n,isRTL:i}=oe(),[o,l]=j.useState(null),[c,u]=j.useState(null),[d,f]=j.useState(1),[m,v]=j.useState(0),[g,w]=j.useState(!1);if(j.useEffect(()=>{if(e&&r.length>0){const E=r.find(O=>O.id===e);E&&(l(E),u(E.variants[0]||null))}},[e,r]),!o)return a.jsx("div",{className:"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8",children:a.jsxs("div",{className:"text-center",children:[a.jsx("h1",{className:"text-2xl font-bold text-gray-900",children:n("products.notFound")}),a.jsx(he,{onClick:()=>t("/products"),className:"mt-4",children:n("navigation.backToProducts")})]})});const y=()=>{if(!c){Q.error(n("products.selectVariant"));return}s(o,c,d),Q.success(n("cart.addedToCart"))},p=()=>{if(!c){Q.error(n("products.selectVariant"));return}s(o,c,d),t("/checkout")},h=()=>{if(!c){Q.error(n("products.selectVariant"));return}w(!0)},x=E=>`${E.toLocaleString()} ${n("currency.sar")}`,b=E=>Array.from({length:5},(O,$)=>a.jsx(jh,{className:`h-5 w-5 ${$<Math.floor(E)?"text-yellow-400 fill-current":"text-gray-300"}`},$));return a.jsxs("div",{className:`max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8 ${i?"font-arabic":""}`,children:[a.jsxs("button",{onClick:()=>t(-1),className:`flex items-center text-gray-600 hover:text-black mb-8 ${i?"flex-row-reverse":""}`,children:[a.jsx(Xa,{className:`h-5 w-5 ${i?"ml-2 rotate-180":"mr-2"}`}),n("navigation.back")]}),a.jsxs("div",{className:"grid grid-cols-1 lg:grid-cols-2 gap-12",children:[a.jsxs("div",{className:"space-y-4",children:[a.jsx("div",{className:"aspect-square overflow-hidden rounded-lg bg-gray-100",children:a.jsx("img",{src:o.images[m],alt:i&&o.nameAr?o.nameAr:o.name,className:"w-full h-full object-cover"})}),o.images.length>1&&a.jsx("div",{className:"grid grid-cols-4 gap-4",children:o.images.map((E,O)=>a.jsx("button",{onClick:()=>v(O),className:`aspect-square overflow-hidden rounded-lg border-2 ${m===O?"border-black":"border-gray-200"}`,children:a.jsx("img",{src:E,alt:`${o.name} ${O+1}`,className:"w-full h-full object-cover"})},O))})]}),a.jsxs("div",{className:"space-y-6",children:[a.jsxs("div",{children:[a.jsx("h1",{className:"text-3xl font-bold text-gray-900",children:i&&o.nameAr?o.nameAr:o.name}),a.jsx("p",{className:"text-lg text-gray-600 mt-2",children:i&&o.descriptionAr?o.descriptionAr:o.description})]}),a.jsxs("div",{className:"flex items-center space-x-4",children:[a.jsx("div",{className:"flex items-center",children:b(o.rating)}),a.jsxs("span",{className:"text-sm text-gray-600",children:[o.rating," (",o.reviewCount," ",n("products.reviews"),")"]})]}),a.jsxs("div",{className:"flex items-center space-x-4",children:[a.jsx("span",{className:"text-3xl font-bold text-black",children:x(o.price)}),o.originalPrice&&o.originalPrice>o.price&&a.jsxs(a.Fragment,{children:[a.jsx("span",{className:"text-xl text-gray-500 line-through",children:x(o.originalPrice)}),a.jsxs("span",{className:"bg-red-100 text-red-800 px-2 py-1 rounded text-sm font-medium",children:[Math.round((o.originalPrice-o.price)/o.originalPrice*100),"% ",n("products.off")]})]})]}),o.variants.length>0&&a.jsxs("div",{children:[a.jsx("h3",{className:"text-lg font-medium text-gray-900 mb-3",children:n("products.selectSize")}),a.jsx("div",{className:"grid grid-cols-4 gap-3",children:o.variants.map(E=>a.jsxs("button",{onClick:()=>u(E),disabled:E.stock===0,className:`p-3 border rounded-lg text-center transition-colors ${(c==null?void 0:c.id)===E.id?"border-black bg-black text-white":E.stock===0?"border-gray-200 bg-gray-100 text-gray-400 cursor-not-allowed":"border-gray-300 hover:border-gray-400"}`,children:[a.jsx("div",{className:"font-medium",children:E.size}),a.jsx("div",{className:"text-sm",children:E.color})]},E.id))})]}),a.jsxs("div",{children:[a.jsx("h3",{className:"text-lg font-medium text-gray-900 mb-3",children:n("products.quantity")}),a.jsxs("div",{className:"flex items-center space-x-4",children:[a.jsxs("div",{className:"flex items-center border border-gray-300 rounded-lg",children:[a.jsx("button",{onClick:()=>f(Math.max(1,d-1)),className:"p-2 hover:bg-gray-100",children:a.jsx(wh,{className:"h-4 w-4"})}),a.jsx("span",{className:"px-4 py-2 font-medium",children:d}),a.jsx("button",{onClick:()=>f(d+1),className:"p-2 hover:bg-gray-100",children:a.jsx(Ur,{className:"h-4 w-4"})})]}),c&&a.jsxs("span",{className:"text-sm text-gray-600",children:[c.stock," ",n("products.inStock")]})]})]}),a.jsxs("div",{className:"space-y-4",children:[a.jsxs(he,{onClick:h,disabled:!c||c.stock===0,className:"w-full",size:"lg",children:[a.jsx(Es,{className:`h-5 w-5 ${i?"ml-2":"mr-2"}`}),n("purchase.chooseOption")]}),a.jsxs(he,{variant:"outline",className:"w-full",size:"lg",children:[a.jsx(Fn,{className:`h-5 w-5 ${i?"ml-2":"mr-2"}`}),n("products.addToWishlist")]})]}),a.jsxs("div",{className:"border-t pt-6",children:[a.jsx("h3",{className:"text-lg font-medium text-gray-900 mb-4",children:n("products.details")}),a.jsxs("div",{className:"space-y-2 text-sm text-gray-600",children:[a.jsxs("div",{className:"flex justify-between",children:[a.jsxs("span",{children:[n("products.category"),":"]}),a.jsx("span",{className:"capitalize",children:o.category})]}),a.jsxs("div",{className:"flex justify-between",children:[a.jsxs("span",{children:[n("products.sku"),":"]}),a.jsx("span",{children:(c==null?void 0:c.sku)||"N/A"})]}),a.jsxs("div",{className:"flex justify-between",children:[a.jsxs("span",{children:[n("products.tags"),":"]}),a.jsx("span",{children:((k=o.tags)==null?void 0:k.join(", "))||n("common.none")})]})]})]})]})]}),a.jsx(yv,{isOpen:g,onClose:()=>w(!1),product:o,selectedVariant:c,quantity:d,onAddToCart:y,onBuyNow:p})]})},wv=()=>{const{items:e,total:t,itemCount:r,removeFromCart:s,updateQuantity:n,clearCart:i}=Si(),{t:o,isRTL:l}=oe(),c=Dt(),u=(y,p)=>{p<1?(s(y),Q.success(o("cart.itemRemoved"))):n(y,p)},d=y=>{s(y),Q.success(o("cart.itemRemoved"))},f=()=>{window.confirm(o("cart.confirmClear"))&&(i(),Q.success(o("cart.cartCleared")))},m=y=>`${y.toLocaleString()} ${l?"ر.س":"SAR"}`,v=t>500?0:25,g=t*.15,w=t+v+g;return e.length===0?a.jsx("div",{className:"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8",children:a.jsxs("div",{className:"text-center py-16",children:[a.jsx(kc,{className:"mx-auto h-24 w-24 text-gray-300 mb-4"}),a.jsx("h2",{className:`text-2xl font-bold text-gray-900 mb-4 ${l?"font-arabic":""}`,children:l?"سلة التسوق فارغة":"Your cart is empty"}),a.jsx("p",{className:`text-gray-600 mb-8 ${l?"font-arabic":""}`,children:l?"ابدأ بإضافة المنتجات إلى سلة التسوق":"Start adding products to your cart"}),a.jsxs(he,{onClick:()=>c("/products"),children:[a.jsx(Xa,{className:`h-4 w-4 ${l?"ml-2 rotate-180":"mr-2"}`}),l?"متابعة التسوق":"Continue Shopping"]})]})}):a.jsxs("div",{className:"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8",children:[a.jsxs("div",{className:`flex items-center justify-between mb-8 ${l?"flex-row-reverse":""}`,children:[a.jsxs("div",{children:[a.jsx("h1",{className:`text-3xl font-bold text-gray-900 ${l?"font-arabic":""}`,children:l?"سلة التسوق":"Shopping Cart"}),a.jsx("p",{className:`text-gray-600 mt-2 ${l?"font-arabic":""}`,children:l?`${r} منتج في السلة`:`${r} items in cart`})]}),a.jsxs("div",{className:`flex items-center space-x-4 ${l?"space-x-reverse":""}`,children:[a.jsxs(he,{variant:"outline",onClick:()=>c("/products"),children:[a.jsx(Xa,{className:`h-4 w-4 ${l?"ml-2 rotate-180":"mr-2"}`}),l?"متابعة التسوق":"Continue Shopping"]}),e.length>0&&a.jsxs(he,{variant:"outline",onClick:f,className:"text-red-600 border-red-600 hover:bg-red-50",children:[a.jsx($n,{className:`h-4 w-4 ${l?"ml-2":"mr-2"}`}),l?"إفراغ السلة":"Clear Cart"]})]})]}),a.jsxs("div",{className:"grid grid-cols-1 lg:grid-cols-3 gap-8",children:[a.jsx("div",{className:"lg:col-span-2",children:a.jsx("div",{className:"bg-white rounded-lg shadow-sm border border-gray-200",children:a.jsxs("div",{className:"p-6",children:[a.jsx("h2",{className:`text-lg font-semibold text-gray-900 mb-6 ${l?"font-arabic":""}`,children:l?"المنتجات":"Items"}),a.jsx("div",{className:"space-y-6",children:e.map(y=>a.jsxs("div",{className:`flex items-center space-x-4 ${l?"space-x-reverse":""}`,children:[a.jsx("div",{className:"flex-shrink-0",children:a.jsx("img",{src:y.product.images[0]||"/placeholder-image.jpg",alt:l?y.product.nameAr:y.product.name,className:"h-20 w-20 object-cover rounded-lg"})}),a.jsxs("div",{className:"flex-1 min-w-0",children:[a.jsx("h3",{className:`text-lg font-medium text-gray-900 ${l?"font-arabic":""}`,children:l?y.product.nameAr:y.product.name}),a.jsxs("p",{className:"text-sm text-gray-500",children:[l?"المقاس:":"Size:"," ",y.variant.size," | ",l?"اللون:":"Color:"," ",y.variant.color]}),a.jsx("p",{className:"text-lg font-semibold text-gray-900 mt-1",children:m(y.price)})]}),a.jsxs("div",{className:`flex items-center space-x-3 ${l?"space-x-reverse":""}`,children:[a.jsx("button",{onClick:()=>u(y.id,y.quantity-1),className:"p-1 rounded-md hover:bg-gray-100 transition-colors",children:a.jsx(wh,{className:"h-4 w-4 text-gray-600"})}),a.jsx("span",{className:"text-lg font-medium text-gray-900 min-w-[2rem] text-center",children:y.quantity}),a.jsx("button",{onClick:()=>u(y.id,y.quantity+1),className:"p-1 rounded-md hover:bg-gray-100 transition-colors",children:a.jsx(Ur,{className:"h-4 w-4 text-gray-600"})})]}),a.jsx("div",{className:"text-right",children:a.jsx("p",{className:"text-lg font-semibold text-gray-900",children:m(y.price*y.quantity)})}),a.jsx("button",{onClick:()=>d(y.id),className:"p-2 text-red-600 hover:bg-red-50 rounded-md transition-colors",title:l?"إزالة المنتج":"Remove item",children:a.jsx($n,{className:"h-4 w-4"})})]},y.id))})]})})}),a.jsx("div",{className:"lg:col-span-1",children:a.jsx("div",{className:"bg-white rounded-lg shadow-sm border border-gray-200 sticky top-4",children:a.jsxs("div",{className:"p-6",children:[a.jsx("h2",{className:`text-lg font-semibold text-gray-900 mb-6 ${l?"font-arabic":""}`,children:l?"ملخص الطلب":"Order Summary"}),a.jsxs("div",{className:"space-y-4",children:[a.jsxs("div",{className:`flex justify-between ${l?"flex-row-reverse":""}`,children:[a.jsx("span",{className:`text-gray-600 ${l?"font-arabic":""}`,children:l?"المجموع الفرعي":"Subtotal"}),a.jsx("span",{className:"font-medium",children:m(t)})]}),a.jsxs("div",{className:`flex justify-between ${l?"flex-row-reverse":""}`,children:[a.jsx("span",{className:`text-gray-600 ${l?"font-arabic":""}`,children:l?"الشحن":"Shipping"}),a.jsx("span",{className:"font-medium",children:v===0?a.jsx("span",{className:"text-green-600",children:l?"مجاني":"Free"}):m(v)})]}),a.jsxs("div",{className:`flex justify-between ${l?"flex-row-reverse":""}`,children:[a.jsx("span",{className:`text-gray-600 ${l?"font-arabic":""}`,children:l?"ضريبة القيمة المضافة (15%)":"VAT (15%)"}),a.jsx("span",{className:"font-medium",children:m(g)})]}),v>0&&a.jsx("div",{className:`text-sm text-gray-500 ${l?"font-arabic text-right":""}`,children:l?`شحن مجاني للطلبات أكثر من ${m(500)}`:`Free shipping on orders over ${m(500)}`}),a.jsx("div",{className:"border-t border-gray-200 pt-4",children:a.jsxs("div",{className:`flex justify-between ${l?"flex-row-reverse":""}`,children:[a.jsx("span",{className:`text-lg font-semibold text-gray-900 ${l?"font-arabic":""}`,children:l?"المجموع الكلي":"Total"}),a.jsx("span",{className:"text-lg font-semibold text-gray-900",children:m(w)})]})}),a.jsx(he,{onClick:()=>c("/checkout"),className:"w-full mt-6",size:"lg",children:l?"إتمام الطلب":"Proceed to Checkout"})]})]})})})]})]})};class kh{getAllOrders(){return H.getAdminData().orders||[]}getCustomerOrders(t){return H.getCustomerData(t).orders||[]}getOrderById(t){return this.getAllOrders().find(s=>s.id===t)||null}createOrder(t){const r={id:this.generateId(),orderNumber:this.generateOrderNumber(),...t,status:"pending",createdAt:new Date().toISOString(),updatedAt:new Date().toISOString()};return H.addOrder(t.customerId,r),r}updateOrderStatus(t,r){const s=this.getAllOrders(),n=s.findIndex(c=>c.id===t);if(n===-1)return!1;s[n].status=r,s[n].updatedAt=new Date().toISOString(),H.updateAdminData({orders:s});const i=s[n].customerId,o=H.getCustomerData(i),l=o.orders.findIndex(c=>c.id===t);return l!==-1&&(o.orders[l].status=r,o.orders[l].updatedAt=new Date().toISOString(),H.updateCustomerData(i,{orders:o.orders})),!0}updateOrder(t,r){const s=this.getAllOrders(),n=s.findIndex(c=>c.id===t);if(n===-1)return null;s[n]={...s[n],...r,updatedAt:new Date().toISOString()},H.updateAdminData({orders:s});const i=s[n].customerId,o=H.getCustomerData(i),l=o.orders.findIndex(c=>c.id===t);return l!==-1&&(o.orders[l]=s[n],H.updateCustomerData(i,{orders:o.orders})),s[n]}deleteOrder(t){const r=this.getAllOrders(),s=r.find(l=>l.id===t);if(!s)return!1;const n=r.filter(l=>l.id!==t);H.updateAdminData({orders:n});const o=H.getCustomerData(s.customerId).orders.filter(l=>l.id!==t);return H.updateCustomerData(s.customerId,{orders:o}),!0}getOrdersByStatus(t){return this.getAllOrders().filter(s=>s.status===t)}getOrdersByDateRange(t,r){return this.getAllOrders().filter(n=>{const i=new Date(n.createdAt),o=new Date(t),l=new Date(r);return i>=o&&i<=l})}searchOrders(t){const r=this.getAllOrders(),s=t.toLowerCase();return r.filter(n=>n.orderNumber.toLowerCase().includes(s)||n.customerName.toLowerCase().includes(s)||n.customerEmail.toLowerCase().includes(s)||n.items.some(i=>i.name.toLowerCase().includes(s)||i.nameAr.toLowerCase().includes(s)))}getOrderStats(){const t=this.getAllOrders(),r=new Date,s=new Date(r.getFullYear(),r.getMonth(),1),n=new Date(r.getFullYear(),r.getMonth()-1,1),i=new Date(r.getFullYear(),r.getMonth(),0),o=t.filter(w=>new Date(w.createdAt)>=s),l=t.filter(w=>{const y=new Date(w.createdAt);return y>=n&&y<=i}),c=t.reduce((w,y)=>w+y.total,0),u=o.reduce((w,y)=>w+y.total,0),d=l.reduce((w,y)=>w+y.total,0),f=d>0?(u-d)/d*100:0,m=l.length>0?(o.length-l.length)/l.length*100:0,v=t.length>0?c/t.length:0,g={pending:t.filter(w=>w.status==="pending").length,processing:t.filter(w=>w.status==="processing").length,shipped:t.filter(w=>w.status==="shipped").length,delivered:t.filter(w=>w.status==="delivered").length,cancelled:t.filter(w=>w.status==="cancelled").length};return{totalOrders:t.length,totalRevenue:c,thisMonthOrders:o.length,thisMonthRevenue:u,revenueChange:f,orderChange:m,avgOrderValue:v,statusCounts:g}}getTopCustomers(t=10){const r=this.getAllOrders(),s=new Map;return r.forEach(n=>{const i=n.customerId;s.has(i)||s.set(i,{customerId:i,customerName:n.customerName,customerEmail:n.customerEmail,totalOrders:0,totalSpent:0});const o=s.get(i);o.totalOrders++,o.totalSpent+=n.total}),Array.from(s.values()).sort((n,i)=>i.totalSpent-n.totalSpent).slice(0,t)}getRecentOrders(t=10){return this.getAllOrders().sort((s,n)=>new Date(n.createdAt).getTime()-new Date(s.createdAt).getTime()).slice(0,t)}generateOrderNumber(){const t=Date.now().toString(),r=Math.random().toString(36).substr(2,4).toUpperCase();return`ORD-${t.slice(-6)}${r}`}generateId(){return Date.now().toString(36)+Math.random().toString(36).substr(2)}calculateOrderTotals(t,r=0,s=0){const n=t.reduce((l,c)=>l+c.price*c.quantity,0),i=n*(s/100),o=n+r+i;return{subtotal:n,shipping:r,tax:i,total:o}}validateOrder(t){const r=[];return t.customerId||r.push("Customer ID is required"),(!t.items||t.items.length===0)&&r.push("Order must contain at least one item"),t.shippingAddress||r.push("Shipping address is required"),t.paymentMethod||r.push("Payment method is required"),(!t.total||t.total<=0)&&r.push("Valid total amount is required"),{isValid:r.length===0,errors:r}}}const go=new kh,Nv=()=>{const{items:e,total:t,clearCart:r}=Si(),{user:s}=at(),{isRTL:n}=oe(),i=Dt(),[o,l]=j.useState(1),[c,u]=j.useState(!1),[d,f]=j.useState({id:"",type:"home",firstName:(s==null?void 0:s.firstName)||"",lastName:(s==null?void 0:s.lastName)||"",phone:(s==null?void 0:s.phone)||"",street:"",city:"",state:"",postalCode:"",country:"Saudi Arabia",isDefault:!1}),[m,v]=j.useState("cod"),[g,w]=j.useState(""),y=new kh;if(j.useEffect(()=>{e.length===0&&i("/cart")},[e.length,i]),j.useEffect(()=>{s||i("/login")},[s,i]),e.length===0||!s)return null;const p=t>500?0:25,h=t*.15,x=t+p+h,b=C=>`${C.toLocaleString()} ${n?"ر.س":"SAR"}`,k=(C,D)=>{f(U=>({...U,[C]:D}))},E=()=>["firstName","lastName","email","phone","street","city","state","postalCode"].every(D=>{var U;return(U=d[D])==null?void 0:U.toString().trim()}),O=async()=>{if(!E()){Q.error(n?"يرجى ملء جميع الحقول المطلوبة":"Please fill in all required fields");return}u(!0);try{const C=e.map(U=>({id:U.id,productId:U.productId,name:U.product.name,nameAr:U.product.nameAr,image:U.product.images[0]||"",quantity:U.quantity,price:U.price,variant:{size:U.variant.size,color:U.variant.color}})),D=y.createOrder({customerId:s.id,customerName:`${s.firstName} ${s.lastName}`,customerEmail:s.email,items:C,shippingAddress:d,paymentMethod:m,subtotal:t,shipping:p,tax:h,total:x});r(),Q.success(n?"تم إنشاء الطلب بنجاح!":"Order placed successfully!"),i(`/customer/orders/view/${D.id}`)}catch(C){console.error("Error placing order:",C),Q.error(n?"حدث خطأ أثناء إنشاء الطلب":"Error placing order")}finally{u(!1)}},$=[{id:1,name:n?"عنوان الشحن":"Shipping Address",icon:wr},{id:2,name:n?"طريقة الدفع":"Payment Method",icon:_n},{id:3,name:n?"مراجعة الطلب":"Review Order",icon:nd}];return a.jsxs("div",{className:"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8",children:[a.jsxs("div",{className:`flex items-center justify-between mb-8 ${n?"flex-row-reverse":""}`,children:[a.jsxs("div",{children:[a.jsx("h1",{className:`text-3xl font-bold text-gray-900 ${n?"font-arabic":""}`,children:n?"إتمام الطلب":"Checkout"}),a.jsx("p",{className:`text-gray-600 mt-2 ${n?"font-arabic":""}`,children:n?"أكمل طلبك بأمان":"Complete your order securely"})]}),a.jsxs(he,{variant:"outline",onClick:()=>i("/cart"),children:[a.jsx(Xa,{className:`h-4 w-4 ${n?"ml-2 rotate-180":"mr-2"}`}),n?"العودة إلى السلة":"Back to Cart"]})]}),a.jsx("div",{className:"mb-8",children:a.jsx("div",{className:`flex items-center justify-center space-x-8 ${n?"space-x-reverse":""}`,children:$.map((C,D)=>{const U=C.icon,W=o===C.id,I=o>C.id;return a.jsxs("div",{className:`flex items-center ${n?"flex-row-reverse":""}`,children:[a.jsx("div",{className:`
                  flex items-center justify-center w-10 h-10 rounded-full border-2 transition-colors
                  ${W?"bg-black border-black text-white":I?"bg-green-500 border-green-500 text-white":"border-gray-300 text-gray-500"}
                `,children:I?a.jsx(nd,{className:"h-5 w-5"}):a.jsx(U,{className:"h-5 w-5"})}),a.jsx("span",{className:`ml-3 text-sm font-medium ${W?"text-black":"text-gray-500"} ${n?"font-arabic mr-3 ml-0":""}`,children:C.name}),D<$.length-1&&a.jsx("div",{className:`w-16 h-0.5 bg-gray-300 ${n?"mr-8":"ml-8"}`})]},C.id)})})}),a.jsxs("div",{className:"grid grid-cols-1 lg:grid-cols-3 gap-8",children:[a.jsxs("div",{className:"lg:col-span-2",children:[o===1&&a.jsxs("div",{className:"bg-white rounded-lg shadow-sm border border-gray-200 p-6",children:[a.jsx("h2",{className:`text-xl font-semibold text-gray-900 mb-6 ${n?"font-arabic":""}`,children:n?"عنوان الشحن":"Shipping Address"}),a.jsxs("div",{className:"grid grid-cols-1 md:grid-cols-2 gap-6",children:[a.jsxs("div",{children:[a.jsxs("label",{className:`block text-sm font-medium text-gray-700 mb-2 ${n?"font-arabic":""}`,children:[n?"الاسم الأول":"First Name"," *"]}),a.jsx("input",{type:"text",value:d.firstName,onChange:C=>k("firstName",C.target.value),className:`w-full px-4 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-black focus:border-transparent ${n?"text-right font-arabic":""}`,required:!0})]}),a.jsxs("div",{children:[a.jsxs("label",{className:`block text-sm font-medium text-gray-700 mb-2 ${n?"font-arabic":""}`,children:[n?"الاسم الأخير":"Last Name"," *"]}),a.jsx("input",{type:"text",value:d.lastName,onChange:C=>k("lastName",C.target.value),className:`w-full px-4 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-black focus:border-transparent ${n?"text-right font-arabic":""}`,required:!0})]}),a.jsxs("div",{children:[a.jsxs("label",{className:`block text-sm font-medium text-gray-700 mb-2 ${n?"font-arabic":""}`,children:[n?"رقم الهاتف":"Phone Number"," *"]}),a.jsx("input",{type:"tel",value:d.phone,onChange:C=>k("phone",C.target.value),className:`w-full px-4 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-black focus:border-transparent ${n?"text-right font-arabic":""}`,required:!0})]}),a.jsxs("div",{className:"md:col-span-2",children:[a.jsxs("label",{className:`block text-sm font-medium text-gray-700 mb-2 ${n?"font-arabic":""}`,children:[n?"عنوان الشارع":"Street Address"," *"]}),a.jsx("input",{type:"text",value:d.street,onChange:C=>k("street",C.target.value),className:`w-full px-4 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-black focus:border-transparent ${n?"text-right font-arabic":""}`,required:!0})]}),a.jsxs("div",{children:[a.jsxs("label",{className:`block text-sm font-medium text-gray-700 mb-2 ${n?"font-arabic":""}`,children:[n?"المدينة":"City"," *"]}),a.jsx("input",{type:"text",value:d.city,onChange:C=>k("city",C.target.value),className:`w-full px-4 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-black focus:border-transparent ${n?"text-right font-arabic":""}`,required:!0})]}),a.jsxs("div",{children:[a.jsxs("label",{className:`block text-sm font-medium text-gray-700 mb-2 ${n?"font-arabic":""}`,children:[n?"المنطقة":"State/Province"," *"]}),a.jsx("input",{type:"text",value:d.state,onChange:C=>k("state",C.target.value),className:`w-full px-4 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-black focus:border-transparent ${n?"text-right font-arabic":""}`,required:!0})]}),a.jsxs("div",{children:[a.jsxs("label",{className:`block text-sm font-medium text-gray-700 mb-2 ${n?"font-arabic":""}`,children:[n?"الرمز البريدي":"Postal Code"," *"]}),a.jsx("input",{type:"text",value:d.postalCode,onChange:C=>k("postalCode",C.target.value),className:`w-full px-4 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-black focus:border-transparent ${n?"text-right font-arabic":""}`,required:!0})]}),a.jsxs("div",{children:[a.jsxs("label",{className:`block text-sm font-medium text-gray-700 mb-2 ${n?"font-arabic":""}`,children:[n?"الدولة":"Country"," *"]}),a.jsx("select",{value:d.country,onChange:C=>k("country",C.target.value),className:`w-full px-4 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-black focus:border-transparent ${n?"text-right font-arabic":""}`,required:!0,children:a.jsx("option",{value:"Saudi Arabia",children:n?"المملكة العربية السعودية":"Saudi Arabia"})})]})]}),a.jsx("div",{className:"mt-6 flex justify-end",children:a.jsx(he,{onClick:()=>l(2),disabled:!E(),children:n?"التالي":"Next"})})]}),o===2&&a.jsxs("div",{className:"bg-white rounded-lg shadow-sm border border-gray-200 p-6",children:[a.jsx("h2",{className:`text-xl font-semibold text-gray-900 mb-6 ${n?"font-arabic":""}`,children:n?"طريقة الدفع":"Payment Method"}),a.jsxs("div",{className:"space-y-4",children:[a.jsx("div",{className:"border border-gray-200 rounded-lg p-4",children:a.jsxs("label",{className:`flex items-center ${n?"flex-row-reverse":""}`,children:[a.jsx("input",{type:"radio",name:"paymentMethod",value:"cod",checked:m==="cod",onChange:C=>v(C.target.value),className:`h-4 w-4 text-black focus:ring-black border-gray-300 ${n?"ml-3":"mr-3"}`}),a.jsxs("div",{children:[a.jsx("div",{className:`font-medium text-gray-900 ${n?"font-arabic":""}`,children:n?"الدفع عند الاستلام":"Cash on Delivery"}),a.jsx("div",{className:`text-sm text-gray-500 ${n?"font-arabic":""}`,children:n?"ادفع نقداً عند استلام طلبك":"Pay with cash when you receive your order"})]})]})}),a.jsx("div",{className:"border border-gray-200 rounded-lg p-4 opacity-50",children:a.jsxs("label",{className:`flex items-center ${n?"flex-row-reverse":""}`,children:[a.jsx("input",{type:"radio",name:"paymentMethod",value:"card",disabled:!0,className:`h-4 w-4 text-black focus:ring-black border-gray-300 ${n?"ml-3":"mr-3"}`}),a.jsxs("div",{children:[a.jsx("div",{className:`font-medium text-gray-900 ${n?"font-arabic":""}`,children:n?"بطاقة ائتمان":"Credit Card"}),a.jsx("div",{className:`text-sm text-gray-500 ${n?"font-arabic":""}`,children:n?"قريباً":"Coming Soon"})]})]})})]}),a.jsxs("div",{className:`mt-6 flex justify-between ${n?"flex-row-reverse":""}`,children:[a.jsx(he,{variant:"outline",onClick:()=>l(1),children:n?"السابق":"Previous"}),a.jsx(he,{onClick:()=>l(3),children:n?"التالي":"Next"})]})]}),o===3&&a.jsxs("div",{className:"bg-white rounded-lg shadow-sm border border-gray-200 p-6",children:[a.jsx("h2",{className:`text-xl font-semibold text-gray-900 mb-6 ${n?"font-arabic":""}`,children:n?"مراجعة الطلب":"Review Your Order"}),a.jsxs("div",{className:"space-y-4 mb-6",children:[a.jsx("h3",{className:`text-lg font-medium text-gray-900 ${n?"font-arabic":""}`,children:n?"المنتجات":"Items"}),e.map(C=>a.jsxs("div",{className:`flex items-center space-x-4 ${n?"space-x-reverse":""}`,children:[a.jsx("img",{src:C.product.images[0]||"/placeholder-image.jpg",alt:n?C.product.nameAr:C.product.name,className:"h-16 w-16 object-cover rounded-lg"}),a.jsxs("div",{className:"flex-1",children:[a.jsx("h4",{className:`font-medium text-gray-900 ${n?"font-arabic":""}`,children:n?C.product.nameAr:C.product.name}),a.jsxs("p",{className:"text-sm text-gray-500",children:[n?"المقاس:":"Size:"," ",C.variant.size," | ",n?"اللون:":"Color:"," ",C.variant.color]}),a.jsxs("p",{className:"text-sm text-gray-500",children:[n?"الكمية:":"Quantity:"," ",C.quantity]})]}),a.jsx("div",{className:"text-right",children:a.jsx("p",{className:"font-medium text-gray-900",children:b(C.price*C.quantity)})})]},C.id))]}),a.jsxs("div",{className:"border-t border-gray-200 pt-6 mb-6",children:[a.jsx("h3",{className:`text-lg font-medium text-gray-900 mb-4 ${n?"font-arabic":""}`,children:n?"عنوان الشحن":"Shipping Address"}),a.jsxs("div",{className:`text-gray-600 ${n?"font-arabic text-right":""}`,children:[a.jsxs("p",{children:[d.firstName," ",d.lastName]}),a.jsx("p",{children:d.street}),a.jsxs("p",{children:[d.city,", ",d.state," ",d.postalCode]}),a.jsx("p",{children:d.country}),a.jsx("p",{children:d.phone})]})]}),a.jsxs("div",{className:"border-t border-gray-200 pt-6 mb-6",children:[a.jsx("h3",{className:`text-lg font-medium text-gray-900 mb-4 ${n?"font-arabic":""}`,children:n?"طريقة الدفع":"Payment Method"}),a.jsx("p",{className:`text-gray-600 ${n?"font-arabic":""}`,children:m==="cod"?n?"الدفع عند الاستلام":"Cash on Delivery":m})]}),a.jsxs("div",{className:"border-t border-gray-200 pt-6 mb-6",children:[a.jsx("label",{className:`block text-sm font-medium text-gray-700 mb-2 ${n?"font-arabic":""}`,children:n?"ملاحظات الطلب (اختياري)":"Order Notes (Optional)"}),a.jsx("textarea",{value:g,onChange:C=>w(C.target.value),rows:3,className:`w-full px-4 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-black focus:border-transparent ${n?"text-right font-arabic":""}`,placeholder:n?"أي ملاحظات خاصة بطلبك...":"Any special notes for your order..."})]}),a.jsxs("div",{className:`flex justify-between ${n?"flex-row-reverse":""}`,children:[a.jsx(he,{variant:"outline",onClick:()=>l(2),children:n?"السابق":"Previous"}),a.jsx(he,{onClick:O,disabled:c,className:"bg-green-600 hover:bg-green-700",children:c?n?"جاري المعالجة...":"Processing...":n?"تأكيد الطلب":"Place Order"})]})]})]}),a.jsx("div",{className:"lg:col-span-1",children:a.jsx("div",{className:"bg-white rounded-lg shadow-sm border border-gray-200 sticky top-4",children:a.jsxs("div",{className:"p-6",children:[a.jsx("h2",{className:`text-lg font-semibold text-gray-900 mb-6 ${n?"font-arabic":""}`,children:n?"ملخص الطلب":"Order Summary"}),a.jsx("div",{className:"space-y-3 mb-6",children:e.map(C=>a.jsxs("div",{className:`flex justify-between text-sm ${n?"flex-row-reverse":""}`,children:[a.jsxs("span",{className:`text-gray-600 ${n?"font-arabic":""}`,children:[n?C.product.nameAr:C.product.name," × ",C.quantity]}),a.jsx("span",{className:"font-medium",children:b(C.price*C.quantity)})]},C.id))}),a.jsxs("div",{className:"border-t border-gray-200 pt-4 space-y-3",children:[a.jsxs("div",{className:`flex justify-between ${n?"flex-row-reverse":""}`,children:[a.jsx("span",{className:`text-gray-600 ${n?"font-arabic":""}`,children:n?"المجموع الفرعي":"Subtotal"}),a.jsx("span",{className:"font-medium",children:b(t)})]}),a.jsxs("div",{className:`flex justify-between ${n?"flex-row-reverse":""}`,children:[a.jsx("span",{className:`text-gray-600 ${n?"font-arabic":""}`,children:n?"الشحن":"Shipping"}),a.jsx("span",{className:"font-medium",children:p===0?a.jsx("span",{className:"text-green-600",children:n?"مجاني":"Free"}):b(p)})]}),a.jsxs("div",{className:`flex justify-between ${n?"flex-row-reverse":""}`,children:[a.jsx("span",{className:`text-gray-600 ${n?"font-arabic":""}`,children:n?"ضريبة القيمة المضافة (15%)":"VAT (15%)"}),a.jsx("span",{className:"font-medium",children:b(h)})]}),a.jsx("div",{className:"border-t border-gray-200 pt-3",children:a.jsxs("div",{className:`flex justify-between ${n?"flex-row-reverse":""}`,children:[a.jsx("span",{className:`text-lg font-semibold text-gray-900 ${n?"font-arabic":""}`,children:n?"المجموع الكلي":"Total"}),a.jsx("span",{className:"text-lg font-semibold text-gray-900",children:b(x)})]})})]}),a.jsx("div",{className:"mt-6 p-4 bg-gray-50 rounded-lg",children:a.jsxs("div",{className:`flex items-center ${n?"flex-row-reverse":""}`,children:[a.jsx(Ei,{className:`h-5 w-5 text-gray-600 ${n?"ml-2":"mr-2"}`}),a.jsx("span",{className:`text-sm text-gray-600 ${n?"font-arabic":""}`,children:n?"شحن آمن ومضمون":"Secure & guaranteed shipping"})]})})]})})})]})]})};var Mn=e=>e.type==="checkbox",Ar=e=>e instanceof Date,Ue=e=>e==null;const Sh=e=>typeof e=="object";var we=e=>!Ue(e)&&!Array.isArray(e)&&Sh(e)&&!Ar(e),jv=e=>we(e)&&e.target?Mn(e.target)?e.target.checked:e.target.value:e,bv=e=>e.substring(0,e.search(/\.\d+(\.|$)/))||e,kv=(e,t)=>e.has(bv(t)),Sv=e=>{const t=e.constructor&&e.constructor.prototype;return we(t)&&t.hasOwnProperty("isPrototypeOf")},Cc=typeof window<"u"&&typeof window.HTMLElement<"u"&&typeof document<"u";function $e(e){let t;const r=Array.isArray(e),s=typeof FileList<"u"?e instanceof FileList:!1;if(e instanceof Date)t=new Date(e);else if(!(Cc&&(e instanceof Blob||s))&&(r||we(e)))if(t=r?[]:{},!r&&!Sv(e))t=e;else for(const n in e)e.hasOwnProperty(n)&&(t[n]=$e(e[n]));else return e;return t}var Di=e=>/^\w*$/.test(e),be=e=>e===void 0,$c=e=>Array.isArray(e)?e.filter(Boolean):[],Pc=e=>$c(e.replace(/["|']|\]/g,"").split(/\.|\[/)),M=(e,t,r)=>{if(!t||!we(e))return r;const s=(Di(t)?[t]:Pc(t)).reduce((n,i)=>Ue(n)?n:n[i],e);return be(s)||s===e?be(e[t])?r:e[t]:s},Ct=e=>typeof e=="boolean",de=(e,t,r)=>{let s=-1;const n=Di(t)?[t]:Pc(t),i=n.length,o=i-1;for(;++s<i;){const l=n[s];let c=r;if(s!==o){const u=e[l];c=we(u)||Array.isArray(u)?u:isNaN(+n[s+1])?{}:[]}if(l==="__proto__"||l==="constructor"||l==="prototype")return;e[l]=c,e=e[l]}};const ad={BLUR:"blur",FOCUS_OUT:"focusout",CHANGE:"change"},yt={onBlur:"onBlur",onChange:"onChange",onSubmit:"onSubmit",onTouched:"onTouched",all:"all"},_t={max:"max",min:"min",maxLength:"maxLength",minLength:"minLength",pattern:"pattern",required:"required",validate:"validate"},Cv=Re.createContext(null);Cv.displayName="HookFormContext";var $v=(e,t,r,s=!0)=>{const n={defaultValues:t._defaultValues};for(const i in e)Object.defineProperty(n,i,{get:()=>{const o=i;return t._proxyFormState[o]!==yt.all&&(t._proxyFormState[o]=!s||yt.all),r&&(r[o]=!0),e[o]}});return n};const Pv=typeof window<"u"?Re.useLayoutEffect:Re.useEffect;var Pt=e=>typeof e=="string",Ev=(e,t,r,s,n)=>Pt(e)?(s&&t.watch.add(e),M(r,e,n)):Array.isArray(e)?e.map(i=>(s&&t.watch.add(i),M(r,i))):(s&&(t.watchAll=!0),r),kl=e=>Ue(e)||!Sh(e);function nr(e,t,r=new WeakSet){if(kl(e)||kl(t))return e===t;if(Ar(e)&&Ar(t))return e.getTime()===t.getTime();const s=Object.keys(e),n=Object.keys(t);if(s.length!==n.length)return!1;if(r.has(e)||r.has(t))return!0;r.add(e),r.add(t);for(const i of s){const o=e[i];if(!n.includes(i))return!1;if(i!=="ref"){const l=t[i];if(Ar(o)&&Ar(l)||we(o)&&we(l)||Array.isArray(o)&&Array.isArray(l)?!nr(o,l,r):o!==l)return!1}}return!0}var Ov=(e,t,r,s,n)=>t?{...r[e],types:{...r[e]&&r[e].types?r[e].types:{},[s]:n||!0}}:{},sn=e=>Array.isArray(e)?e:[e],id=()=>{let e=[];return{get observers(){return e},next:n=>{for(const i of e)i.next&&i.next(n)},subscribe:n=>(e.push(n),{unsubscribe:()=>{e=e.filter(i=>i!==n)}}),unsubscribe:()=>{e=[]}}},We=e=>we(e)&&!Object.keys(e).length,Ec=e=>e.type==="file",vt=e=>typeof e=="function",Za=e=>{if(!Cc)return!1;const t=e?e.ownerDocument:0;return e instanceof(t&&t.defaultView?t.defaultView.HTMLElement:HTMLElement)},Ch=e=>e.type==="select-multiple",Oc=e=>e.type==="radio",Av=e=>Oc(e)||Mn(e),xo=e=>Za(e)&&e.isConnected;function Dv(e,t){const r=t.slice(0,-1).length;let s=0;for(;s<r;)e=be(e)?s++:e[t[s++]];return e}function Lv(e){for(const t in e)if(e.hasOwnProperty(t)&&!be(e[t]))return!1;return!0}function je(e,t){const r=Array.isArray(t)?t:Di(t)?[t]:Pc(t),s=r.length===1?e:Dv(e,r),n=r.length-1,i=r[n];return s&&delete s[i],n!==0&&(we(s)&&We(s)||Array.isArray(s)&&Lv(s))&&je(e,r.slice(0,-1)),e}var $h=e=>{for(const t in e)if(vt(e[t]))return!0;return!1};function ei(e,t={}){const r=Array.isArray(e);if(we(e)||r)for(const s in e)Array.isArray(e[s])||we(e[s])&&!$h(e[s])?(t[s]=Array.isArray(e[s])?[]:{},ei(e[s],t[s])):Ue(e[s])||(t[s]=!0);return t}function Ph(e,t,r){const s=Array.isArray(e);if(we(e)||s)for(const n in e)Array.isArray(e[n])||we(e[n])&&!$h(e[n])?be(t)||kl(r[n])?r[n]=Array.isArray(e[n])?ei(e[n],[]):{...ei(e[n])}:Ph(e[n],Ue(t)?{}:t[n],r[n]):r[n]=!nr(e[n],t[n]);return r}var Ms=(e,t)=>Ph(e,t,ei(t));const od={value:!1,isValid:!1},ld={value:!0,isValid:!0};var Eh=e=>{if(Array.isArray(e)){if(e.length>1){const t=e.filter(r=>r&&r.checked&&!r.disabled).map(r=>r.value);return{value:t,isValid:!!t.length}}return e[0].checked&&!e[0].disabled?e[0].attributes&&!be(e[0].attributes.value)?be(e[0].value)||e[0].value===""?ld:{value:e[0].value,isValid:!0}:ld:od}return od},Oh=(e,{valueAsNumber:t,valueAsDate:r,setValueAs:s})=>be(e)?e:t?e===""?NaN:e&&+e:r&&Pt(e)?new Date(e):s?s(e):e;const cd={isValid:!1,value:null};var Ah=e=>Array.isArray(e)?e.reduce((t,r)=>r&&r.checked&&!r.disabled?{isValid:!0,value:r.value}:t,cd):cd;function ud(e){const t=e.ref;return Ec(t)?t.files:Oc(t)?Ah(e.refs).value:Ch(t)?[...t.selectedOptions].map(({value:r})=>r):Mn(t)?Eh(e.refs).value:Oh(be(t.value)?e.ref.value:t.value,e)}var Tv=(e,t,r,s)=>{const n={};for(const i of e){const o=M(t,i);o&&de(n,i,o._f)}return{criteriaMode:r,names:[...e],fields:n,shouldUseNativeValidation:s}},ti=e=>e instanceof RegExp,Us=e=>be(e)?e:ti(e)?e.source:we(e)?ti(e.value)?e.value.source:e.value:e,dd=e=>({isOnSubmit:!e||e===yt.onSubmit,isOnBlur:e===yt.onBlur,isOnChange:e===yt.onChange,isOnAll:e===yt.all,isOnTouch:e===yt.onTouched});const fd="AsyncFunction";var Rv=e=>!!e&&!!e.validate&&!!(vt(e.validate)&&e.validate.constructor.name===fd||we(e.validate)&&Object.values(e.validate).find(t=>t.constructor.name===fd)),_v=e=>e.mount&&(e.required||e.min||e.max||e.maxLength||e.minLength||e.pattern||e.validate),md=(e,t,r)=>!r&&(t.watchAll||t.watch.has(e)||[...t.watch].some(s=>e.startsWith(s)&&/^\.\w+/.test(e.slice(s.length))));const nn=(e,t,r,s)=>{for(const n of r||Object.keys(e)){const i=M(e,n);if(i){const{_f:o,...l}=i;if(o){if(o.refs&&o.refs[0]&&t(o.refs[0],n)&&!s)return!0;if(o.ref&&t(o.ref,o.name)&&!s)return!0;if(nn(l,t))break}else if(we(l)&&nn(l,t))break}}};function hd(e,t,r){const s=M(e,r);if(s||Di(r))return{error:s,name:r};const n=r.split(".");for(;n.length;){const i=n.join("."),o=M(t,i),l=M(e,i);if(o&&!Array.isArray(o)&&r!==i)return{name:r};if(l&&l.type)return{name:i,error:l};if(l&&l.root&&l.root.type)return{name:`${i}.root`,error:l.root};n.pop()}return{name:r}}var Fv=(e,t,r,s)=>{r(e);const{name:n,...i}=e;return We(i)||Object.keys(i).length>=Object.keys(t).length||Object.keys(i).find(o=>t[o]===(!s||yt.all))},Iv=(e,t,r)=>!e||!t||e===t||sn(e).some(s=>s&&(r?s===t:s.startsWith(t)||t.startsWith(s))),Mv=(e,t,r,s,n)=>n.isOnAll?!1:!r&&n.isOnTouch?!(t||e):(r?s.isOnBlur:n.isOnBlur)?!e:(r?s.isOnChange:n.isOnChange)?e:!0,Uv=(e,t)=>!$c(M(e,t)).length&&je(e,t),zv=(e,t,r)=>{const s=sn(M(e,r));return de(s,"root",t[r]),de(e,r,s),e},ja=e=>Pt(e);function pd(e,t,r="validate"){if(ja(e)||Array.isArray(e)&&e.every(ja)||Ct(e)&&!e)return{type:r,message:ja(e)?e:"",ref:t}}var Wr=e=>we(e)&&!ti(e)?e:{value:e,message:""},gd=async(e,t,r,s,n,i)=>{const{ref:o,refs:l,required:c,maxLength:u,minLength:d,min:f,max:m,pattern:v,validate:g,name:w,valueAsNumber:y,mount:p}=e._f,h=M(r,w);if(!p||t.has(w))return{};const x=l?l[0]:o,b=W=>{n&&x.reportValidity&&(x.setCustomValidity(Ct(W)?"":W||""),x.reportValidity())},k={},E=Oc(o),O=Mn(o),$=E||O,C=(y||Ec(o))&&be(o.value)&&be(h)||Za(o)&&o.value===""||h===""||Array.isArray(h)&&!h.length,D=Ov.bind(null,w,s,k),U=(W,I,Y,Ne=_t.maxLength,te=_t.minLength)=>{const ue=W?I:Y;k[w]={type:W?Ne:te,message:ue,ref:o,...D(W?Ne:te,ue)}};if(i?!Array.isArray(h)||!h.length:c&&(!$&&(C||Ue(h))||Ct(h)&&!h||O&&!Eh(l).isValid||E&&!Ah(l).isValid)){const{value:W,message:I}=ja(c)?{value:!!c,message:c}:Wr(c);if(W&&(k[w]={type:_t.required,message:I,ref:x,...D(_t.required,I)},!s))return b(I),k}if(!C&&(!Ue(f)||!Ue(m))){let W,I;const Y=Wr(m),Ne=Wr(f);if(!Ue(h)&&!isNaN(h)){const te=o.valueAsNumber||h&&+h;Ue(Y.value)||(W=te>Y.value),Ue(Ne.value)||(I=te<Ne.value)}else{const te=o.valueAsDate||new Date(h),ue=q=>new Date(new Date().toDateString()+" "+q),T=o.type=="time",V=o.type=="week";Pt(Y.value)&&h&&(W=T?ue(h)>ue(Y.value):V?h>Y.value:te>new Date(Y.value)),Pt(Ne.value)&&h&&(I=T?ue(h)<ue(Ne.value):V?h<Ne.value:te<new Date(Ne.value))}if((W||I)&&(U(!!W,Y.message,Ne.message,_t.max,_t.min),!s))return b(k[w].message),k}if((u||d)&&!C&&(Pt(h)||i&&Array.isArray(h))){const W=Wr(u),I=Wr(d),Y=!Ue(W.value)&&h.length>+W.value,Ne=!Ue(I.value)&&h.length<+I.value;if((Y||Ne)&&(U(Y,W.message,I.message),!s))return b(k[w].message),k}if(v&&!C&&Pt(h)){const{value:W,message:I}=Wr(v);if(ti(W)&&!h.match(W)&&(k[w]={type:_t.pattern,message:I,ref:o,...D(_t.pattern,I)},!s))return b(I),k}if(g){if(vt(g)){const W=await g(h,r),I=pd(W,x);if(I&&(k[w]={...I,...D(_t.validate,I.message)},!s))return b(I.message),k}else if(we(g)){let W={};for(const I in g){if(!We(W)&&!s)break;const Y=pd(await g[I](h,r),x,I);Y&&(W={...Y,...D(I,Y.message)},b(Y.message),s&&(k[w]=W))}if(!We(W)&&(k[w]={ref:x,...W},!s))return k}}return b(!0),k};const Vv={mode:yt.onSubmit,reValidateMode:yt.onChange,shouldFocusError:!0};function Bv(e={}){let t={...Vv,...e},r={submitCount:0,isDirty:!1,isReady:!1,isLoading:vt(t.defaultValues),isValidating:!1,isSubmitted:!1,isSubmitting:!1,isSubmitSuccessful:!1,isValid:!1,touchedFields:{},dirtyFields:{},validatingFields:{},errors:t.errors||{},disabled:t.disabled||!1},s={},n=we(t.defaultValues)||we(t.values)?$e(t.defaultValues||t.values)||{}:{},i=t.shouldUnregister?{}:$e(n),o={action:!1,mount:!1,watch:!1},l={mount:new Set,disabled:new Set,unMount:new Set,array:new Set,watch:new Set},c,u=0;const d={isDirty:!1,dirtyFields:!1,validatingFields:!1,touchedFields:!1,isValidating:!1,isValid:!1,errors:!1};let f={...d};const m={array:id(),state:id()},v=t.criteriaMode===yt.all,g=N=>S=>{clearTimeout(u),u=setTimeout(N,S)},w=async N=>{if(!t.disabled&&(d.isValid||f.isValid||N)){const S=t.resolver?We((await O()).errors):await C(s,!0);S!==r.isValid&&m.state.next({isValid:S})}},y=(N,S)=>{!t.disabled&&(d.isValidating||d.validatingFields||f.isValidating||f.validatingFields)&&((N||Array.from(l.mount)).forEach(P=>{P&&(S?de(r.validatingFields,P,S):je(r.validatingFields,P))}),m.state.next({validatingFields:r.validatingFields,isValidating:!We(r.validatingFields)}))},p=(N,S=[],P,_,R=!0,L=!0)=>{if(_&&P&&!t.disabled){if(o.action=!0,L&&Array.isArray(M(s,N))){const z=P(M(s,N),_.argA,_.argB);R&&de(s,N,z)}if(L&&Array.isArray(M(r.errors,N))){const z=P(M(r.errors,N),_.argA,_.argB);R&&de(r.errors,N,z),Uv(r.errors,N)}if((d.touchedFields||f.touchedFields)&&L&&Array.isArray(M(r.touchedFields,N))){const z=P(M(r.touchedFields,N),_.argA,_.argB);R&&de(r.touchedFields,N,z)}(d.dirtyFields||f.dirtyFields)&&(r.dirtyFields=Ms(n,i)),m.state.next({name:N,isDirty:U(N,S),dirtyFields:r.dirtyFields,errors:r.errors,isValid:r.isValid})}else de(i,N,S)},h=(N,S)=>{de(r.errors,N,S),m.state.next({errors:r.errors})},x=N=>{r.errors=N,m.state.next({errors:r.errors,isValid:!1})},b=(N,S,P,_)=>{const R=M(s,N);if(R){const L=M(i,N,be(P)?M(n,N):P);be(L)||_&&_.defaultChecked||S?de(i,N,S?L:ud(R._f)):Y(N,L),o.mount&&w()}},k=(N,S,P,_,R)=>{let L=!1,z=!1;const re={name:N};if(!t.disabled){if(!P||_){(d.isDirty||f.isDirty)&&(z=r.isDirty,r.isDirty=re.isDirty=U(),L=z!==re.isDirty);const ae=nr(M(n,N),S);z=!!M(r.dirtyFields,N),ae?je(r.dirtyFields,N):de(r.dirtyFields,N,!0),re.dirtyFields=r.dirtyFields,L=L||(d.dirtyFields||f.dirtyFields)&&z!==!ae}if(P){const ae=M(r.touchedFields,N);ae||(de(r.touchedFields,N,P),re.touchedFields=r.touchedFields,L=L||(d.touchedFields||f.touchedFields)&&ae!==P)}L&&R&&m.state.next(re)}return L?re:{}},E=(N,S,P,_)=>{const R=M(r.errors,N),L=(d.isValid||f.isValid)&&Ct(S)&&r.isValid!==S;if(t.delayError&&P?(c=g(()=>h(N,P)),c(t.delayError)):(clearTimeout(u),c=null,P?de(r.errors,N,P):je(r.errors,N)),(P?!nr(R,P):R)||!We(_)||L){const z={..._,...L&&Ct(S)?{isValid:S}:{},errors:r.errors,name:N};r={...r,...z},m.state.next(z)}},O=async N=>{y(N,!0);const S=await t.resolver(i,t.context,Tv(N||l.mount,s,t.criteriaMode,t.shouldUseNativeValidation));return y(N),S},$=async N=>{const{errors:S}=await O(N);if(N)for(const P of N){const _=M(S,P);_?de(r.errors,P,_):je(r.errors,P)}else r.errors=S;return S},C=async(N,S,P={valid:!0})=>{for(const _ in N){const R=N[_];if(R){const{_f:L,...z}=R;if(L){const re=l.array.has(L.name),ae=R._f&&Rv(R._f);ae&&d.validatingFields&&y([_],!0);const ot=await gd(R,l.disabled,i,v,t.shouldUseNativeValidation&&!S,re);if(ae&&d.validatingFields&&y([_]),ot[L.name]&&(P.valid=!1,S))break;!S&&(M(ot,L.name)?re?zv(r.errors,ot,L.name):de(r.errors,L.name,ot[L.name]):je(r.errors,L.name))}!We(z)&&await C(z,S,P)}}return P.valid},D=()=>{for(const N of l.unMount){const S=M(s,N);S&&(S._f.refs?S._f.refs.every(P=>!xo(P)):!xo(S._f.ref))&&Gt(N)}l.unMount=new Set},U=(N,S)=>!t.disabled&&(N&&S&&de(i,N,S),!nr(q(),n)),W=(N,S,P)=>Ev(N,l,{...o.mount?i:be(S)?n:Pt(N)?{[N]:S}:S},P,S),I=N=>$c(M(o.mount?i:n,N,t.shouldUnregister?M(n,N,[]):[])),Y=(N,S,P={})=>{const _=M(s,N);let R=S;if(_){const L=_._f;L&&(!L.disabled&&de(i,N,Oh(S,L)),R=Za(L.ref)&&Ue(S)?"":S,Ch(L.ref)?[...L.ref.options].forEach(z=>z.selected=R.includes(z.value)):L.refs?Mn(L.ref)?L.refs.forEach(z=>{(!z.defaultChecked||!z.disabled)&&(Array.isArray(R)?z.checked=!!R.find(re=>re===z.value):z.checked=R===z.value||!!R)}):L.refs.forEach(z=>z.checked=z.value===R):Ec(L.ref)?L.ref.value="":(L.ref.value=R,L.ref.type||m.state.next({name:N,values:$e(i)})))}(P.shouldDirty||P.shouldTouch)&&k(N,R,P.shouldTouch,P.shouldDirty,!0),P.shouldValidate&&V(N)},Ne=(N,S,P)=>{for(const _ in S){if(!S.hasOwnProperty(_))return;const R=S[_],L=N+"."+_,z=M(s,L);(l.array.has(N)||we(R)||z&&!z._f)&&!Ar(R)?Ne(L,R,P):Y(L,R,P)}},te=(N,S,P={})=>{const _=M(s,N),R=l.array.has(N),L=$e(S);de(i,N,L),R?(m.array.next({name:N,values:$e(i)}),(d.isDirty||d.dirtyFields||f.isDirty||f.dirtyFields)&&P.shouldDirty&&m.state.next({name:N,dirtyFields:Ms(n,i),isDirty:U(N,L)})):_&&!_._f&&!Ue(L)?Ne(N,L,P):Y(N,L,P),md(N,l)&&m.state.next({...r,name:N}),m.state.next({name:o.mount?N:void 0,values:$e(i)})},ue=async N=>{o.mount=!0;const S=N.target;let P=S.name,_=!0;const R=M(s,P),L=ae=>{_=Number.isNaN(ae)||Ar(ae)&&isNaN(ae.getTime())||nr(ae,M(i,P,ae))},z=dd(t.mode),re=dd(t.reValidateMode);if(R){let ae,ot;const Un=S.type?ud(R._f):jv(N),Jt=N.type===ad.BLUR||N.type===ad.FOCUS_OUT,Vh=!_v(R._f)&&!t.resolver&&!M(r.errors,P)&&!R._f.deps||Mv(Jt,M(r.touchedFields,P),r.isSubmitted,re,z),Fi=md(P,l,Jt);de(i,P,Un),Jt?(R._f.onBlur&&R._f.onBlur(N),c&&c(0)):R._f.onChange&&R._f.onChange(N);const Ii=k(P,Un,Jt),Bh=!We(Ii)||Fi;if(!Jt&&m.state.next({name:P,type:N.type,values:$e(i)}),Vh)return(d.isValid||f.isValid)&&(t.mode==="onBlur"?Jt&&w():Jt||w()),Bh&&m.state.next({name:P,...Fi?{}:Ii});if(!Jt&&Fi&&m.state.next({...r}),t.resolver){const{errors:_c}=await O([P]);if(L(Un),_){const Hh=hd(r.errors,s,P),Fc=hd(_c,s,Hh.name||P);ae=Fc.error,P=Fc.name,ot=We(_c)}}else y([P],!0),ae=(await gd(R,l.disabled,i,v,t.shouldUseNativeValidation))[P],y([P]),L(Un),_&&(ae?ot=!1:(d.isValid||f.isValid)&&(ot=await C(s,!0)));_&&(R._f.deps&&V(R._f.deps),E(P,ot,ae,Ii))}},T=(N,S)=>{if(M(r.errors,S)&&N.focus)return N.focus(),1},V=async(N,S={})=>{let P,_;const R=sn(N);if(t.resolver){const L=await $(be(N)?N:R);P=We(L),_=N?!R.some(z=>M(L,z)):P}else N?(_=(await Promise.all(R.map(async L=>{const z=M(s,L);return await C(z&&z._f?{[L]:z}:z)}))).every(Boolean),!(!_&&!r.isValid)&&w()):_=P=await C(s);return m.state.next({...!Pt(N)||(d.isValid||f.isValid)&&P!==r.isValid?{}:{name:N},...t.resolver||!N?{isValid:P}:{},errors:r.errors}),S.shouldFocus&&!_&&nn(s,T,N?R:l.mount),_},q=N=>{const S={...o.mount?i:n};return be(N)?S:Pt(N)?M(S,N):N.map(P=>M(S,P))},J=(N,S)=>({invalid:!!M((S||r).errors,N),isDirty:!!M((S||r).dirtyFields,N),error:M((S||r).errors,N),isValidating:!!M(r.validatingFields,N),isTouched:!!M((S||r).touchedFields,N)}),ne=N=>{N&&sn(N).forEach(S=>je(r.errors,S)),m.state.next({errors:N?r.errors:{}})},Xe=(N,S,P)=>{const _=(M(s,N,{_f:{}})._f||{}).ref,R=M(r.errors,N)||{},{ref:L,message:z,type:re,...ae}=R;de(r.errors,N,{...ae,...S,ref:_}),m.state.next({name:N,errors:r.errors,isValid:!1}),P&&P.shouldFocus&&_&&_.focus&&_.focus()},it=(N,S)=>vt(N)?m.state.subscribe({next:P=>"values"in P&&N(W(void 0,S),P)}):W(N,S,!0),Lt=N=>m.state.subscribe({next:S=>{Iv(N.name,S.name,N.exact)&&Fv(S,N.formState||d,zh,N.reRenderRoot)&&N.callback({values:{...i},...r,...S,defaultValues:n})}}).unsubscribe,Tt=N=>(o.mount=!0,f={...f,...N.formState},Lt({...N,formState:f})),Gt=(N,S={})=>{for(const P of N?sn(N):l.mount)l.mount.delete(P),l.array.delete(P),S.keepValue||(je(s,P),je(i,P)),!S.keepError&&je(r.errors,P),!S.keepDirty&&je(r.dirtyFields,P),!S.keepTouched&&je(r.touchedFields,P),!S.keepIsValidating&&je(r.validatingFields,P),!t.shouldUnregister&&!S.keepDefaultValue&&je(n,P);m.state.next({values:$e(i)}),m.state.next({...r,...S.keepDirty?{isDirty:U()}:{}}),!S.keepIsValid&&w()},Ac=({disabled:N,name:S})=>{(Ct(N)&&o.mount||N||l.disabled.has(S))&&(N?l.disabled.add(S):l.disabled.delete(S))},Ri=(N,S={})=>{let P=M(s,N);const _=Ct(S.disabled)||Ct(t.disabled);return de(s,N,{...P||{},_f:{...P&&P._f?P._f:{ref:{name:N}},name:N,mount:!0,...S}}),l.mount.add(N),P?Ac({disabled:Ct(S.disabled)?S.disabled:t.disabled,name:N}):b(N,!0,S.value),{..._?{disabled:S.disabled||t.disabled}:{},...t.progressive?{required:!!S.required,min:Us(S.min),max:Us(S.max),minLength:Us(S.minLength),maxLength:Us(S.maxLength),pattern:Us(S.pattern)}:{},name:N,onChange:ue,onBlur:ue,ref:R=>{if(R){Ri(N,S),P=M(s,N);const L=be(R.value)&&R.querySelectorAll&&R.querySelectorAll("input,select,textarea")[0]||R,z=Av(L),re=P._f.refs||[];if(z?re.find(ae=>ae===L):L===P._f.ref)return;de(s,N,{_f:{...P._f,...z?{refs:[...re.filter(xo),L,...Array.isArray(M(n,N))?[{}]:[]],ref:{type:L.type,name:N}}:{ref:L}}}),b(N,!1,void 0,L)}else P=M(s,N,{}),P._f&&(P._f.mount=!1),(t.shouldUnregister||S.shouldUnregister)&&!(kv(l.array,N)&&o.action)&&l.unMount.add(N)}}},_i=()=>t.shouldFocusError&&nn(s,T,l.mount),Ih=N=>{Ct(N)&&(m.state.next({disabled:N}),nn(s,(S,P)=>{const _=M(s,P);_&&(S.disabled=_._f.disabled||N,Array.isArray(_._f.refs)&&_._f.refs.forEach(R=>{R.disabled=_._f.disabled||N}))},0,!1))},Dc=(N,S)=>async P=>{let _;P&&(P.preventDefault&&P.preventDefault(),P.persist&&P.persist());let R=$e(i);if(m.state.next({isSubmitting:!0}),t.resolver){const{errors:L,values:z}=await O();r.errors=L,R=$e(z)}else await C(s);if(l.disabled.size)for(const L of l.disabled)je(R,L);if(je(r.errors,"root"),We(r.errors)){m.state.next({errors:{}});try{await N(R,P)}catch(L){_=L}}else S&&await S({...r.errors},P),_i(),setTimeout(_i);if(m.state.next({isSubmitted:!0,isSubmitting:!1,isSubmitSuccessful:We(r.errors)&&!_,submitCount:r.submitCount+1,errors:r.errors}),_)throw _},Mh=(N,S={})=>{M(s,N)&&(be(S.defaultValue)?te(N,$e(M(n,N))):(te(N,S.defaultValue),de(n,N,$e(S.defaultValue))),S.keepTouched||je(r.touchedFields,N),S.keepDirty||(je(r.dirtyFields,N),r.isDirty=S.defaultValue?U(N,$e(M(n,N))):U()),S.keepError||(je(r.errors,N),d.isValid&&w()),m.state.next({...r}))},Lc=(N,S={})=>{const P=N?$e(N):n,_=$e(P),R=We(N),L=R?n:_;if(S.keepDefaultValues||(n=P),!S.keepValues){if(S.keepDirtyValues){const z=new Set([...l.mount,...Object.keys(Ms(n,i))]);for(const re of Array.from(z))M(r.dirtyFields,re)?de(L,re,M(i,re)):te(re,M(L,re))}else{if(Cc&&be(N))for(const z of l.mount){const re=M(s,z);if(re&&re._f){const ae=Array.isArray(re._f.refs)?re._f.refs[0]:re._f.ref;if(Za(ae)){const ot=ae.closest("form");if(ot){ot.reset();break}}}}if(S.keepFieldsRef)for(const z of l.mount)te(z,M(L,z));else s={}}i=t.shouldUnregister?S.keepDefaultValues?$e(n):{}:$e(L),m.array.next({values:{...L}}),m.state.next({values:{...L}})}l={mount:S.keepDirtyValues?l.mount:new Set,unMount:new Set,array:new Set,disabled:new Set,watch:new Set,watchAll:!1,focus:""},o.mount=!d.isValid||!!S.keepIsValid||!!S.keepDirtyValues,o.watch=!!t.shouldUnregister,m.state.next({submitCount:S.keepSubmitCount?r.submitCount:0,isDirty:R?!1:S.keepDirty?r.isDirty:!!(S.keepDefaultValues&&!nr(N,n)),isSubmitted:S.keepIsSubmitted?r.isSubmitted:!1,dirtyFields:R?{}:S.keepDirtyValues?S.keepDefaultValues&&i?Ms(n,i):r.dirtyFields:S.keepDefaultValues&&N?Ms(n,N):S.keepDirty?r.dirtyFields:{},touchedFields:S.keepTouched?r.touchedFields:{},errors:S.keepErrors?r.errors:{},isSubmitSuccessful:S.keepIsSubmitSuccessful?r.isSubmitSuccessful:!1,isSubmitting:!1})},Tc=(N,S)=>Lc(vt(N)?N(i):N,S),Uh=(N,S={})=>{const P=M(s,N),_=P&&P._f;if(_){const R=_.refs?_.refs[0]:_.ref;R.focus&&(R.focus(),S.shouldSelect&&vt(R.select)&&R.select())}},zh=N=>{r={...r,...N}},Rc={control:{register:Ri,unregister:Gt,getFieldState:J,handleSubmit:Dc,setError:Xe,_subscribe:Lt,_runSchema:O,_focusError:_i,_getWatch:W,_getDirty:U,_setValid:w,_setFieldArray:p,_setDisabledField:Ac,_setErrors:x,_getFieldArray:I,_reset:Lc,_resetDefaultValues:()=>vt(t.defaultValues)&&t.defaultValues().then(N=>{Tc(N,t.resetOptions),m.state.next({isLoading:!1})}),_removeUnmounted:D,_disableForm:Ih,_subjects:m,_proxyFormState:d,get _fields(){return s},get _formValues(){return i},get _state(){return o},set _state(N){o=N},get _defaultValues(){return n},get _names(){return l},set _names(N){l=N},get _formState(){return r},get _options(){return t},set _options(N){t={...t,...N}}},subscribe:Tt,trigger:V,register:Ri,handleSubmit:Dc,watch:it,setValue:te,getValues:q,reset:Tc,resetField:Mh,clearErrors:ne,unregister:Gt,setError:Xe,setFocus:Uh,getFieldState:J};return{...Rc,formControl:Rc}}function Dh(e={}){const t=Re.useRef(void 0),r=Re.useRef(void 0),[s,n]=Re.useState({isDirty:!1,isValidating:!1,isLoading:vt(e.defaultValues),isSubmitted:!1,isSubmitting:!1,isSubmitSuccessful:!1,isValid:!1,submitCount:0,dirtyFields:{},touchedFields:{},validatingFields:{},errors:e.errors||{},disabled:e.disabled||!1,isReady:!1,defaultValues:vt(e.defaultValues)?void 0:e.defaultValues});if(!t.current)if(e.formControl)t.current={...e.formControl,formState:s},e.defaultValues&&!vt(e.defaultValues)&&e.formControl.reset(e.defaultValues,e.resetOptions);else{const{formControl:o,...l}=Bv(e);t.current={...l,formState:s}}const i=t.current.control;return i._options=e,Pv(()=>{const o=i._subscribe({formState:i._proxyFormState,callback:()=>n({...i._formState}),reRenderRoot:!0});return n(l=>({...l,isReady:!0})),i._formState.isReady=!0,o},[i]),Re.useEffect(()=>i._disableForm(e.disabled),[i,e.disabled]),Re.useEffect(()=>{e.mode&&(i._options.mode=e.mode),e.reValidateMode&&(i._options.reValidateMode=e.reValidateMode)},[i,e.mode,e.reValidateMode]),Re.useEffect(()=>{e.errors&&(i._setErrors(e.errors),i._focusError())},[i,e.errors]),Re.useEffect(()=>{e.shouldUnregister&&i._subjects.state.next({values:i._getWatch()})},[i,e.shouldUnregister]),Re.useEffect(()=>{if(i._proxyFormState.isDirty){const o=i._getDirty();o!==s.isDirty&&i._subjects.state.next({isDirty:o})}},[i,s.isDirty]),Re.useEffect(()=>{e.values&&!nr(e.values,r.current)?(i._reset(e.values,{keepFieldsRef:!0,...i._options.resetOptions}),r.current=e.values,n(o=>({...o}))):i._resetDefaultValues()},[i,e.values]),Re.useEffect(()=>{i._state.mount||(i._setValid(),i._state.mount=!0),i._state.watch&&(i._state.watch=!1,i._subjects.state.next({...i._formState})),i._removeUnmounted()}),t.current.formState=$v(s,i),t.current}const ar=Re.forwardRef(({label:e,error:t,helperText:r,className:s="",id:n,...i},o)=>{const l=n||`input-${Math.random().toString(36).substr(2,9)}`;return a.jsxs("div",{className:"w-full",children:[e&&a.jsx("label",{htmlFor:l,className:"block text-sm font-medium text-gray-700 mb-1",children:e}),a.jsx("input",{ref:o,id:l,className:`
          w-full px-3 py-2 border rounded-md shadow-sm focus:outline-none focus:ring-2 focus:ring-black focus:border-transparent
          ${t?"border-red-500":"border-gray-300"}
          ${s}
        `,...i}),t&&a.jsx("p",{className:"mt-1 text-sm text-red-600",children:t}),r&&!t&&a.jsx("p",{className:"mt-1 text-sm text-gray-500",children:r})]})});ar.displayName="Input";const Hv=()=>{var h,x,b,k;const[e,t]=j.useState(!1),{login:r,isAuthenticated:s,error:n,clearError:i}=at(),{t:o,isRTL:l}=oe(),c=Dt(),d=((x=(h=bt().state)==null?void 0:h.from)==null?void 0:x.pathname)||"/",{register:f,handleSubmit:m,setValue:v,formState:{errors:g}}=Dh();j.useEffect(()=>{s&&c(d,{replace:!0})},[s,c,d]),j.useEffect(()=>{n&&(Q.error(n),i())},[n,i]);const w=async E=>{t(!0);try{await r(E.email,E.password),Q.success(o("auth.loginSuccessful"))}catch{Q.error(o("auth.invalidCredentials"))}finally{t(!1)}},y=()=>{v("email","<EMAIL>"),v("password","admin123")},p=()=>{v("email","<EMAIL>"),v("password","customer123")};return a.jsx("div",{className:"min-h-screen flex items-center justify-center bg-gray-50 py-12 px-4 sm:px-6 lg:px-8",children:a.jsxs("div",{className:`max-w-md w-full space-y-8 ${l?"font-arabic text-right":"text-left"}`,children:[a.jsxs("div",{className:"text-center",children:[a.jsx("h2",{className:`mt-6 text-3xl font-bold text-gray-900 ${l?"font-arabic":""}`,children:o("auth.signInToAccount")}),a.jsxs("p",{className:`mt-2 text-sm text-gray-600 ${l?"font-arabic":""}`,children:[o("auth.noAccount")," ",a.jsx(G,{to:"/register",className:"font-medium text-black hover:text-gray-800",children:o("auth.createNewAccount")})]})]}),a.jsxs("form",{className:`mt-8 space-y-6 ${l?"text-right":"text-left"}`,onSubmit:m(w),children:[a.jsxs("div",{className:"space-y-4",children:[a.jsx(ar,{label:o("auth.email"),type:"email",autoComplete:"email",...f("email",{required:o("auth.emailRequired"),pattern:{value:/^[A-Z0-9._%+-]+@[A-Z0-9.-]+\.[A-Z]{2,}$/i,message:o("auth.invalidEmail")}}),error:(b=g.email)==null?void 0:b.message}),a.jsx(ar,{label:o("auth.password"),type:"password",autoComplete:"current-password",...f("password",{required:o("auth.passwordRequired"),minLength:{value:6,message:o("auth.passwordMinLength")}}),error:(k=g.password)==null?void 0:k.message})]}),a.jsxs("div",{className:`flex items-center justify-between ${l?"flex-row-reverse":""}`,children:[a.jsxs("div",{className:`flex items-center ${l?"flex-row-reverse":""}`,children:[a.jsx("input",{id:"remember-me",name:"remember-me",type:"checkbox",className:"h-4 w-4 text-black focus:ring-black border-gray-300 rounded"}),a.jsx("label",{htmlFor:"remember-me",className:`block text-sm text-gray-900 ${l?"mr-2":"ml-2"}`,children:o("auth.rememberMe")})]}),a.jsx("div",{className:"text-sm",children:a.jsx("a",{href:"#",className:"font-medium text-black hover:text-gray-800",children:o("auth.forgotPassword")})})]}),a.jsx(he,{type:"submit",className:"w-full",size:"lg",isLoading:e,children:o("auth.login")})]}),a.jsxs("div",{className:"mt-6 p-4 bg-blue-50 border border-blue-200 rounded-md",children:[a.jsx("h3",{className:`text-sm font-medium text-blue-900 mb-3 ${l?"font-arabic":""}`,children:l?"⚠️ بيانات تجريبية للاختبار - انسخ والصق:":"⚠️ Demo Credentials - Copy & Paste:"}),a.jsxs("div",{className:"space-y-3",children:[a.jsxs("div",{className:"bg-white p-3 rounded border border-green-200",children:[a.jsx("p",{className:`text-sm font-medium text-green-800 mb-2 ${l?"font-arabic":""}`,children:l?"👤 حساب المدير:":"👤 Admin Account:"}),a.jsxs("div",{className:"space-y-1",children:[a.jsxs("div",{className:"flex items-center justify-between",children:[a.jsx("span",{className:"text-xs text-gray-500",children:l?"البريد:":"Email:"}),a.jsx("code",{className:"text-sm bg-gray-100 px-2 py-1 rounded",children:"<EMAIL>"})]}),a.jsxs("div",{className:"flex items-center justify-between",children:[a.jsx("span",{className:"text-xs text-gray-500",children:l?"كلمة المرور:":"Password:"}),a.jsx("code",{className:"text-sm bg-gray-100 px-2 py-1 rounded",children:"admin123"})]}),a.jsx("button",{type:"button",onClick:y,className:"w-full mt-2 px-3 py-1 bg-green-100 text-green-800 text-xs rounded hover:bg-green-200 transition-colors",children:l?"📋 ملء البيانات تلقائياً":"📋 Auto-fill"})]})]}),a.jsxs("div",{className:"bg-white p-3 rounded border border-blue-200",children:[a.jsx("p",{className:`text-sm font-medium text-blue-800 mb-2 ${l?"font-arabic":""}`,children:l?"👥 حساب العميل:":"👥 Customer Account:"}),a.jsxs("div",{className:"space-y-1",children:[a.jsxs("div",{className:"flex items-center justify-between",children:[a.jsx("span",{className:"text-xs text-gray-500",children:l?"البريد:":"Email:"}),a.jsx("code",{className:"text-sm bg-gray-100 px-2 py-1 rounded",children:"<EMAIL>"})]}),a.jsxs("div",{className:"flex items-center justify-between",children:[a.jsx("span",{className:"text-xs text-gray-500",children:l?"كلمة المرور:":"Password:"}),a.jsx("code",{className:"text-sm bg-gray-100 px-2 py-1 rounded",children:"customer123"})]}),a.jsx("button",{type:"button",onClick:p,className:"w-full mt-2 px-3 py-1 bg-blue-100 text-blue-800 text-xs rounded hover:bg-blue-200 transition-colors",children:l?"📋 ملء البيانات تلقائياً":"📋 Auto-fill"})]})]})]}),a.jsx("p",{className:`text-xs text-blue-700 mt-3 ${l?"font-arabic":""}`,children:l?"💡 انسخ البيانات بالضبط كما هي مكتوبة أعلاه":"💡 Copy the credentials exactly as written above"})]})]})})},Wv=()=>{var v,g,w,y,p;const[e,t]=j.useState(!1),{register:r,isAuthenticated:s,error:n,clearError:i}=at(),o=Dt(),{register:l,handleSubmit:c,watch:u,formState:{errors:d}}=Dh(),f=u("password");j.useEffect(()=>{s&&o("/")},[s,o]),j.useEffect(()=>{n&&(Q.error(n),i())},[n,i]);const m=async h=>{t(!0);try{await r({firstName:h.firstName,lastName:h.lastName,email:h.email,password:h.password}),Q.success("Registration successful!")}catch{}finally{t(!1)}};return a.jsx("div",{className:"min-h-screen flex items-center justify-center bg-gray-50 py-12 px-4 sm:px-6 lg:px-8",children:a.jsxs("div",{className:"max-w-md w-full space-y-8",children:[a.jsxs("div",{children:[a.jsx("h2",{className:"mt-6 text-center text-3xl font-bold text-gray-900",children:"Create your account"}),a.jsxs("p",{className:"mt-2 text-center text-sm text-gray-600",children:["Or"," ",a.jsx(G,{to:"/login",className:"font-medium text-black hover:text-gray-800",children:"sign in to your existing account"})]})]}),a.jsxs("form",{className:"mt-8 space-y-6",onSubmit:c(m),children:[a.jsxs("div",{className:"space-y-4",children:[a.jsxs("div",{className:"grid grid-cols-2 gap-4",children:[a.jsx(ar,{label:"First name",type:"text",autoComplete:"given-name",...l("firstName",{required:"First name is required",minLength:{value:2,message:"First name must be at least 2 characters"}}),error:(v=d.firstName)==null?void 0:v.message}),a.jsx(ar,{label:"Last name",type:"text",autoComplete:"family-name",...l("lastName",{required:"Last name is required",minLength:{value:2,message:"Last name must be at least 2 characters"}}),error:(g=d.lastName)==null?void 0:g.message})]}),a.jsx(ar,{label:"Email address",type:"email",autoComplete:"email",...l("email",{required:"Email is required",pattern:{value:/^[A-Z0-9._%+-]+@[A-Z0-9.-]+\.[A-Z]{2,}$/i,message:"Invalid email address"}}),error:(w=d.email)==null?void 0:w.message}),a.jsx(ar,{label:"Password",type:"password",autoComplete:"new-password",...l("password",{required:"Password is required",minLength:{value:6,message:"Password must be at least 6 characters"}}),error:(y=d.password)==null?void 0:y.message}),a.jsx(ar,{label:"Confirm password",type:"password",autoComplete:"new-password",...l("confirmPassword",{required:"Please confirm your password",validate:h=>h===f||"Passwords do not match"}),error:(p=d.confirmPassword)==null?void 0:p.message})]}),a.jsxs("div",{className:"flex items-center",children:[a.jsx("input",{id:"agree-terms",name:"agree-terms",type:"checkbox",required:!0,className:"h-4 w-4 text-black focus:ring-black border-gray-300 rounded"}),a.jsxs("label",{htmlFor:"agree-terms",className:"ml-2 block text-sm text-gray-900",children:["I agree to the"," ",a.jsx("a",{href:"#",className:"text-black hover:text-gray-800 font-medium",children:"Terms and Conditions"})," ","and"," ",a.jsx("a",{href:"#",className:"text-black hover:text-gray-800 font-medium",children:"Privacy Policy"})]})]}),a.jsx(he,{type:"submit",className:"w-full",size:"lg",isLoading:e,children:"Create account"})]})]})})},qv=()=>{const{user:e}=at();return a.jsxs("div",{className:"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8",children:[a.jsx("h1",{className:"text-3xl font-bold text-black mb-8",children:"Profile"}),a.jsxs("p",{className:"text-gray-600",children:["Welcome, ",e==null?void 0:e.firstName," ",e==null?void 0:e.lastName]}),a.jsxs("p",{className:"text-gray-600",children:["Email: ",e==null?void 0:e.email]}),a.jsxs("p",{className:"text-gray-600",children:["Role: ",e==null?void 0:e.role]}),a.jsx("p",{className:"text-gray-600 mt-4",children:"This page will be implemented in the next phase."})]})},Kv=({isOpen:e,onClose:t})=>{const{t:r,isRTL:s}=oe(),n=bt(),i=[{path:"/admin",icon:sv,label:r("admin.dashboard"),exact:!0},{path:"/admin/products",icon:Je,label:r("admin.products")},{path:"/admin/orders",icon:Es,label:r("admin.orders")},{path:"/admin/customers",icon:Oi,label:r("admin.customers")},{path:"/admin/analytics",icon:Nl,label:r("admin.analytics")},{path:"/admin/inventory",icon:fv,label:r("admin.inventory")},{path:"/admin/settings",icon:bc,label:r("admin.settings")}],o=(l,c)=>c?n.pathname===l:n.pathname.startsWith(l);return a.jsx(a.Fragment,{children:a.jsxs("div",{className:`
        fixed top-0 ${s?"right-0":"left-0"} z-50 h-full w-64 bg-white shadow-lg transform transition-transform duration-300 ease-in-out
        ${e?"translate-x-0":s?"translate-x-full":"-translate-x-full"}
        lg:translate-x-0
      `,children:[a.jsxs("div",{className:"flex items-center justify-between p-4 border-b border-gray-200",children:[a.jsx("h2",{className:`text-xl font-bold text-gray-900 ${s?"font-arabic":""}`,children:r("admin.adminPanel")}),a.jsx("button",{onClick:t,className:"lg:hidden p-2 rounded-md hover:bg-gray-100",children:a.jsx(Ai,{className:"h-5 w-5"})})]}),a.jsx("nav",{className:"mt-4 px-4",children:a.jsx("ul",{className:"space-y-2",children:i.map(l=>{const c=l.icon,u=o(l.path,l.exact);return a.jsx("li",{children:a.jsxs(j0,{to:l.path,onClick:t,className:`
                      flex items-center px-4 py-3 rounded-lg transition-colors duration-200
                      ${u?"bg-black text-white":"text-gray-700 hover:bg-gray-100"}
                      ${s?"flex-row-reverse":""}
                    `,children:[a.jsx(c,{className:`h-5 w-5 ${s?"ml-3":"mr-3"}`}),a.jsx("span",{className:`font-medium ${s?"font-arabic":""}`,children:l.label})]})},l.path)})})})]})})},Qv=({onMenuClick:e})=>{const{user:t,logout:r}=at(),{t:s,isRTL:n,language:i,changeLanguage:o}=oe(),[l,c]=j.useState(!1),u=()=>{r(),c(!1)};return a.jsx("header",{className:"bg-white shadow-sm border-b border-gray-200",children:a.jsxs("div",{className:"flex items-center justify-between px-4 py-3",children:[a.jsxs("div",{className:`flex items-center ${n?"flex-row-reverse":""}`,children:[a.jsx("button",{onClick:e,className:"lg:hidden p-2 rounded-md hover:bg-gray-100",children:a.jsx(jc,{className:"h-6 w-6"})}),a.jsx("h1",{className:`text-2xl font-bold text-gray-900 ${n?"font-arabic mr-4":"ml-4"}`,children:s("admin.dashboard")})]}),a.jsxs("div",{className:`flex items-center space-x-4 ${n?"space-x-reverse":""}`,children:[a.jsxs("button",{onClick:()=>o(i==="ar"?"en":"ar"),className:"p-2 rounded-md hover:bg-gray-100 transition-colors",title:s("common.changeLanguage"),children:[a.jsx(xh,{className:"h-5 w-5 text-gray-600"}),a.jsx("span",{className:"ml-1 text-sm font-medium text-gray-600",children:i==="ar"?"EN":"ع"})]}),a.jsxs("button",{className:"p-2 rounded-md hover:bg-gray-100 relative",children:[a.jsx(gh,{className:"h-5 w-5 text-gray-600"}),a.jsx("span",{className:"absolute top-1 right-1 h-2 w-2 bg-red-500 rounded-full"})]}),a.jsxs("div",{className:"relative",children:[a.jsxs("button",{onClick:()=>c(!l),className:`flex items-center space-x-2 p-2 rounded-md hover:bg-gray-100 ${n?"space-x-reverse":""}`,children:[a.jsx("div",{className:"h-8 w-8 bg-gray-300 rounded-full flex items-center justify-center",children:a.jsx(Sc,{className:"h-5 w-5 text-gray-600"})}),a.jsxs("div",{className:`text-sm ${n?"text-right":"text-left"}`,children:[a.jsxs("p",{className:`font-medium text-gray-900 ${n?"font-arabic":""}`,children:[t==null?void 0:t.firstName," ",t==null?void 0:t.lastName]}),a.jsx("p",{className:"text-gray-500",children:t==null?void 0:t.role})]})]}),l&&a.jsx("div",{className:`
                absolute top-full mt-2 w-48 bg-white rounded-md shadow-lg border border-gray-200 z-50
                ${n?"left-0":"right-0"}
              `,children:a.jsx("div",{className:"py-1",children:a.jsxs("button",{onClick:u,className:`
                      flex items-center w-full px-4 py-2 text-sm text-gray-700 hover:bg-gray-100
                      ${n?"flex-row-reverse font-arabic":""}
                    `,children:[a.jsx(yh,{className:`h-4 w-4 ${n?"ml-2":"mr-2"}`}),s("auth.signOut")]})})})]})]})]})})},Yv=()=>{const{t:e,isRTL:t}=oe(),r=[{title:e("admin.totalRevenue"),value:"0 ر.س",change:"0%",changeType:"positive",icon:$i},{title:e("admin.totalOrders"),value:"0",change:"0%",changeType:"positive",icon:Es},{title:e("admin.totalCustomers"),value:"0",change:"0%",changeType:"positive",icon:Oi},{title:e("admin.totalProducts"),value:"0",change:"0%",changeType:"positive",icon:Je}],s=[],n=[],i=l=>{switch(l){case"completed":return"bg-green-100 text-green-800";case"pending":return"bg-yellow-100 text-yellow-800";case"processing":return"bg-blue-100 text-blue-800";default:return"bg-gray-100 text-gray-800"}},o=l=>{switch(l){case"completed":return e("orders.completed");case"pending":return e("orders.pending");case"processing":return e("orders.processing");default:return l}};return a.jsxs("div",{className:"space-y-6",children:[a.jsxs("div",{children:[a.jsx("h1",{className:`text-3xl font-bold text-gray-900 ${t?"font-arabic":""}`,children:e("admin.dashboardOverview")}),a.jsx("p",{className:`mt-2 text-gray-600 ${t?"font-arabic":""}`,children:e("admin.dashboardDescription")})]}),a.jsx("div",{className:"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6",children:r.map((l,c)=>{const u=l.icon;return a.jsx("div",{className:"bg-white rounded-lg shadow-sm border border-gray-200 p-6",children:a.jsxs("div",{className:`flex items-center justify-between ${t?"flex-row-reverse":""}`,children:[a.jsxs("div",{className:t?"text-right":"text-left",children:[a.jsx("p",{className:`text-sm font-medium text-gray-600 ${t?"font-arabic":""}`,children:l.title}),a.jsx("p",{className:"text-2xl font-bold text-gray-900 mt-2",children:l.value}),a.jsxs("p",{className:`text-sm mt-2 ${l.changeType==="positive"?"text-green-600":l.changeType==="negative"?"text-red-600":"text-gray-500"}`,children:[a.jsx(In,{className:"inline h-4 w-4 mr-1"}),l.change]})]}),a.jsx("div",{className:"h-12 w-12 bg-gray-100 rounded-lg flex items-center justify-center",children:a.jsx(u,{className:"h-6 w-6 text-gray-600"})})]})},c)})}),a.jsxs("div",{className:"grid grid-cols-1 lg:grid-cols-2 gap-6",children:[a.jsxs("div",{className:"bg-white rounded-lg shadow-sm border border-gray-200",children:[a.jsx("div",{className:"p-6 border-b border-gray-200",children:a.jsx("h3",{className:`text-lg font-semibold text-gray-900 ${t?"font-arabic":""}`,children:e("admin.recentOrders")})}),a.jsx("div",{className:"p-6",children:s.length>0?a.jsx("div",{className:"space-y-4",children:s.map(l=>a.jsxs("div",{className:`flex items-center justify-between ${t?"flex-row-reverse":""}`,children:[a.jsxs("div",{className:t?"text-right":"text-left",children:[a.jsx("p",{className:"font-medium text-gray-900",children:l.id}),a.jsx("p",{className:`text-sm text-gray-600 ${t?"font-arabic":""}`,children:l.customer})]}),a.jsxs("div",{className:`flex items-center space-x-4 ${t?"space-x-reverse":""}`,children:[a.jsx("span",{className:"font-medium text-gray-900",children:l.amount}),a.jsx("span",{className:`px-2 py-1 rounded-full text-xs font-medium ${i(l.status)}`,children:o(l.status)})]})]},l.id))}):a.jsx("div",{className:"text-center py-8",children:a.jsx("p",{className:`text-gray-500 ${t?"font-arabic":""}`,children:t?"لا توجد طلبات حديثة":"No recent orders"})})})]}),a.jsxs("div",{className:"bg-white rounded-lg shadow-sm border border-gray-200",children:[a.jsx("div",{className:"p-6 border-b border-gray-200",children:a.jsxs("h3",{className:`text-lg font-semibold text-gray-900 flex items-center ${t?"font-arabic flex-row-reverse":""}`,children:[a.jsx(Cn,{className:`h-5 w-5 text-orange-500 ${t?"ml-2":"mr-2"}`}),e("admin.lowStockAlerts")]})}),a.jsx("div",{className:"p-6",children:n.length>0?a.jsx("div",{className:"space-y-4",children:n.map((l,c)=>a.jsxs("div",{className:`flex items-center justify-between ${t?"flex-row-reverse":""}`,children:[a.jsxs("div",{className:t?"text-right":"text-left",children:[a.jsx("p",{className:`font-medium text-gray-900 ${t?"font-arabic":""}`,children:l.name}),a.jsxs("p",{className:"text-sm text-gray-600",children:[e("admin.threshold"),": ",l.threshold]})]}),a.jsxs("span",{className:"px-3 py-1 bg-red-100 text-red-800 rounded-full text-sm font-medium",children:[l.stock," ",e("admin.inStock")]})]},c))}):a.jsx("div",{className:"text-center py-8",children:a.jsx("p",{className:`text-gray-500 ${t?"font-arabic":""}`,children:t?"جميع المنتجات متوفرة بكمية كافية":"All products are well stocked"})})})]})]})]})},Gv=()=>{const{t:e,isRTL:t}=oe(),[r,s]=j.useState([]),[n,i]=j.useState(""),[o,l]=j.useState("all"),[c,u]=j.useState(!0);j.useEffect(()=>{d()},[]);const d=()=>{u(!0);try{const p=rn.getAllProducts();s(p)}catch(p){Q.error(e("admin.errorLoadingProducts")),console.error("Error loading products:",p)}finally{u(!1)}},f=["all",...rn.getCategories().map(p=>typeof p=="string"?p:p.id)],m=r.filter(p=>{const h=p.name.toLowerCase().includes(n.toLowerCase())||p.nameAr.toLowerCase().includes(n.toLowerCase()),x=o==="all"||p.category===o;return h&&x}),v=async p=>{if(window.confirm(e("admin.confirmDeleteProduct")))try{rn.deleteProduct(p)?(Q.success(e("admin.productDeletedSuccessfully")),d()):Q.error(e("admin.errorDeletingProduct"))}catch(h){Q.error(e("admin.errorDeletingProduct")),console.error("Error deleting product:",h)}};return a.jsxs(Ps,{children:[a.jsx(ee,{index:!0,element:a.jsx(g,{})}),a.jsx(ee,{path:"add",element:a.jsx(w,{})}),a.jsx(ee,{path:"edit/:id",element:a.jsx(w,{})}),a.jsx(ee,{path:"view/:id",element:a.jsx(y,{})})]});function g(){return c?a.jsx("div",{className:"flex items-center justify-center h-64",children:a.jsx("div",{className:"animate-spin rounded-full h-12 w-12 border-b-2 border-blue-600"})}):a.jsxs("div",{className:"space-y-6",children:[a.jsxs("div",{className:`flex items-center justify-between ${t?"flex-row-reverse":""}`,children:[a.jsxs("div",{children:[a.jsx("h1",{className:`text-3xl font-bold text-gray-900 ${t?"font-arabic":""}`,children:e("admin.productManagement")}),a.jsx("p",{className:`mt-2 text-gray-600 ${t?"font-arabic":""}`,children:e("admin.manageYourProducts")})]}),a.jsxs(G,{to:"/admin/products/add",className:`
              flex items-center px-4 py-2 bg-black text-white rounded-lg hover:bg-gray-800 transition-colors
              ${t?"flex-row-reverse":""}
            `,children:[a.jsx(Ur,{className:`h-5 w-5 ${t?"ml-2":"mr-2"}`}),a.jsx("span",{className:t?"font-arabic":"",children:e("admin.addProduct")})]})]}),a.jsxs("div",{className:"grid grid-cols-1 md:grid-cols-4 gap-6",children:[a.jsx("div",{className:"bg-white rounded-lg shadow-sm border border-gray-200 p-6",children:a.jsxs("div",{className:`flex items-center ${t?"flex-row-reverse":""}`,children:[a.jsx("div",{className:"flex-shrink-0",children:a.jsx(Je,{className:"h-8 w-8 text-blue-600"})}),a.jsxs("div",{className:`${t?"mr-4 text-right":"ml-4 text-left"}`,children:[a.jsx("p",{className:`text-sm font-medium text-gray-600 ${t?"font-arabic":""}`,children:e("admin.totalProducts")}),a.jsx("p",{className:"text-2xl font-semibold text-gray-900",children:r.length})]})]})}),a.jsx("div",{className:"bg-white rounded-lg shadow-sm border border-gray-200 p-6",children:a.jsxs("div",{className:`flex items-center ${t?"flex-row-reverse":""}`,children:[a.jsx("div",{className:"flex-shrink-0",children:a.jsx(Cn,{className:"h-8 w-8 text-yellow-600"})}),a.jsxs("div",{className:`${t?"mr-4 text-right":"ml-4 text-left"}`,children:[a.jsx("p",{className:`text-sm font-medium text-gray-600 ${t?"font-arabic":""}`,children:e("admin.lowStock")}),a.jsx("p",{className:"text-2xl font-semibold text-gray-900",children:r.filter(p=>p.stock<=5&&p.stock>0).length})]})]})}),a.jsx("div",{className:"bg-white rounded-lg shadow-sm border border-gray-200 p-6",children:a.jsxs("div",{className:`flex items-center ${t?"flex-row-reverse":""}`,children:[a.jsx("div",{className:"flex-shrink-0",children:a.jsx(Cn,{className:"h-8 w-8 text-red-600"})}),a.jsxs("div",{className:`${t?"mr-4 text-right":"ml-4 text-left"}`,children:[a.jsx("p",{className:`text-sm font-medium text-gray-600 ${t?"font-arabic":""}`,children:e("admin.outOfStock")}),a.jsx("p",{className:"text-2xl font-semibold text-gray-900",children:r.filter(p=>p.stock===0).length})]})]})}),a.jsx("div",{className:"bg-white rounded-lg shadow-sm border border-gray-200 p-6",children:a.jsxs("div",{className:`flex items-center ${t?"flex-row-reverse":""}`,children:[a.jsx("div",{className:"flex-shrink-0",children:a.jsx(Je,{className:"h-8 w-8 text-green-600"})}),a.jsxs("div",{className:`${t?"mr-4 text-right":"ml-4 text-left"}`,children:[a.jsx("p",{className:`text-sm font-medium text-gray-600 ${t?"font-arabic":""}`,children:e("admin.totalValue")}),a.jsxs("p",{className:"text-2xl font-semibold text-gray-900",children:[r.reduce((p,h)=>p+h.price*h.stock,0).toLocaleString()," ",e("currency.symbol")]})]})]})})]}),a.jsx("div",{className:"bg-white rounded-lg shadow-sm border border-gray-200 p-6",children:a.jsxs("div",{className:`grid grid-cols-1 md:grid-cols-2 gap-4 ${t?"text-right":"text-left"}`,children:[a.jsxs("div",{children:[a.jsx("label",{className:`block text-sm font-medium text-gray-700 mb-2 ${t?"font-arabic":""}`,children:e("common.search")}),a.jsxs("div",{className:"relative",children:[a.jsx(js,{className:`absolute top-3 ${t?"right-3":"left-3"} h-5 w-5 text-gray-400`}),a.jsx("input",{type:"text",value:n,onChange:p=>i(p.target.value),placeholder:e("admin.searchProducts"),className:`
                    w-full ${t?"pr-10 pl-4 font-arabic":"pl-10 pr-4"} py-2 border border-gray-300 rounded-lg 
                    focus:ring-2 focus:ring-black focus:border-transparent
                  `})]})]}),a.jsxs("div",{children:[a.jsx("label",{className:`block text-sm font-medium text-gray-700 mb-2 ${t?"font-arabic":""}`,children:e("products.category")}),a.jsx("select",{value:o,onChange:p=>l(p.target.value),className:`
                  w-full px-4 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-black focus:border-transparent
                  ${t?"font-arabic":""}
                `,children:f.map(p=>a.jsx("option",{value:p,children:e(`categories.${p}`)},p))})]})]})}),a.jsxs("div",{className:"bg-white rounded-lg shadow-sm border border-gray-200 overflow-hidden",children:[a.jsx("div",{className:"overflow-x-auto",children:a.jsxs("table",{className:"min-w-full divide-y divide-gray-200",children:[a.jsx("thead",{className:"bg-gray-50",children:a.jsxs("tr",{children:[a.jsx("th",{className:`px-6 py-3 text-xs font-medium text-gray-500 uppercase tracking-wider ${t?"text-right font-arabic":"text-left"}`,children:e("admin.product")}),a.jsx("th",{className:`px-6 py-3 text-xs font-medium text-gray-500 uppercase tracking-wider ${t?"text-right font-arabic":"text-left"}`,children:e("products.category")}),a.jsx("th",{className:`px-6 py-3 text-xs font-medium text-gray-500 uppercase tracking-wider ${t?"text-right font-arabic":"text-left"}`,children:e("products.price")}),a.jsx("th",{className:`px-6 py-3 text-xs font-medium text-gray-500 uppercase tracking-wider ${t?"text-right font-arabic":"text-left"}`,children:e("admin.stock")}),a.jsx("th",{className:`px-6 py-3 text-xs font-medium text-gray-500 uppercase tracking-wider ${t?"text-right font-arabic":"text-left"}`,children:e("common.actions")})]})}),a.jsx("tbody",{className:"bg-white divide-y divide-gray-200",children:m.map(p=>a.jsxs("tr",{className:"hover:bg-gray-50",children:[a.jsx("td",{className:"px-6 py-4 whitespace-nowrap",children:a.jsxs("div",{className:`flex items-center ${t?"flex-row-reverse":""}`,children:[a.jsx("img",{src:p.images[0],alt:t?p.nameAr:p.name,className:"h-12 w-12 rounded-lg object-cover"}),a.jsxs("div",{className:t?"mr-4 text-right":"ml-4 text-left",children:[a.jsx("div",{className:`text-sm font-medium text-gray-900 ${t?"font-arabic":""}`,children:t?p.nameAr:p.name}),a.jsxs("div",{className:"text-sm text-gray-500",children:["ID: ",p.id]})]})]})}),a.jsx("td",{className:`px-6 py-4 whitespace-nowrap text-sm text-gray-900 ${t?"text-right font-arabic":"text-left"}`,children:e(`categories.${p.category}`)}),a.jsxs("td",{className:`px-6 py-4 whitespace-nowrap text-sm text-gray-900 ${t?"text-right":"text-left"}`,children:[p.price," ",e("currency.symbol")]}),a.jsx("td",{className:`px-6 py-4 whitespace-nowrap ${t?"text-right":"text-left"}`,children:a.jsxs("div",{className:"flex flex-col",children:[a.jsxs("span",{className:`inline-flex px-2 py-1 text-xs font-semibold rounded-full w-fit ${p.stock>10?"bg-green-100 text-green-800":p.stock>0?"bg-yellow-100 text-yellow-800":"bg-red-100 text-red-800"}`,children:[p.stock," ",e("admin.inStock")]}),p.stock<=5&&p.stock>0&&a.jsx("span",{className:"text-xs text-orange-600 mt-1",children:e("admin.lowStock")}),p.stock===0&&a.jsx("span",{className:"text-xs text-red-600 mt-1",children:e("admin.outOfStock")})]})}),a.jsx("td",{className:`px-6 py-4 whitespace-nowrap text-sm font-medium ${t?"text-right":"text-left"}`,children:a.jsxs("div",{className:`flex items-center space-x-2 ${t?"space-x-reverse":""}`,children:[a.jsx(G,{to:`/admin/products/view/${p.id}`,className:"text-blue-600 hover:text-blue-900",title:e("common.view"),children:a.jsx(ms,{className:"h-4 w-4"})}),a.jsx(G,{to:`/admin/products/edit/${p.id}`,className:"text-indigo-600 hover:text-indigo-900",title:e("common.edit"),children:a.jsx(Pi,{className:"h-4 w-4"})}),a.jsx("button",{onClick:()=>v(p.id),className:"text-red-600 hover:text-red-900",title:e("common.delete"),children:a.jsx($n,{className:"h-4 w-4"})})]})})]},p.id))})]})}),m.length===0&&a.jsx("div",{className:"text-center py-12",children:a.jsx("p",{className:`text-gray-500 ${t?"font-arabic":""}`,children:e("admin.noProductsFound")})})]})]})}function w(){return a.jsxs("div",{className:"space-y-6",children:[a.jsx("h1",{className:`text-3xl font-bold text-gray-900 ${t?"font-arabic":""}`,children:e("admin.addProduct")}),a.jsx("div",{className:"bg-white rounded-lg shadow-sm border border-gray-200 p-6",children:a.jsx("p",{className:`text-gray-600 ${t?"font-arabic":""}`,children:e("admin.productFormComingSoon")})})]})}function y(){return a.jsxs("div",{className:"space-y-6",children:[a.jsx("h1",{className:`text-3xl font-bold text-gray-900 ${t?"font-arabic":""}`,children:e("admin.viewProduct")}),a.jsx("div",{className:"bg-white rounded-lg shadow-sm border border-gray-200 p-6",children:a.jsx("p",{className:`text-gray-600 ${t?"font-arabic":""}`,children:e("admin.productViewComingSoon")})})]})}},Jv=()=>{const{t:e,isRTL:t}=oe(),[r,s]=j.useState([]),[n,i]=j.useState(""),[o,l]=j.useState("all"),[c,u]=j.useState(!0);j.useEffect(()=>{d()},[]);const d=()=>{u(!0);try{const x=go.getAllOrders();s(x)}catch(x){Q.error(e("admin.errorLoadingOrders")),console.error("Error loading orders:",x)}finally{u(!1)}},f=go.getOrderStats(),m=["all","pending","processing","shipped","delivered","cancelled"],v=r.filter(x=>{const b=x.orderNumber.toLowerCase().includes(n.toLowerCase())||x.customerName.toLowerCase().includes(n.toLowerCase())||x.customerEmail.toLowerCase().includes(n.toLowerCase()),k=o==="all"||x.status===o;return b&&k}),g=async(x,b)=>{try{go.updateOrderStatus(x,b)?(Q.success(e("admin.orderStatusUpdated")),d()):Q.error(e("admin.errorUpdatingOrder"))}catch(k){Q.error(e("admin.errorUpdatingOrder")),console.error("Error updating order status:",k)}},w=x=>{switch(x){case"pending":return a.jsx(ho,{className:"h-4 w-4"});case"processing":return a.jsx(Je,{className:"h-4 w-4"});case"shipped":return a.jsx(Ei,{className:"h-4 w-4"});case"delivered":return a.jsx(Gy,{className:"h-4 w-4"});default:return a.jsx(ho,{className:"h-4 w-4"})}},y=x=>{switch(x){case"pending":return"bg-yellow-100 text-yellow-800";case"processing":return"bg-blue-100 text-blue-800";case"shipped":return"bg-purple-100 text-purple-800";case"delivered":return"bg-green-100 text-green-800";case"cancelled":return"bg-red-100 text-red-800";default:return"bg-gray-100 text-gray-800"}};return a.jsxs(Ps,{children:[a.jsx(ee,{index:!0,element:a.jsx(p,{})}),a.jsx(ee,{path:"view/:id",element:a.jsx(h,{})})]});function p(){return c?a.jsx("div",{className:"flex items-center justify-center h-64",children:a.jsx("div",{className:"animate-spin rounded-full h-12 w-12 border-b-2 border-blue-600"})}):a.jsxs("div",{className:"space-y-6",children:[a.jsxs("div",{children:[a.jsx("h1",{className:`text-3xl font-bold text-gray-900 ${t?"font-arabic":""}`,children:e("admin.orderManagement")}),a.jsx("p",{className:`mt-2 text-gray-600 ${t?"font-arabic":""}`,children:e("admin.manageCustomerOrders")})]}),a.jsxs("div",{className:"grid grid-cols-1 md:grid-cols-4 gap-6",children:[a.jsx("div",{className:"bg-white rounded-lg shadow-sm border border-gray-200 p-6",children:a.jsxs("div",{className:`flex items-center ${t?"flex-row-reverse":""}`,children:[a.jsx("div",{className:"flex-shrink-0",children:a.jsx(Je,{className:"h-8 w-8 text-blue-600"})}),a.jsxs("div",{className:`${t?"mr-4 text-right":"ml-4 text-left"}`,children:[a.jsx("p",{className:`text-sm font-medium text-gray-600 ${t?"font-arabic":""}`,children:e("admin.totalOrders")}),a.jsx("p",{className:"text-2xl font-semibold text-gray-900",children:f.totalOrders})]})]})}),a.jsx("div",{className:"bg-white rounded-lg shadow-sm border border-gray-200 p-6",children:a.jsxs("div",{className:`flex items-center ${t?"flex-row-reverse":""}`,children:[a.jsx("div",{className:"flex-shrink-0",children:a.jsx($i,{className:"h-8 w-8 text-green-600"})}),a.jsxs("div",{className:`${t?"mr-4 text-right":"ml-4 text-left"}`,children:[a.jsx("p",{className:`text-sm font-medium text-gray-600 ${t?"font-arabic":""}`,children:e("admin.totalRevenue")}),a.jsxs("p",{className:"text-2xl font-semibold text-gray-900",children:[f.totalRevenue.toLocaleString()," ",e("currency.symbol")]})]})]})}),a.jsx("div",{className:"bg-white rounded-lg shadow-sm border border-gray-200 p-6",children:a.jsxs("div",{className:`flex items-center ${t?"flex-row-reverse":""}`,children:[a.jsx("div",{className:"flex-shrink-0",children:a.jsx(In,{className:"h-8 w-8 text-purple-600"})}),a.jsxs("div",{className:`${t?"mr-4 text-right":"ml-4 text-left"}`,children:[a.jsx("p",{className:`text-sm font-medium text-gray-600 ${t?"font-arabic":""}`,children:e("admin.thisMonthOrders")}),a.jsx("p",{className:"text-2xl font-semibold text-gray-900",children:f.thisMonthOrders})]})]})}),a.jsx("div",{className:"bg-white rounded-lg shadow-sm border border-gray-200 p-6",children:a.jsxs("div",{className:`flex items-center ${t?"flex-row-reverse":""}`,children:[a.jsx("div",{className:"flex-shrink-0",children:a.jsx(ho,{className:"h-8 w-8 text-yellow-600"})}),a.jsxs("div",{className:`${t?"mr-4 text-right":"ml-4 text-left"}`,children:[a.jsx("p",{className:`text-sm font-medium text-gray-600 ${t?"font-arabic":""}`,children:e("admin.pendingOrders")}),a.jsx("p",{className:"text-2xl font-semibold text-gray-900",children:f.statusCounts.pending})]})]})})]}),a.jsx("div",{className:"bg-white rounded-lg shadow-sm border border-gray-200 p-6",children:a.jsxs("div",{className:`grid grid-cols-1 md:grid-cols-2 gap-4 ${t?"text-right":"text-left"}`,children:[a.jsxs("div",{children:[a.jsx("label",{className:`block text-sm font-medium text-gray-700 mb-2 ${t?"font-arabic":""}`,children:e("common.search")}),a.jsxs("div",{className:"relative",children:[a.jsx(js,{className:`absolute top-3 ${t?"right-3":"left-3"} h-5 w-5 text-gray-400`}),a.jsx("input",{type:"text",value:n,onChange:x=>i(x.target.value),placeholder:e("admin.searchOrders"),className:`
                    w-full ${t?"pr-10 pl-4 font-arabic":"pl-10 pr-4"} py-2 border border-gray-300 rounded-lg 
                    focus:ring-2 focus:ring-black focus:border-transparent
                  `})]})]}),a.jsxs("div",{children:[a.jsx("label",{className:`block text-sm font-medium text-gray-700 mb-2 ${t?"font-arabic":""}`,children:e("orders.status")}),a.jsx("select",{value:o,onChange:x=>l(x.target.value),className:`
                  w-full px-4 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-black focus:border-transparent
                  ${t?"font-arabic":""}
                `,children:m.map(x=>a.jsx("option",{value:x,children:e(`orders.${x}`)},x))})]})]})}),a.jsxs("div",{className:"bg-white rounded-lg shadow-sm border border-gray-200 overflow-hidden",children:[a.jsx("div",{className:"overflow-x-auto",children:a.jsxs("table",{className:"min-w-full divide-y divide-gray-200",children:[a.jsx("thead",{className:"bg-gray-50",children:a.jsxs("tr",{children:[a.jsx("th",{className:`px-6 py-3 text-xs font-medium text-gray-500 uppercase tracking-wider ${t?"text-right font-arabic":"text-left"}`,children:e("orders.orderNumber")}),a.jsx("th",{className:`px-6 py-3 text-xs font-medium text-gray-500 uppercase tracking-wider ${t?"text-right font-arabic":"text-left"}`,children:e("admin.customer")}),a.jsx("th",{className:`px-6 py-3 text-xs font-medium text-gray-500 uppercase tracking-wider ${t?"text-right font-arabic":"text-left"}`,children:e("orders.total")}),a.jsx("th",{className:`px-6 py-3 text-xs font-medium text-gray-500 uppercase tracking-wider ${t?"text-right font-arabic":"text-left"}`,children:e("orders.status")}),a.jsx("th",{className:`px-6 py-3 text-xs font-medium text-gray-500 uppercase tracking-wider ${t?"text-right font-arabic":"text-left"}`,children:e("orders.date")}),a.jsx("th",{className:`px-6 py-3 text-xs font-medium text-gray-500 uppercase tracking-wider ${t?"text-right font-arabic":"text-left"}`,children:e("common.actions")})]})}),a.jsx("tbody",{className:"bg-white divide-y divide-gray-200",children:v.map(x=>a.jsxs("tr",{className:"hover:bg-gray-50",children:[a.jsx("td",{className:`px-6 py-4 whitespace-nowrap text-sm font-medium text-gray-900 ${t?"text-right":"text-left"}`,children:x.orderNumber}),a.jsxs("td",{className:`px-6 py-4 whitespace-nowrap ${t?"text-right":"text-left"}`,children:[a.jsx("div",{className:`text-sm font-medium text-gray-900 ${t?"font-arabic":""}`,children:x.customerName}),a.jsx("div",{className:"text-sm text-gray-500",children:x.customerEmail})]}),a.jsxs("td",{className:`px-6 py-4 whitespace-nowrap text-sm text-gray-900 ${t?"text-right":"text-left"}`,children:[x.total," ",e("currency.symbol")]}),a.jsx("td",{className:`px-6 py-4 whitespace-nowrap ${t?"text-right":"text-left"}`,children:a.jsxs("span",{className:`inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium ${y(x.status)} ${t?"flex-row-reverse":""}`,children:[w(x.status),a.jsx("span",{className:t?"mr-1 font-arabic":"ml-1",children:e(`orders.${x.status}`)})]})}),a.jsx("td",{className:`px-6 py-4 whitespace-nowrap text-sm text-gray-900 ${t?"text-right":"text-left"}`,children:new Date(x.createdAt).toLocaleDateString(t?"ar-SA":"en-US")}),a.jsx("td",{className:`px-6 py-4 whitespace-nowrap text-sm font-medium ${t?"text-right":"text-left"}`,children:a.jsxs("div",{className:`flex items-center space-x-2 ${t?"space-x-reverse":""}`,children:[a.jsx(G,{to:`/admin/orders/view/${x.id}`,className:"text-blue-600 hover:text-blue-900",title:e("common.view"),children:a.jsx(ms,{className:"h-4 w-4"})}),a.jsx("select",{value:x.status,onChange:b=>g(x.id,b.target.value),className:`text-xs border border-gray-300 rounded px-2 py-1 ${t?"font-arabic":""}`,children:m.filter(b=>b!=="all").map(b=>a.jsx("option",{value:b,children:e(`orders.${b}`)},b))})]})})]},x.id))})]})}),v.length===0&&a.jsx("div",{className:"text-center py-12",children:a.jsx("p",{className:`text-gray-500 ${t?"font-arabic":""}`,children:e("admin.noOrdersFound")})})]})]})}function h(){return a.jsxs("div",{className:"space-y-6",children:[a.jsx("h1",{className:`text-3xl font-bold text-gray-900 ${t?"font-arabic":""}`,children:e("admin.viewOrder")}),a.jsx("div",{className:"bg-white rounded-lg shadow-sm border border-gray-200 p-6",children:a.jsx("p",{className:`text-gray-600 ${t?"font-arabic":""}`,children:e("admin.orderViewComingSoon")})})]})}};class Li{getCustomerProfile(t){return H.getCustomerData(t).profile}updateCustomerProfile(t,r){const s=H.getCustomerData(t);s.profile={...s.profile,...r},H.updateCustomerData(t,{profile:s.profile})}getCustomerWishlist(t){return H.getCustomerData(t).wishlist}getWishlist(t){return this.getCustomerWishlist(t)}addToWishlist(t,r){H.addToWishlist(t,r)}removeFromWishlist(t,r){try{return H.removeFromWishlist(t,r),!0}catch(s){return console.error("Error removing from wishlist:",s),!1}}clearWishlist(t){try{const r=H.getCustomerData(t);return r.wishlist=[],H.updateCustomerData(t,r),!0}catch(r){return console.error("Error clearing wishlist:",r),!1}}isInWishlist(t,r){return this.getCustomerWishlist(t).some(n=>n.productId===r)}getCustomerAddresses(t){return H.getCustomerData(t).addresses}addAddress(t,r){const s={...r,id:this.generateId()};return H.addAddress(t,s),s}updateAddress(t,r,s){return this.getCustomerAddresses(t).some(o=>o.id===r)?(H.updateAddress(t,r,s),!0):!1}deleteAddress(t,r){return this.getCustomerAddresses(t).some(i=>i.id===r)?(H.deleteAddress(t,r),!0):!1}setDefaultAddress(t,r){const s=this.getCustomerAddresses(t);return s.find(i=>i.id===r)?(s.forEach(i=>{i.id!==r&&H.updateAddress(t,i.id,{isDefault:!1})}),H.updateAddress(t,r,{isDefault:!0}),!0):!1}getDefaultAddress(t){return this.getCustomerAddresses(t).find(s=>s.isDefault)||null}getCustomerSettings(t){return H.getCustomerData(t).settings}updateCustomerSettings(t,r){const s=H.getCustomerData(t);s.settings={...s.settings,...r},H.updateCustomerData(t,{settings:s.settings})}updateNotificationPreferences(t,r){const s=H.getCustomerData(t);s.settings.notifications={...s.settings.notifications,...r},H.updateCustomerData(t,{settings:s.settings})}updatePreferences(t,r){const s=H.getCustomerData(t);s.settings.preferences={...s.settings.preferences,...r},H.updateCustomerData(t,{settings:s.settings})}getCustomerStats(t){const r=H.getCustomerData(t),s=r.orders,n=s.length,i=s.reduce((u,d)=>u+d.total,0),o=n>0?i/n:0,l={pending:s.filter(u=>u.status==="pending").length,processing:s.filter(u=>u.status==="processing").length,shipped:s.filter(u=>u.status==="shipped").length,delivered:s.filter(u=>u.status==="delivered").length,cancelled:s.filter(u=>u.status==="cancelled").length},c=s.sort((u,d)=>new Date(d.createdAt).getTime()-new Date(u.createdAt).getTime()).slice(0,5);return{totalOrders:n,totalSpent:i,avgOrderValue:o,statusCounts:l,recentOrders:c,wishlistCount:r.wishlist.length,addressCount:r.addresses.length}}getAllCustomers(){const t=H.getAllData();return Object.keys(t.customers).map(s=>{const n=t.customers[s],i=n.orders,o=i.length,l=i.reduce((u,d)=>u+d.total,0),c=i.sort((u,d)=>new Date(d.createdAt).getTime()-new Date(u.createdAt).getTime())[0];return{id:s,profile:n.profile,stats:{totalOrders:o,totalSpent:l,lastOrderDate:c==null?void 0:c.createdAt}}}).sort((s,n)=>n.stats.totalSpent-s.stats.totalSpent)}searchCustomers(t){const r=this.getAllCustomers(),s=t.toLowerCase();return r.filter(n=>{var o,l,c,u;const i=n.profile;return((o=i.firstName)==null?void 0:o.toLowerCase().includes(s))||((l=i.lastName)==null?void 0:l.toLowerCase().includes(s))||((c=i.email)==null?void 0:c.toLowerCase().includes(s))||((u=i.phone)==null?void 0:u.toLowerCase().includes(s))})}getCustomerDetails(t){const r=H.getCustomerData(t),s=this.getCustomerStats(t);return{profile:r.profile,orders:r.orders,wishlist:r.wishlist,addresses:r.addresses,settings:r.settings,stats:s}}validateCustomerProfile(t){const r=[];return t.email&&!/^[^\s@]+@[^\s@]+\.[^\s@]+$/.test(t.email)&&r.push("Invalid email format"),t.phone&&!/^\+?[\d\s-()]+$/.test(t.phone)&&r.push("Invalid phone number format"),t.firstName&&t.firstName.trim().length<2&&r.push("First name must be at least 2 characters"),t.lastName&&t.lastName.trim().length<2&&r.push("Last name must be at least 2 characters"),{isValid:r.length===0,errors:r}}validateAddress(t){const r=[];return(!t.firstName||t.firstName.trim().length===0)&&r.push("First name is required"),(!t.lastName||t.lastName.trim().length===0)&&r.push("Last name is required"),(!t.phone||t.phone.trim().length===0)&&r.push("Phone number is required"),(!t.street||t.street.trim().length===0)&&r.push("Street address is required"),(!t.city||t.city.trim().length===0)&&r.push("City is required"),(!t.postalCode||t.postalCode.trim().length===0)&&r.push("Postal code is required"),{isValid:r.length===0,errors:r}}getCustomerPaymentCards(t){return H.getCustomerData(t).paymentCards||[]}addPaymentCard(t,r){const s=H.getCustomerData(t),n={id:this.generateId(),...r,createdAt:new Date().toISOString()};return(!s.paymentCards||s.paymentCards.length===0||r.isDefault)&&(s.paymentCards&&s.paymentCards.forEach(i=>i.isDefault=!1),n.isDefault=!0),s.paymentCards=s.paymentCards||[],s.paymentCards.push(n),H.updateCustomerData(t,{paymentCards:s.paymentCards}),n}updatePaymentCard(t,r,s){var o;const n=H.getCustomerData(t),i=(o=n.paymentCards)==null?void 0:o.findIndex(l=>l.id===r);if(i===-1||i===void 0)throw new Error("Payment card not found");return s.isDefault&&n.paymentCards.forEach(l=>l.isDefault=!1),n.paymentCards[i]={...n.paymentCards[i],...s,updatedAt:new Date().toISOString()},H.updateCustomerData(t,{paymentCards:n.paymentCards}),n.paymentCards[i]}deletePaymentCard(t,r){var o;const s=H.getCustomerData(t),n=(o=s.paymentCards)==null?void 0:o.findIndex(l=>l.id===r);if(n===-1||n===void 0)throw new Error("Payment card not found");const i=s.paymentCards[n];return s.paymentCards.splice(n,1),i.isDefault&&s.paymentCards.length>0&&(s.paymentCards[0].isDefault=!0),H.updateCustomerData(t,{paymentCards:s.paymentCards}),i}setDefaultPaymentCard(t,r){var i;const s=H.getCustomerData(t),n=(i=s.paymentCards)==null?void 0:i.findIndex(o=>o.id===r);if(n===-1||n===void 0)throw new Error("Payment card not found");return s.paymentCards.forEach(o=>o.isDefault=!1),s.paymentCards[n].isDefault=!0,H.updateCustomerData(t,{paymentCards:s.paymentCards}),s.paymentCards[n]}generateId(){return Date.now().toString(36)+Math.random().toString(36).substring(2)}}const Xv=new Li,Zv=()=>{const{t:e,isRTL:t}=oe(),[r,s]=j.useState([]),[n,i]=j.useState(""),[o,l]=j.useState("all"),[c,u]=j.useState(!0);j.useEffect(()=>{d()},[]);const d=()=>{u(!0);try{const y=Xv.getAllCustomers();s(y)}catch(y){Q.error(e("admin.errorLoadingCustomers")),console.error("Error loading customers:",y)}finally{u(!1)}},f=["all","active","inactive"],m=r.filter(y=>{const p=y.email.toLowerCase().includes(n.toLowerCase())||y.id.toLowerCase().includes(n.toLowerCase()),h=o==="all"||y.status===o;return p&&h}),v=y=>{switch(y){case"active":return"bg-green-100 text-green-800";case"inactive":return"bg-gray-100 text-gray-800";default:return"bg-gray-100 text-gray-800"}};return a.jsxs(Ps,{children:[a.jsx(ee,{index:!0,element:a.jsx(g,{})}),a.jsx(ee,{path:"view/:id",element:a.jsx(w,{})})]});function g(){return c?a.jsx("div",{className:"flex items-center justify-center h-64",children:a.jsx("div",{className:"animate-spin rounded-full h-12 w-12 border-b-2 border-blue-600"})}):a.jsxs("div",{className:"space-y-6",children:[a.jsxs("div",{children:[a.jsx("h1",{className:`text-3xl font-bold text-gray-900 ${t?"font-arabic":""}`,children:e("admin.customerManagement")}),a.jsx("p",{className:`mt-2 text-gray-600 ${t?"font-arabic":""}`,children:e("admin.manageCustomerAccounts")})]}),a.jsxs("div",{className:"grid grid-cols-1 md:grid-cols-3 gap-6",children:[a.jsx("div",{className:"bg-white rounded-lg shadow-sm border border-gray-200 p-6",children:a.jsxs("div",{className:`flex items-center ${t?"flex-row-reverse":""}`,children:[a.jsx("div",{className:"flex-shrink-0",children:a.jsx(Oi,{className:"h-8 w-8 text-blue-600"})}),a.jsxs("div",{className:`${t?"mr-4 text-right":"ml-4 text-left"}`,children:[a.jsx("p",{className:`text-sm font-medium text-gray-600 ${t?"font-arabic":""}`,children:e("admin.totalCustomers")}),a.jsx("p",{className:"text-2xl font-semibold text-gray-900",children:r.length})]})]})}),a.jsx("div",{className:"bg-white rounded-lg shadow-sm border border-gray-200 p-6",children:a.jsxs("div",{className:`flex items-center ${t?"flex-row-reverse":""}`,children:[a.jsx("div",{className:"flex-shrink-0",children:a.jsx(In,{className:"h-8 w-8 text-green-600"})}),a.jsxs("div",{className:`${t?"mr-4 text-right":"ml-4 text-left"}`,children:[a.jsx("p",{className:`text-sm font-medium text-gray-600 ${t?"font-arabic":""}`,children:e("admin.activeCustomers")}),a.jsx("p",{className:"text-2xl font-semibold text-gray-900",children:r.filter(y=>y.status==="active").length})]})]})}),a.jsx("div",{className:"bg-white rounded-lg shadow-sm border border-gray-200 p-6",children:a.jsxs("div",{className:`flex items-center ${t?"flex-row-reverse":""}`,children:[a.jsx("div",{className:"flex-shrink-0",children:a.jsx(jl,{className:"h-8 w-8 text-purple-600"})}),a.jsxs("div",{className:`${t?"mr-4 text-right":"ml-4 text-left"}`,children:[a.jsx("p",{className:`text-sm font-medium text-gray-600 ${t?"font-arabic":""}`,children:e("admin.newCustomersThisMonth")}),a.jsx("p",{className:"text-2xl font-semibold text-gray-900",children:r.filter(y=>{const p=new Date(y.joinDate||Date.now()),h=new Date;return p.getMonth()===h.getMonth()&&p.getFullYear()===h.getFullYear()}).length})]})]})}),a.jsx("div",{className:"bg-white rounded-lg shadow-sm border border-gray-200 p-6",children:a.jsxs("div",{className:`flex items-center justify-between ${t?"flex-row-reverse":""}`,children:[a.jsxs("div",{className:t?"text-right":"text-left",children:[a.jsx("p",{className:`text-sm font-medium text-gray-600 ${t?"font-arabic":""}`,children:e("admin.activeCustomers")}),a.jsx("p",{className:"text-2xl font-bold text-gray-900 mt-2",children:r.filter(y=>y.status==="active").length})]}),a.jsx("div",{className:"h-12 w-12 bg-green-100 rounded-lg flex items-center justify-center",children:a.jsx(jl,{className:"h-6 w-6 text-green-600"})})]})}),a.jsx("div",{className:"bg-white rounded-lg shadow-sm border border-gray-200 p-6",children:a.jsxs("div",{className:`flex items-center justify-between ${t?"flex-row-reverse":""}`,children:[a.jsxs("div",{className:t?"text-right":"text-left",children:[a.jsx("p",{className:`text-sm font-medium text-gray-600 ${t?"font-arabic":""}`,children:e("admin.averageSpent")}),a.jsxs("p",{className:"text-2xl font-bold text-gray-900 mt-2",children:[Math.round(r.reduce((y,p)=>y+p.totalSpent,0)/r.length)," ",e("currency.symbol")]})]}),a.jsx("div",{className:"h-12 w-12 bg-purple-100 rounded-lg flex items-center justify-center",children:a.jsx(wr,{className:"h-6 w-6 text-purple-600"})})]})})]}),a.jsx("div",{className:"bg-white rounded-lg shadow-sm border border-gray-200 p-6",children:a.jsxs("div",{className:`grid grid-cols-1 md:grid-cols-2 gap-4 ${t?"text-right":"text-left"}`,children:[a.jsxs("div",{children:[a.jsx("label",{className:`block text-sm font-medium text-gray-700 mb-2 ${t?"font-arabic":""}`,children:e("common.search")}),a.jsxs("div",{className:"relative",children:[a.jsx(js,{className:`absolute top-3 ${t?"right-3":"left-3"} h-5 w-5 text-gray-400`}),a.jsx("input",{type:"text",value:n,onChange:y=>i(y.target.value),placeholder:e("admin.searchCustomers"),className:`
                    w-full ${t?"pr-10 pl-4 font-arabic":"pl-10 pr-4"} py-2 border border-gray-300 rounded-lg 
                    focus:ring-2 focus:ring-black focus:border-transparent
                  `})]})]}),a.jsxs("div",{children:[a.jsx("label",{className:`block text-sm font-medium text-gray-700 mb-2 ${t?"font-arabic":""}`,children:e("admin.status")}),a.jsx("select",{value:o,onChange:y=>l(y.target.value),className:`
                  w-full px-4 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-black focus:border-transparent
                  ${t?"font-arabic":""}
                `,children:f.map(y=>a.jsx("option",{value:y,children:e(`admin.${y}`)},y))})]})]})}),a.jsxs("div",{className:"bg-white rounded-lg shadow-sm border border-gray-200 overflow-hidden",children:[a.jsx("div",{className:"overflow-x-auto",children:a.jsxs("table",{className:"min-w-full divide-y divide-gray-200",children:[a.jsx("thead",{className:"bg-gray-50",children:a.jsxs("tr",{children:[a.jsx("th",{className:`px-6 py-3 text-xs font-medium text-gray-500 uppercase tracking-wider ${t?"text-right font-arabic":"text-left"}`,children:e("admin.customer")}),a.jsx("th",{className:`px-6 py-3 text-xs font-medium text-gray-500 uppercase tracking-wider ${t?"text-right font-arabic":"text-left"}`,children:e("admin.contact")}),a.jsx("th",{className:`px-6 py-3 text-xs font-medium text-gray-500 uppercase tracking-wider ${t?"text-right font-arabic":"text-left"}`,children:e("admin.orders")}),a.jsx("th",{className:`px-6 py-3 text-xs font-medium text-gray-500 uppercase tracking-wider ${t?"text-right font-arabic":"text-left"}`,children:e("admin.totalSpent")}),a.jsx("th",{className:`px-6 py-3 text-xs font-medium text-gray-500 uppercase tracking-wider ${t?"text-right font-arabic":"text-left"}`,children:e("admin.status")}),a.jsx("th",{className:`px-6 py-3 text-xs font-medium text-gray-500 uppercase tracking-wider ${t?"text-right font-arabic":"text-left"}`,children:e("common.actions")})]})}),a.jsx("tbody",{className:"bg-white divide-y divide-gray-200",children:m.map(y=>a.jsxs("tr",{className:"hover:bg-gray-50",children:[a.jsxs("td",{className:`px-6 py-4 whitespace-nowrap ${t?"text-right":"text-left"}`,children:[a.jsxs("div",{className:`text-sm font-medium text-gray-900 ${t?"font-arabic":""}`,children:[y.firstName," ",y.lastName]}),a.jsxs("div",{className:"text-sm text-gray-500",children:["ID: ",y.id]})]}),a.jsxs("td",{className:`px-6 py-4 whitespace-nowrap ${t?"text-right":"text-left"}`,children:[a.jsx("div",{className:"text-sm text-gray-900",children:y.email}),y.phone&&a.jsx("div",{className:"text-sm text-gray-500",children:y.phone})]}),a.jsx("td",{className:`px-6 py-4 whitespace-nowrap text-sm text-gray-900 ${t?"text-right":"text-left"}`,children:y.totalOrders}),a.jsxs("td",{className:`px-6 py-4 whitespace-nowrap text-sm text-gray-900 ${t?"text-right":"text-left"}`,children:[y.totalSpent," ",e("currency.symbol")]}),a.jsx("td",{className:`px-6 py-4 whitespace-nowrap ${t?"text-right":"text-left"}`,children:a.jsx("span",{className:`inline-flex px-2 py-1 text-xs font-semibold rounded-full ${v(y.status)}`,children:e(`admin.${y.status}`)})}),a.jsx("td",{className:`px-6 py-4 whitespace-nowrap text-sm font-medium ${t?"text-right":"text-left"}`,children:a.jsx("button",{onClick:()=>window.location.href=`/admin/customers/view/${y.id}`,className:"text-blue-600 hover:text-blue-900",title:e("common.view"),children:a.jsx(ms,{className:"h-4 w-4"})})})]},y.id))})]})}),m.length===0&&a.jsx("div",{className:"text-center py-12",children:a.jsx("p",{className:`text-gray-500 ${t?"font-arabic":""}`,children:e("admin.noCustomersFound")})})]})]})}function w(){return a.jsxs("div",{className:"space-y-6",children:[a.jsx("h1",{className:`text-3xl font-bold text-gray-900 ${t?"font-arabic":""}`,children:e("admin.viewCustomer")}),a.jsx("div",{className:"bg-white rounded-lg shadow-sm border border-gray-200 p-6",children:a.jsx("p",{className:`text-gray-600 ${t?"font-arabic":""}`,children:e("admin.customerViewComingSoon")})})]})}},e1=()=>{const{t:e,isRTL:t}=oe(),[r,s]=j.useState("month"),n=[{value:"week",label:e("analytics.thisWeek")},{value:"month",label:e("analytics.thisMonth")},{value:"quarter",label:e("analytics.thisQuarter")},{value:"year",label:e("analytics.thisYear")}],i={revenue:{current:125430,previous:112350,change:11.6},orders:{current:1234,previous:1156,change:6.7},customers:{current:856,previous:742,change:15.4},avgOrderValue:{current:101.6,previous:97.2,change:4.5}},o=[{name:"قميص أبيض كلاسيكي",nameEn:"Classic White Shirt",sales:145,revenue:21750},{name:"بنطلون جينز أزرق",nameEn:"Blue Jeans",sales:132,revenue:19800},{name:"فستان صيفي أحمر",nameEn:"Red Summer Dress",sales:98,revenue:31360},{name:"تيشيرت قطني",nameEn:"Cotton T-Shirt",sales:87,revenue:5220},{name:"جاكيت شتوي",nameEn:"Winter Jacket",sales:76,revenue:22800}],l=[{category:"shirts",sales:45,revenue:67500},{category:"pants",sales:32,revenue:48e3},{category:"dresses",sales:18,revenue:57600},{category:"accessories",sales:5,revenue:7500}],c=m=>new Intl.NumberFormat(t?"ar-SA":"en-US").format(m),u=m=>`${c(m)} ${e("currency.symbol")}`,d=m=>m>=0?a.jsx(In,{className:"h-4 w-4"}):a.jsx(bl,{className:"h-4 w-4"}),f=m=>m>=0?"text-green-600":"text-red-600";return a.jsxs("div",{className:"space-y-6",children:[a.jsxs("div",{className:`flex items-center justify-between ${t?"flex-row-reverse":""}`,children:[a.jsxs("div",{children:[a.jsx("h1",{className:`text-3xl font-bold text-gray-900 ${t?"font-arabic":""}`,children:e("admin.salesAnalytics")}),a.jsx("p",{className:`mt-2 text-gray-600 ${t?"font-arabic":""}`,children:e("admin.trackYourSalesPerformance")})]}),a.jsx("div",{children:a.jsx("select",{value:r,onChange:m=>s(m.target.value),className:`
              px-4 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-black focus:border-transparent
              ${t?"font-arabic":""}
            `,children:n.map(m=>a.jsx("option",{value:m.value,children:m.label},m.value))})})]}),a.jsxs("div",{className:"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6",children:[a.jsx("div",{className:"bg-white rounded-lg shadow-sm border border-gray-200 p-6",children:a.jsxs("div",{className:`flex items-center justify-between ${t?"flex-row-reverse":""}`,children:[a.jsxs("div",{className:t?"text-right":"text-left",children:[a.jsx("p",{className:`text-sm font-medium text-gray-600 ${t?"font-arabic":""}`,children:e("admin.totalRevenue")}),a.jsx("p",{className:"text-2xl font-bold text-gray-900 mt-2",children:u(i.revenue.current)}),a.jsxs("p",{className:`text-sm mt-2 flex items-center ${f(i.revenue.change)} ${t?"flex-row-reverse":""}`,children:[d(i.revenue.change),a.jsxs("span",{className:t?"mr-1":"ml-1",children:["+",i.revenue.change,"%"]})]})]}),a.jsx("div",{className:"h-12 w-12 bg-green-100 rounded-lg flex items-center justify-center",children:a.jsx($i,{className:"h-6 w-6 text-green-600"})})]})}),a.jsx("div",{className:"bg-white rounded-lg shadow-sm border border-gray-200 p-6",children:a.jsxs("div",{className:`flex items-center justify-between ${t?"flex-row-reverse":""}`,children:[a.jsxs("div",{className:t?"text-right":"text-left",children:[a.jsx("p",{className:`text-sm font-medium text-gray-600 ${t?"font-arabic":""}`,children:e("admin.totalOrders")}),a.jsx("p",{className:"text-2xl font-bold text-gray-900 mt-2",children:c(i.orders.current)}),a.jsxs("p",{className:`text-sm mt-2 flex items-center ${f(i.orders.change)} ${t?"flex-row-reverse":""}`,children:[d(i.orders.change),a.jsxs("span",{className:t?"mr-1":"ml-1",children:["+",i.orders.change,"%"]})]})]}),a.jsx("div",{className:"h-12 w-12 bg-blue-100 rounded-lg flex items-center justify-center",children:a.jsx(Es,{className:"h-6 w-6 text-blue-600"})})]})}),a.jsx("div",{className:"bg-white rounded-lg shadow-sm border border-gray-200 p-6",children:a.jsxs("div",{className:`flex items-center justify-between ${t?"flex-row-reverse":""}`,children:[a.jsxs("div",{className:t?"text-right":"text-left",children:[a.jsx("p",{className:`text-sm font-medium text-gray-600 ${t?"font-arabic":""}`,children:e("admin.newCustomers")}),a.jsx("p",{className:"text-2xl font-bold text-gray-900 mt-2",children:c(i.customers.current)}),a.jsxs("p",{className:`text-sm mt-2 flex items-center ${f(i.customers.change)} ${t?"flex-row-reverse":""}`,children:[d(i.customers.change),a.jsxs("span",{className:t?"mr-1":"ml-1",children:["+",i.customers.change,"%"]})]})]}),a.jsx("div",{className:"h-12 w-12 bg-purple-100 rounded-lg flex items-center justify-center",children:a.jsx(Oi,{className:"h-6 w-6 text-purple-600"})})]})}),a.jsx("div",{className:"bg-white rounded-lg shadow-sm border border-gray-200 p-6",children:a.jsxs("div",{className:`flex items-center justify-between ${t?"flex-row-reverse":""}`,children:[a.jsxs("div",{className:t?"text-right":"text-left",children:[a.jsx("p",{className:`text-sm font-medium text-gray-600 ${t?"font-arabic":""}`,children:e("admin.avgOrderValue")}),a.jsx("p",{className:"text-2xl font-bold text-gray-900 mt-2",children:u(i.avgOrderValue.current)}),a.jsxs("p",{className:`text-sm mt-2 flex items-center ${f(i.avgOrderValue.change)} ${t?"flex-row-reverse":""}`,children:[d(i.avgOrderValue.change),a.jsxs("span",{className:t?"mr-1":"ml-1",children:["+",i.avgOrderValue.change,"%"]})]})]}),a.jsx("div",{className:"h-12 w-12 bg-orange-100 rounded-lg flex items-center justify-center",children:a.jsx(Nl,{className:"h-6 w-6 text-orange-600"})})]})})]}),a.jsxs("div",{className:"grid grid-cols-1 lg:grid-cols-2 gap-6",children:[a.jsxs("div",{className:"bg-white rounded-lg shadow-sm border border-gray-200",children:[a.jsx("div",{className:"p-6 border-b border-gray-200",children:a.jsxs("h3",{className:`text-lg font-semibold text-gray-900 flex items-center ${t?"font-arabic flex-row-reverse":""}`,children:[a.jsx(Je,{className:`h-5 w-5 text-gray-600 ${t?"ml-2":"mr-2"}`}),e("admin.topProducts")]})}),a.jsx("div",{className:"p-6",children:a.jsx("div",{className:"space-y-4",children:o.map((m,v)=>a.jsxs("div",{className:`flex items-center justify-between ${t?"flex-row-reverse":""}`,children:[a.jsxs("div",{className:`flex items-center ${t?"flex-row-reverse":""}`,children:[a.jsx("div",{className:`w-8 h-8 bg-gray-100 rounded-full flex items-center justify-center text-sm font-medium text-gray-600 ${t?"ml-3":"mr-3"}`,children:v+1}),a.jsxs("div",{className:t?"text-right":"text-left",children:[a.jsx("p",{className:`font-medium text-gray-900 ${t?"font-arabic":""}`,children:t?m.name:m.nameEn}),a.jsxs("p",{className:"text-sm text-gray-500",children:[m.sales," ",e("admin.sales")]})]})]}),a.jsx("div",{className:`text-sm font-medium text-gray-900 ${t?"text-left":"text-right"}`,children:u(m.revenue)})]},v))})})]}),a.jsxs("div",{className:"bg-white rounded-lg shadow-sm border border-gray-200",children:[a.jsx("div",{className:"p-6 border-b border-gray-200",children:a.jsxs("h3",{className:`text-lg font-semibold text-gray-900 flex items-center ${t?"font-arabic flex-row-reverse":""}`,children:[a.jsx(Nl,{className:`h-5 w-5 text-gray-600 ${t?"ml-2":"mr-2"}`}),e("admin.salesByCategory")]})}),a.jsx("div",{className:"p-6",children:a.jsx("div",{className:"space-y-4",children:l.map((m,v)=>a.jsxs("div",{className:`flex items-center justify-between ${t?"flex-row-reverse":""}`,children:[a.jsxs("div",{className:t?"text-right":"text-left",children:[a.jsx("p",{className:`font-medium text-gray-900 ${t?"font-arabic":""}`,children:e(`categories.${m.category}`)}),a.jsxs("p",{className:"text-sm text-gray-500",children:[m.sales,"% ",e("admin.ofTotalSales")]})]}),a.jsx("div",{className:`text-sm font-medium text-gray-900 ${t?"text-left":"text-right"}`,children:u(m.revenue)})]},v))})})]})]}),a.jsxs("div",{className:"bg-white rounded-lg shadow-sm border border-gray-200 p-6",children:[a.jsx("h3",{className:`text-lg font-semibold text-gray-900 mb-4 ${t?"font-arabic":""}`,children:e("admin.salesChart")}),a.jsx("div",{className:"h-64 bg-gray-50 rounded-lg flex items-center justify-center",children:a.jsxs("div",{className:`text-center ${t?"font-arabic":""}`,children:[a.jsx(jl,{className:"h-12 w-12 text-gray-400 mx-auto mb-4"}),a.jsx("p",{className:"text-gray-500",children:e("admin.chartComingSoon")})]})})]})]})},t1=()=>{const{t:e,isRTL:t}=oe(),{products:r}=Ci(),[s,n]=j.useState(""),[i,o]=j.useState("all"),l=[{value:"all",label:e("admin.allProducts")},{value:"low",label:e("admin.lowStock")},{value:"out",label:e("admin.outOfStock")},{value:"good",label:e("admin.goodStock")}],c=r.filter(g=>{const w=g.name.toLowerCase().includes(s.toLowerCase())||g.nameAr.toLowerCase().includes(s.toLowerCase());let y=!0;return i==="low"?y=g.stock>0&&g.stock<=10:i==="out"?y=g.stock===0:i==="good"&&(y=g.stock>10),w&&y}),u=g=>g===0?"bg-red-100 text-red-800":g<=10?"bg-yellow-100 text-yellow-800":"bg-green-100 text-green-800",d=g=>g===0?a.jsx(Cn,{className:"h-4 w-4"}):g<=10?a.jsx(bl,{className:"h-4 w-4"}):a.jsx(Je,{className:"h-4 w-4"}),f=r.filter(g=>g.stock>0&&g.stock<=10).length,m=r.filter(g=>g.stock===0).length,v=r.reduce((g,w)=>g+w.price*w.stock,0);return a.jsxs("div",{className:"space-y-6",children:[a.jsxs("div",{children:[a.jsx("h1",{className:`text-3xl font-bold text-gray-900 ${t?"font-arabic":""}`,children:e("admin.inventoryManagement")}),a.jsx("p",{className:`mt-2 text-gray-600 ${t?"font-arabic":""}`,children:e("admin.manageProductInventory")})]}),a.jsxs("div",{className:"grid grid-cols-1 md:grid-cols-4 gap-6",children:[a.jsx("div",{className:"bg-white rounded-lg shadow-sm border border-gray-200 p-6",children:a.jsxs("div",{className:`flex items-center justify-between ${t?"flex-row-reverse":""}`,children:[a.jsxs("div",{className:t?"text-right":"text-left",children:[a.jsx("p",{className:`text-sm font-medium text-gray-600 ${t?"font-arabic":""}`,children:e("admin.totalProducts")}),a.jsx("p",{className:"text-2xl font-bold text-gray-900 mt-2",children:r.length})]}),a.jsx("div",{className:"h-12 w-12 bg-blue-100 rounded-lg flex items-center justify-center",children:a.jsx(Je,{className:"h-6 w-6 text-blue-600"})})]})}),a.jsx("div",{className:"bg-white rounded-lg shadow-sm border border-gray-200 p-6",children:a.jsxs("div",{className:`flex items-center justify-between ${t?"flex-row-reverse":""}`,children:[a.jsxs("div",{className:t?"text-right":"text-left",children:[a.jsx("p",{className:`text-sm font-medium text-gray-600 ${t?"font-arabic":""}`,children:e("admin.lowStockItems")}),a.jsx("p",{className:"text-2xl font-bold text-yellow-600 mt-2",children:f})]}),a.jsx("div",{className:"h-12 w-12 bg-yellow-100 rounded-lg flex items-center justify-center",children:a.jsx(bl,{className:"h-6 w-6 text-yellow-600"})})]})}),a.jsx("div",{className:"bg-white rounded-lg shadow-sm border border-gray-200 p-6",children:a.jsxs("div",{className:`flex items-center justify-between ${t?"flex-row-reverse":""}`,children:[a.jsxs("div",{className:t?"text-right":"text-left",children:[a.jsx("p",{className:`text-sm font-medium text-gray-600 ${t?"font-arabic":""}`,children:e("admin.outOfStock")}),a.jsx("p",{className:"text-2xl font-bold text-red-600 mt-2",children:m})]}),a.jsx("div",{className:"h-12 w-12 bg-red-100 rounded-lg flex items-center justify-center",children:a.jsx(Cn,{className:"h-6 w-6 text-red-600"})})]})}),a.jsx("div",{className:"bg-white rounded-lg shadow-sm border border-gray-200 p-6",children:a.jsxs("div",{className:`flex items-center justify-between ${t?"flex-row-reverse":""}`,children:[a.jsxs("div",{className:t?"text-right":"text-left",children:[a.jsx("p",{className:`text-sm font-medium text-gray-600 ${t?"font-arabic":""}`,children:e("admin.inventoryValue")}),a.jsxs("p",{className:"text-2xl font-bold text-gray-900 mt-2",children:[v.toLocaleString()," ",e("currency.symbol")]})]}),a.jsx("div",{className:"h-12 w-12 bg-green-100 rounded-lg flex items-center justify-center",children:a.jsx(Je,{className:"h-6 w-6 text-green-600"})})]})})]}),a.jsx("div",{className:"bg-white rounded-lg shadow-sm border border-gray-200 p-6",children:a.jsxs("div",{className:`grid grid-cols-1 md:grid-cols-2 gap-4 ${t?"text-right":"text-left"}`,children:[a.jsxs("div",{children:[a.jsx("label",{className:`block text-sm font-medium text-gray-700 mb-2 ${t?"font-arabic":""}`,children:e("common.search")}),a.jsxs("div",{className:"relative",children:[a.jsx(js,{className:`absolute top-3 ${t?"right-3":"left-3"} h-5 w-5 text-gray-400`}),a.jsx("input",{type:"text",value:s,onChange:g=>n(g.target.value),placeholder:e("admin.searchProducts"),className:`
                  w-full ${t?"pr-10 pl-4 font-arabic":"pl-10 pr-4"} py-2 border border-gray-300 rounded-lg 
                  focus:ring-2 focus:ring-black focus:border-transparent
                `})]})]}),a.jsxs("div",{children:[a.jsx("label",{className:`block text-sm font-medium text-gray-700 mb-2 ${t?"font-arabic":""}`,children:e("admin.stockStatus")}),a.jsx("select",{value:i,onChange:g=>o(g.target.value),className:`
                w-full px-4 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-black focus:border-transparent
                ${t?"font-arabic":""}
              `,children:l.map(g=>a.jsx("option",{value:g.value,children:g.label},g.value))})]})]})}),a.jsxs("div",{className:"bg-white rounded-lg shadow-sm border border-gray-200 overflow-hidden",children:[a.jsx("div",{className:"overflow-x-auto",children:a.jsxs("table",{className:"min-w-full divide-y divide-gray-200",children:[a.jsx("thead",{className:"bg-gray-50",children:a.jsxs("tr",{children:[a.jsx("th",{className:`px-6 py-3 text-xs font-medium text-gray-500 uppercase tracking-wider ${t?"text-right font-arabic":"text-left"}`,children:e("admin.product")}),a.jsx("th",{className:`px-6 py-3 text-xs font-medium text-gray-500 uppercase tracking-wider ${t?"text-right font-arabic":"text-left"}`,children:e("admin.sku")}),a.jsx("th",{className:`px-6 py-3 text-xs font-medium text-gray-500 uppercase tracking-wider ${t?"text-right font-arabic":"text-left"}`,children:e("admin.currentStock")}),a.jsx("th",{className:`px-6 py-3 text-xs font-medium text-gray-500 uppercase tracking-wider ${t?"text-right font-arabic":"text-left"}`,children:e("admin.unitPrice")}),a.jsx("th",{className:`px-6 py-3 text-xs font-medium text-gray-500 uppercase tracking-wider ${t?"text-right font-arabic":"text-left"}`,children:e("admin.totalValue")}),a.jsx("th",{className:`px-6 py-3 text-xs font-medium text-gray-500 uppercase tracking-wider ${t?"text-right font-arabic":"text-left"}`,children:e("admin.status")}),a.jsx("th",{className:`px-6 py-3 text-xs font-medium text-gray-500 uppercase tracking-wider ${t?"text-right font-arabic":"text-left"}`,children:e("common.actions")})]})}),a.jsx("tbody",{className:"bg-white divide-y divide-gray-200",children:c.map(g=>a.jsxs("tr",{className:"hover:bg-gray-50",children:[a.jsx("td",{className:"px-6 py-4 whitespace-nowrap",children:a.jsxs("div",{className:`flex items-center ${t?"flex-row-reverse":""}`,children:[a.jsx("img",{src:g.images[0],alt:t?g.nameAr:g.name,className:"h-12 w-12 rounded-lg object-cover"}),a.jsxs("div",{className:t?"mr-4 text-right":"ml-4 text-left",children:[a.jsx("div",{className:`text-sm font-medium text-gray-900 ${t?"font-arabic":""}`,children:t?g.nameAr:g.name}),a.jsx("div",{className:"text-sm text-gray-500",children:e(`categories.${g.category}`)})]})]})}),a.jsxs("td",{className:`px-6 py-4 whitespace-nowrap text-sm text-gray-900 ${t?"text-right":"text-left"}`,children:["SKU-",g.id]}),a.jsx("td",{className:`px-6 py-4 whitespace-nowrap text-sm text-gray-900 ${t?"text-right":"text-left"}`,children:g.stock}),a.jsxs("td",{className:`px-6 py-4 whitespace-nowrap text-sm text-gray-900 ${t?"text-right":"text-left"}`,children:[g.price," ",e("currency.symbol")]}),a.jsxs("td",{className:`px-6 py-4 whitespace-nowrap text-sm text-gray-900 ${t?"text-right":"text-left"}`,children:[(g.price*g.stock).toLocaleString()," ",e("currency.symbol")]}),a.jsx("td",{className:`px-6 py-4 whitespace-nowrap ${t?"text-right":"text-left"}`,children:a.jsxs("span",{className:`inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium ${u(g.stock)} ${t?"flex-row-reverse":""}`,children:[d(g.stock),a.jsx("span",{className:t?"mr-1 font-arabic":"ml-1",children:g.stock===0?e("admin.outOfStock"):g.stock<=10?e("admin.lowStock"):e("admin.inStock")})]})}),a.jsx("td",{className:`px-6 py-4 whitespace-nowrap text-sm font-medium ${t?"text-right":"text-left"}`,children:a.jsxs("div",{className:`flex items-center space-x-2 ${t?"space-x-reverse":""}`,children:[a.jsx("button",{onClick:()=>console.log("Update stock for:",g.id),className:"text-blue-600 hover:text-blue-900",title:e("admin.updateStock"),children:a.jsx(Pi,{className:"h-4 w-4"})}),a.jsx("button",{onClick:()=>console.log("Restock:",g.id),className:"text-green-600 hover:text-green-900",title:e("admin.restock"),children:a.jsx(Ur,{className:"h-4 w-4"})})]})})]},g.id))})]})}),c.length===0&&a.jsx("div",{className:"text-center py-12",children:a.jsx("p",{className:`text-gray-500 ${t?"font-arabic":""}`,children:e("admin.noProductsFound")})})]})]})},r1=()=>{const{t:e,isRTL:t}=oe(),[r,s]=j.useState("general"),n=[{id:"general",label:e("admin.generalSettings"),icon:uv},{id:"payment",label:e("admin.paymentMethods"),icon:_n},{id:"shipping",label:e("admin.shippingOptions"),icon:Ei},{id:"notifications",label:e("admin.notifications"),icon:vh}],[i,o]=j.useState({general:{storeName:"متجر الملابس الأنيق",storeNameEn:"Elegant Clothing Store",description:"متجر متخصص في بيع الملابس العصرية والأنيقة",descriptionEn:"Specialized store for modern and elegant clothing",email:"<EMAIL>",phone:"+************",address:"شارع الملك فهد، الرياض، السعودية",addressEn:"King Fahd Street, Riyadh, Saudi Arabia",currency:"SAR",timezone:"Asia/Riyadh"},payment:{enableCreditCard:!0,enablePayPal:!1,enableBankTransfer:!0,enableCashOnDelivery:!0,taxRate:15},shipping:{freeShippingThreshold:200,localShippingCost:25,internationalShippingCost:75,processingTime:"1-2",shippingTime:"3-5"},notifications:{orderConfirmation:!0,orderStatusUpdate:!0,lowStockAlert:!0,newCustomerAlert:!0,emailNotifications:!0,smsNotifications:!1}}),l=()=>{console.log("Save settings:",i),alert(e("admin.settingsSaved"))},c=(u,d,f)=>{o(m=>({...m,[u]:{...m[u],[d]:f}}))};return a.jsxs("div",{className:"space-y-6",children:[a.jsxs("div",{className:`flex items-center justify-between ${t?"flex-row-reverse":""}`,children:[a.jsxs("div",{children:[a.jsx("h1",{className:`text-3xl font-bold text-gray-900 ${t?"font-arabic":""}`,children:e("admin.storeSettings")}),a.jsx("p",{className:`mt-2 text-gray-600 ${t?"font-arabic":""}`,children:e("admin.configureYourStore")})]}),a.jsxs("button",{onClick:l,className:`
            flex items-center px-4 py-2 bg-black text-white rounded-lg hover:bg-gray-800 transition-colors
            ${t?"flex-row-reverse":""}
          `,children:[a.jsx(cv,{className:`h-5 w-5 ${t?"ml-2":"mr-2"}`}),a.jsx("span",{className:t?"font-arabic":"",children:e("common.save")})]})]}),a.jsxs("div",{className:"bg-white rounded-lg shadow-sm border border-gray-200",children:[a.jsx("div",{className:"border-b border-gray-200",children:a.jsx("nav",{className:`flex space-x-8 ${t?"space-x-reverse":""} px-6`,children:n.map(u=>{const d=u.icon;return a.jsxs("button",{onClick:()=>s(u.id),className:`
                    flex items-center py-4 px-1 border-b-2 font-medium text-sm transition-colors
                    ${r===u.id?"border-black text-black":"border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300"}
                    ${t?"flex-row-reverse":""}
                  `,children:[a.jsx(d,{className:`h-5 w-5 ${t?"ml-2":"mr-2"}`}),a.jsx("span",{className:t?"font-arabic":"",children:u.label})]},u.id)})})}),a.jsxs("div",{className:"p-6",children:[r==="general"&&a.jsxs("div",{className:"space-y-6",children:[a.jsx("h3",{className:`text-lg font-semibold text-gray-900 ${t?"font-arabic":""}`,children:e("admin.generalInformation")}),a.jsxs("div",{className:"grid grid-cols-1 md:grid-cols-2 gap-6",children:[a.jsxs("div",{children:[a.jsx("label",{className:`block text-sm font-medium text-gray-700 mb-2 ${t?"font-arabic":""}`,children:e("admin.storeNameArabic")}),a.jsx("input",{type:"text",value:i.general.storeName,onChange:u=>c("general","storeName",u.target.value),className:`w-full px-4 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-black focus:border-transparent ${t?"text-right font-arabic":"text-left"}`})]}),a.jsxs("div",{children:[a.jsx("label",{className:`block text-sm font-medium text-gray-700 mb-2 ${t?"font-arabic":""}`,children:e("admin.storeNameEnglish")}),a.jsx("input",{type:"text",value:i.general.storeNameEn,onChange:u=>c("general","storeNameEn",u.target.value),className:"w-full px-4 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-black focus:border-transparent text-left"})]}),a.jsxs("div",{children:[a.jsx("label",{className:`block text-sm font-medium text-gray-700 mb-2 ${t?"font-arabic":""}`,children:e("admin.email")}),a.jsx("input",{type:"email",value:i.general.email,onChange:u=>c("general","email",u.target.value),className:"w-full px-4 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-black focus:border-transparent"})]}),a.jsxs("div",{children:[a.jsx("label",{className:`block text-sm font-medium text-gray-700 mb-2 ${t?"font-arabic":""}`,children:e("admin.phone")}),a.jsx("input",{type:"tel",value:i.general.phone,onChange:u=>c("general","phone",u.target.value),className:"w-full px-4 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-black focus:border-transparent"})]})]}),a.jsxs("div",{children:[a.jsx("label",{className:`block text-sm font-medium text-gray-700 mb-2 ${t?"font-arabic":""}`,children:e("admin.descriptionArabic")}),a.jsx("textarea",{value:i.general.description,onChange:u=>c("general","description",u.target.value),rows:3,className:`w-full px-4 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-black focus:border-transparent ${t?"text-right font-arabic":"text-left"}`})]}),a.jsxs("div",{children:[a.jsx("label",{className:`block text-sm font-medium text-gray-700 mb-2 ${t?"font-arabic":""}`,children:e("admin.descriptionEnglish")}),a.jsx("textarea",{value:i.general.descriptionEn,onChange:u=>c("general","descriptionEn",u.target.value),rows:3,className:"w-full px-4 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-black focus:border-transparent text-left"})]})]}),r==="payment"&&a.jsxs("div",{className:"space-y-6",children:[a.jsx("h3",{className:`text-lg font-semibold text-gray-900 ${t?"font-arabic":""}`,children:e("admin.paymentConfiguration")}),a.jsxs("div",{className:"space-y-4",children:[a.jsxs("div",{className:`flex items-center justify-between ${t?"flex-row-reverse":""}`,children:[a.jsxs("div",{children:[a.jsx("h4",{className:`font-medium text-gray-900 ${t?"font-arabic":""}`,children:e("admin.creditCardPayments")}),a.jsx("p",{className:`text-sm text-gray-500 ${t?"font-arabic":""}`,children:e("admin.acceptCreditCards")})]}),a.jsxs("label",{className:"relative inline-flex items-center cursor-pointer",children:[a.jsx("input",{type:"checkbox",checked:i.payment.enableCreditCard,onChange:u=>c("payment","enableCreditCard",u.target.checked),className:"sr-only peer"}),a.jsx("div",{className:"w-11 h-6 bg-gray-200 peer-focus:outline-none peer-focus:ring-4 peer-focus:ring-black/20 rounded-full peer peer-checked:after:translate-x-full peer-checked:after:border-white after:content-[''] after:absolute after:top-[2px] after:left-[2px] after:bg-white after:border-gray-300 after:border after:rounded-full after:h-5 after:w-5 after:transition-all peer-checked:bg-black"})]})]}),a.jsxs("div",{className:`flex items-center justify-between ${t?"flex-row-reverse":""}`,children:[a.jsxs("div",{children:[a.jsx("h4",{className:`font-medium text-gray-900 ${t?"font-arabic":""}`,children:e("admin.cashOnDelivery")}),a.jsx("p",{className:`text-sm text-gray-500 ${t?"font-arabic":""}`,children:e("admin.allowCashPayment")})]}),a.jsxs("label",{className:"relative inline-flex items-center cursor-pointer",children:[a.jsx("input",{type:"checkbox",checked:i.payment.enableCashOnDelivery,onChange:u=>c("payment","enableCashOnDelivery",u.target.checked),className:"sr-only peer"}),a.jsx("div",{className:"w-11 h-6 bg-gray-200 peer-focus:outline-none peer-focus:ring-4 peer-focus:ring-black/20 rounded-full peer peer-checked:after:translate-x-full peer-checked:after:border-white after:content-[''] after:absolute after:top-[2px] after:left-[2px] after:bg-white after:border-gray-300 after:border after:rounded-full after:h-5 after:w-5 after:transition-all peer-checked:bg-black"})]})]}),a.jsxs("div",{children:[a.jsxs("label",{className:`block text-sm font-medium text-gray-700 mb-2 ${t?"font-arabic":""}`,children:[e("admin.taxRate")," (%)"]}),a.jsx("input",{type:"number",value:i.payment.taxRate,onChange:u=>c("payment","taxRate",parseFloat(u.target.value)),className:"w-full px-4 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-black focus:border-transparent",min:"0",max:"100",step:"0.1"})]})]})]}),r==="shipping"&&a.jsxs("div",{className:"space-y-6",children:[a.jsx("h3",{className:`text-lg font-semibold text-gray-900 ${t?"font-arabic":""}`,children:e("admin.shippingConfiguration")}),a.jsxs("div",{className:"grid grid-cols-1 md:grid-cols-2 gap-6",children:[a.jsxs("div",{children:[a.jsxs("label",{className:`block text-sm font-medium text-gray-700 mb-2 ${t?"font-arabic":""}`,children:[e("admin.freeShippingThreshold")," (",e("currency.symbol"),")"]}),a.jsx("input",{type:"number",value:i.shipping.freeShippingThreshold,onChange:u=>c("shipping","freeShippingThreshold",parseFloat(u.target.value)),className:"w-full px-4 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-black focus:border-transparent"})]}),a.jsxs("div",{children:[a.jsxs("label",{className:`block text-sm font-medium text-gray-700 mb-2 ${t?"font-arabic":""}`,children:[e("admin.localShippingCost")," (",e("currency.symbol"),")"]}),a.jsx("input",{type:"number",value:i.shipping.localShippingCost,onChange:u=>c("shipping","localShippingCost",parseFloat(u.target.value)),className:"w-full px-4 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-black focus:border-transparent"})]}),a.jsxs("div",{children:[a.jsxs("label",{className:`block text-sm font-medium text-gray-700 mb-2 ${t?"font-arabic":""}`,children:[e("admin.processingTime")," (",e("admin.days"),")"]}),a.jsx("input",{type:"text",value:i.shipping.processingTime,onChange:u=>c("shipping","processingTime",u.target.value),className:"w-full px-4 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-black focus:border-transparent"})]}),a.jsxs("div",{children:[a.jsxs("label",{className:`block text-sm font-medium text-gray-700 mb-2 ${t?"font-arabic":""}`,children:[e("admin.shippingTime")," (",e("admin.days"),")"]}),a.jsx("input",{type:"text",value:i.shipping.shippingTime,onChange:u=>c("shipping","shippingTime",u.target.value),className:"w-full px-4 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-black focus:border-transparent"})]})]})]}),r==="notifications"&&a.jsxs("div",{className:"space-y-6",children:[a.jsx("h3",{className:`text-lg font-semibold text-gray-900 ${t?"font-arabic":""}`,children:e("admin.notificationSettings")}),a.jsxs("div",{className:"space-y-4",children:[a.jsxs("div",{className:`flex items-center justify-between ${t?"flex-row-reverse":""}`,children:[a.jsxs("div",{children:[a.jsx("h4",{className:`font-medium text-gray-900 ${t?"font-arabic":""}`,children:e("admin.orderConfirmationEmails")}),a.jsx("p",{className:`text-sm text-gray-500 ${t?"font-arabic":""}`,children:e("admin.sendOrderConfirmation")})]}),a.jsxs("label",{className:"relative inline-flex items-center cursor-pointer",children:[a.jsx("input",{type:"checkbox",checked:i.notifications.orderConfirmation,onChange:u=>c("notifications","orderConfirmation",u.target.checked),className:"sr-only peer"}),a.jsx("div",{className:"w-11 h-6 bg-gray-200 peer-focus:outline-none peer-focus:ring-4 peer-focus:ring-black/20 rounded-full peer peer-checked:after:translate-x-full peer-checked:after:border-white after:content-[''] after:absolute after:top-[2px] after:left-[2px] after:bg-white after:border-gray-300 after:border after:rounded-full after:h-5 after:w-5 after:transition-all peer-checked:bg-black"})]})]}),a.jsxs("div",{className:`flex items-center justify-between ${t?"flex-row-reverse":""}`,children:[a.jsxs("div",{children:[a.jsx("h4",{className:`font-medium text-gray-900 ${t?"font-arabic":""}`,children:e("admin.lowStockAlerts")}),a.jsx("p",{className:`text-sm text-gray-500 ${t?"font-arabic":""}`,children:e("admin.notifyLowStock")})]}),a.jsxs("label",{className:"relative inline-flex items-center cursor-pointer",children:[a.jsx("input",{type:"checkbox",checked:i.notifications.lowStockAlert,onChange:u=>c("notifications","lowStockAlert",u.target.checked),className:"sr-only peer"}),a.jsx("div",{className:"w-11 h-6 bg-gray-200 peer-focus:outline-none peer-focus:ring-4 peer-focus:ring-black/20 rounded-full peer peer-checked:after:translate-x-full peer-checked:after:border-white after:content-[''] after:absolute after:top-[2px] after:left-[2px] after:bg-white after:border-gray-300 after:border after:rounded-full after:h-5 after:w-5 after:transition-all peer-checked:bg-black"})]})]})]})]})]})]})]})},s1=()=>{const{user:e}=at(),{isRTL:t}=oe(),[r,s]=j.useState(!1);return!e||e.role!=="admin"?a.jsx(wa,{to:"/login",replace:!0}):a.jsxs("div",{className:`min-h-screen bg-gray-50 ${t?"rtl":"ltr"}`,children:[r&&a.jsx("div",{className:"fixed inset-0 z-40 bg-black bg-opacity-50 lg:hidden",onClick:()=>s(!1)}),a.jsx(Kv,{isOpen:r,onClose:()=>s(!1)}),a.jsxs("div",{className:`lg:${t?"mr-64":"ml-64"} flex flex-col min-h-screen`,children:[a.jsx(Qv,{onMenuClick:()=>s(!0)}),a.jsx("main",{className:"flex-1 p-4 lg:p-6",children:a.jsxs(Ps,{children:[a.jsx(ee,{index:!0,element:a.jsx(Yv,{})}),a.jsx(ee,{path:"products/*",element:a.jsx(Gv,{})}),a.jsx(ee,{path:"orders/*",element:a.jsx(Jv,{})}),a.jsx(ee,{path:"customers/*",element:a.jsx(Zv,{})}),a.jsx(ee,{path:"analytics",element:a.jsx(e1,{})}),a.jsx(ee,{path:"inventory",element:a.jsx(t1,{})}),a.jsx(ee,{path:"settings",element:a.jsx(r1,{})})]})})]})]})},n1=()=>{const{isRTL:e}=oe(),{user:t}=at(),[r,s]=j.useState([]),[n,i]=j.useState(!0);j.useEffect(()=>{t!=null&&t.id&&o()},[t==null?void 0:t.id]);const o=()=>{if(t!=null&&t.id){i(!0);try{s([{id:"1",orderNumber:"ORD-2024-001",status:"delivered",total:339.97,itemCount:3,createdAt:"2024-01-15T10:30:00Z"},{id:"2",orderNumber:"ORD-2024-002",status:"shipped",total:199.99,itemCount:1,createdAt:"2024-01-20T09:15:00Z"},{id:"3",orderNumber:"ORD-2024-003",status:"processing",total:129.99,itemCount:1,createdAt:"2024-01-25T11:00:00Z"},{id:"4",orderNumber:"ORD-2024-004",status:"pending",total:89.99,itemCount:1,createdAt:"2024-01-28T14:20:00Z"}])}catch(u){console.error("Error loading orders:",u)}finally{i(!1)}}},l=u=>({pending:e?"قيد الانتظار":"Pending",processing:e?"قيد المعالجة":"Processing",shipped:e?"تم الشحن":"Shipped",delivered:e?"تم التسليم":"Delivered",cancelled:e?"ملغي":"Cancelled"})[u]||u,c=u=>e?`${u.toFixed(2)} ر.س`:`SAR ${u.toFixed(2)}`;return n?a.jsxs("div",{className:"space-y-4",children:[a.jsx("h2",{className:`text-2xl font-bold text-gray-900 ${e?"font-arabic":""}`,children:e?"طلباتي":"My Orders"}),a.jsx("div",{className:"space-y-4",children:Array.from({length:3}).map((u,d)=>a.jsx("div",{className:"animate-pulse bg-gray-200 rounded-lg h-32"},d))})]}):r.length===0?a.jsxs("div",{className:"text-center py-12",children:[a.jsx(kc,{className:"h-16 w-16 text-gray-400 mx-auto mb-4"}),a.jsx("h3",{className:`text-xl font-semibold text-gray-900 mb-2 ${e?"font-arabic":""}`,children:e?"لا توجد طلبات":"No Orders Yet"}),a.jsx("p",{className:`text-gray-500 mb-6 ${e?"font-arabic":""}`,children:e?"لم تقم بأي طلبات حتى الآن. ابدأ التسوق الآن!":"You haven't placed any orders yet. Start shopping now!"}),a.jsx("button",{onClick:()=>window.location.href="/products",className:`bg-black text-white px-6 py-3 rounded-lg hover:bg-gray-800 transition-colors ${e?"font-arabic":""}`,children:e?"ابدأ التسوق":"Start Shopping"})]}):a.jsxs("div",{className:"space-y-6",children:[a.jsx("h2",{className:`text-2xl font-bold text-gray-900 ${e?"font-arabic":""}`,children:e?"طلباتي":"My Orders"}),a.jsx("div",{className:"space-y-4",children:r.map(u=>a.jsxs("div",{className:"bg-white border border-gray-200 rounded-lg p-6 hover:shadow-md transition-shadow",children:[a.jsxs("div",{className:`flex items-start justify-between mb-4 ${e?"flex-row-reverse":""}`,children:[a.jsxs("div",{className:`flex items-center space-x-3 ${e?"space-x-reverse":""}`,children:[a.jsx(Je,{className:"h-5 w-5 text-gray-600"}),a.jsxs("div",{children:[a.jsx("h3",{className:`font-semibold text-gray-900 ${e?"font-arabic":""}`,children:e?`طلب رقم: ${u.orderNumber}`:`Order #${u.orderNumber}`}),a.jsx("p",{className:`text-sm text-gray-500 ${e?"font-arabic":""}`,children:new Date(u.createdAt).toLocaleDateString(e?"ar-SA":"en-US")})]})]}),a.jsx("span",{className:`inline-block px-3 py-1 rounded-full text-xs font-medium bg-gray-100 text-gray-800 ${e?"font-arabic":""}`,children:l(u.status)})]}),a.jsx("div",{className:`flex items-center justify-between ${e?"flex-row-reverse":""}`,children:a.jsxs("div",{children:[a.jsx("p",{className:`text-sm text-gray-600 ${e?"font-arabic":""}`,children:e?`${u.itemCount} منتج`:`${u.itemCount} items`}),a.jsx("p",{className:`font-semibold text-gray-900 ${e?"font-arabic":""}`,children:c(u.total)})]})})]},u.id))})]})},a1=()=>{const{isRTL:e}=oe();return a.jsxs("div",{className:"text-center py-12",children:[a.jsx("h2",{className:`text-2xl font-bold text-gray-900 mb-4 ${e?"font-arabic":""}`,children:e?"المفضلة":"My Wishlist"}),a.jsx("p",{className:`text-gray-500 ${e?"font-arabic":""}`,children:e?"المفضلة فارغة حالياً":"Your wishlist is currently empty"})]})},i1=()=>{const{isRTL:e}=oe(),{user:t}=at(),[r,s]=j.useState([]),[n,i]=j.useState(!0),o=new Li;j.useEffect(()=>{t!=null&&t.id&&l()},[t==null?void 0:t.id]);const l=()=>{if(t!=null&&t.id){i(!0);try{const y=o.getCustomerAddresses(t.id);s(y)}catch(y){console.error("Error loading addresses:",y)}finally{i(!1)}}},[c,u]=j.useState(!1),[d,f]=j.useState(null),m=y=>{switch(y){case"home":return a.jsx(tv,{className:"h-5 w-5"});case"work":return a.jsx(Yy,{className:"h-5 w-5"});default:return a.jsx(wr,{className:"h-5 w-5"})}},v=y=>{switch(y){case"home":return e?"المنزل":"Home";case"work":return e?"العمل":"Work";default:return e?"أخرى":"Other"}},g=y=>{if(t!=null&&t.id&&window.confirm(e?"هل أنت متأكد من حذف هذا العنوان؟":"Are you sure you want to delete this address?"))try{o.deleteAddress(t.id,y)?(Q.success(e?"تم حذف العنوان بنجاح":"Address deleted successfully"),l()):Q.error(e?"حدث خطأ أثناء حذف العنوان":"Error deleting address")}catch(p){console.error("Error deleting address:",p),Q.error(e?"حدث خطأ أثناء حذف العنوان":"Error deleting address")}},w=y=>{if(t!=null&&t.id)try{o.setDefaultAddress(t.id,y)?(Q.success(e?"تم تعيين العنوان الافتراضي":"Default address set successfully"),l()):Q.error(e?"حدث خطأ أثناء تعيين العنوان الافتراضي":"Error setting default address")}catch(p){console.error("Error setting default address:",p),Q.error(e?"حدث خطأ أثناء تعيين العنوان الافتراضي":"Error setting default address")}};return a.jsxs("div",{className:"space-y-6",children:[a.jsxs("div",{className:`flex items-center justify-between ${e?"flex-row-reverse":""}`,children:[a.jsxs("div",{children:[a.jsx("h1",{className:`text-3xl font-bold text-gray-900 ${e?"font-arabic":""}`,children:"عناويني"}),a.jsx("p",{className:`mt-2 text-gray-600 ${e?"font-arabic":""}`,children:"إدارة عناوين الشحن والفوترة"})]}),a.jsxs("button",{onClick:()=>u(!0),className:`
            flex items-center px-4 py-2 bg-black text-white rounded-lg hover:bg-gray-800 transition-colors
            ${e?"flex-row-reverse":""}
          `,children:[a.jsx(Ur,{className:`h-5 w-5 ${e?"ml-2":"mr-2"}`}),a.jsx("span",{className:e?"font-arabic":"",children:"إضافة عنوان جديد"})]})]}),n&&a.jsx("div",{className:"flex items-center justify-center py-12",children:a.jsx("div",{className:"animate-spin rounded-full h-8 w-8 border-b-2 border-black"})}),!n&&r.length===0&&a.jsxs("div",{className:"text-center py-12",children:[a.jsx(wr,{className:"mx-auto h-12 w-12 text-gray-400 mb-4"}),a.jsx("h2",{className:`text-xl font-semibold text-gray-900 mb-2 ${e?"font-arabic":""}`,children:e?"لا توجد عناوين محفوظة":"No saved addresses"}),a.jsx("p",{className:`text-gray-600 mb-4 ${e?"font-arabic":""}`,children:e?"أضف عنوانك الأول لتسهيل عملية الشحن":"Add your first address to make shipping easier"}),a.jsx("button",{onClick:()=>u(!0),className:`bg-black text-white px-6 py-3 rounded-lg hover:bg-gray-800 transition-colors ${e?"font-arabic":""}`,children:e?"إضافة عنوان جديد":"Add New Address"})]}),!n&&r.length>0&&a.jsx("div",{className:"grid grid-cols-1 md:grid-cols-2 gap-6",children:r.map(y=>a.jsxs("div",{className:"bg-white rounded-lg shadow-sm border border-gray-200 p-6 relative",children:[y.isDefault&&a.jsx("div",{className:`absolute top-4 ${e?"left-4":"right-4"} bg-green-100 text-green-800 px-2 py-1 rounded text-xs font-medium`,children:"العنوان الافتراضي"}),a.jsxs("div",{className:`flex items-center mb-4 ${e?"flex-row-reverse":""}`,children:[a.jsx("div",{className:"h-10 w-10 bg-gray-100 rounded-lg flex items-center justify-center",children:m(y.type)}),a.jsxs("div",{className:e?"mr-3 text-right":"ml-3 text-left",children:[a.jsx("h3",{className:`font-medium text-gray-900 ${e?"font-arabic":""}`,children:v(y.type)}),a.jsxs("p",{className:`text-sm text-gray-500 ${e?"font-arabic":""}`,children:[y.firstName," ",y.lastName]})]})]}),a.jsxs("div",{className:`space-y-2 mb-4 ${e?"text-right":"text-left"}`,children:[a.jsx("p",{className:`text-sm text-gray-900 ${e?"font-arabic":""}`,children:y.street}),a.jsxs("p",{className:`text-sm text-gray-900 ${e?"font-arabic":""}`,children:[y.city,", ",y.state," ",y.postalCode]}),a.jsx("p",{className:`text-sm text-gray-900 ${e?"font-arabic":""}`,children:y.country}),a.jsx("p",{className:"text-sm text-gray-500",children:y.phone})]}),a.jsxs("div",{className:`flex items-center space-x-3 ${e?"space-x-reverse":""}`,children:[!y.isDefault&&a.jsx("button",{onClick:()=>w(y.id),className:`text-sm text-blue-600 hover:text-blue-800 ${e?"font-arabic":""}`,children:"تعيين كافتراضي"}),a.jsx("button",{onClick:()=>{f(y),u(!0)},className:"text-sm text-gray-600 hover:text-gray-800",title:"تعديل",children:a.jsx(Pi,{className:"h-4 w-4"})}),a.jsx("button",{onClick:()=>g(y.id),className:"text-sm text-red-600 hover:text-red-800",title:"حذف",children:a.jsx($n,{className:"h-4 w-4"})})]})]},y.id))}),c&&a.jsx("div",{className:"fixed inset-0 z-50 overflow-y-auto",children:a.jsxs("div",{className:"flex items-center justify-center min-h-screen px-4 pt-4 pb-20 text-center sm:block sm:p-0",children:[a.jsx("div",{className:"fixed inset-0 transition-opacity bg-gray-500 bg-opacity-75",onClick:()=>u(!1)}),a.jsxs("div",{className:"inline-block w-full max-w-md p-6 my-8 overflow-hidden text-left align-middle transition-all transform bg-white shadow-xl rounded-lg",children:[a.jsx("h3",{className:`text-lg font-medium text-gray-900 mb-4 ${e?"font-arabic text-right":"text-left"}`,children:d?"تعديل العنوان":"إضافة عنوان جديد"}),a.jsxs("div",{className:"space-y-4",children:[a.jsxs("div",{className:"grid grid-cols-2 gap-4",children:[a.jsxs("div",{children:[a.jsx("label",{className:`block text-sm font-medium text-gray-700 mb-1 ${e?"font-arabic text-right":"text-left"}`,children:"الاسم الأول"}),a.jsx("input",{type:"text",className:`w-full px-3 py-2 border border-gray-300 rounded-md focus:ring-2 focus:ring-black focus:border-transparent ${e?"text-right font-arabic":"text-left"}`,defaultValue:d==null?void 0:d.firstName})]}),a.jsxs("div",{children:[a.jsx("label",{className:`block text-sm font-medium text-gray-700 mb-1 ${e?"font-arabic text-right":"text-left"}`,children:"الاسم الأخير"}),a.jsx("input",{type:"text",className:`w-full px-3 py-2 border border-gray-300 rounded-md focus:ring-2 focus:ring-black focus:border-transparent ${e?"text-right font-arabic":"text-left"}`,defaultValue:d==null?void 0:d.lastName})]})]}),a.jsxs("div",{children:[a.jsx("label",{className:`block text-sm font-medium text-gray-700 mb-1 ${e?"font-arabic text-right":"text-left"}`,children:"نوع العنوان"}),a.jsxs("select",{className:`w-full px-3 py-2 border border-gray-300 rounded-md focus:ring-2 focus:ring-black focus:border-transparent ${e?"font-arabic":""}`,children:[a.jsx("option",{value:"home",children:"المنزل"}),a.jsx("option",{value:"work",children:"العمل"}),a.jsx("option",{value:"other",children:"أخرى"})]})]}),a.jsxs("div",{children:[a.jsx("label",{className:`block text-sm font-medium text-gray-700 mb-1 ${e?"font-arabic text-right":"text-left"}`,children:"الشارع والحي"}),a.jsx("input",{type:"text",className:`w-full px-3 py-2 border border-gray-300 rounded-md focus:ring-2 focus:ring-black focus:border-transparent ${e?"text-right font-arabic":"text-left"}`,defaultValue:d==null?void 0:d.street})]}),a.jsxs("div",{className:"grid grid-cols-2 gap-4",children:[a.jsxs("div",{children:[a.jsx("label",{className:`block text-sm font-medium text-gray-700 mb-1 ${e?"font-arabic text-right":"text-left"}`,children:"المدينة"}),a.jsx("input",{type:"text",className:`w-full px-3 py-2 border border-gray-300 rounded-md focus:ring-2 focus:ring-black focus:border-transparent ${e?"text-right font-arabic":"text-left"}`,defaultValue:d==null?void 0:d.city})]}),a.jsxs("div",{children:[a.jsx("label",{className:`block text-sm font-medium text-gray-700 mb-1 ${e?"font-arabic text-right":"text-left"}`,children:"الرمز البريدي"}),a.jsx("input",{type:"text",className:"w-full px-3 py-2 border border-gray-300 rounded-md focus:ring-2 focus:ring-black focus:border-transparent",defaultValue:d==null?void 0:d.postalCode})]})]})]}),a.jsxs("div",{className:`flex items-center space-x-3 mt-6 ${e?"space-x-reverse":""}`,children:[a.jsx("button",{onClick:()=>{u(!1),f(null),alert("تم حفظ العنوان بنجاح")},className:`flex-1 px-4 py-2 bg-black text-white rounded-md hover:bg-gray-800 transition-colors ${e?"font-arabic":""}`,children:"حفظ العنوان"}),a.jsx("button",{onClick:()=>{u(!1),f(null)},className:`flex-1 px-4 py-2 bg-gray-200 text-gray-800 rounded-md hover:bg-gray-300 transition-colors ${e?"font-arabic":""}`,children:"إلغاء"})]})]})]})})]})},o1=()=>{const{isRTL:e,language:t,changeLanguage:r}=oe(),{user:s}=at(),[n,i]=j.useState("preferences"),[o,l]=j.useState(!1),[c,u]=j.useState(!1),[d,f]=j.useState(!1),[,m]=j.useState(!0),v=new Li,g=[{id:"preferences",label:e?"التفضيلات":"Preferences",icon:bc},{id:"notifications",label:e?"الإشعارات":"Notifications",icon:gh},{id:"security",label:e?"الأمان":"Security",icon:av}],[w,y]=j.useState({preferences:{language:t,currency:"SAR",timezone:"Asia/Riyadh"},notifications:{orderUpdates:!0,promotions:!0,newsletter:!1,smsNotifications:!0,emailNotifications:!0},security:{currentPassword:"",newPassword:"",confirmPassword:""}});j.useEffect(()=>{s!=null&&s.id&&p()},[s==null?void 0:s.id]);const p=()=>{if(s!=null&&s.id){m(!0);try{const k=v.getCustomerSettings(s.id);y(E=>({...E,preferences:{...E.preferences,...k.preferences},notifications:{...E.notifications,...k.notifications}}))}catch(k){console.error("Error loading settings:",k)}finally{m(!1)}}},h=()=>{if(s!=null&&s.id)try{v.updateCustomerSettings(s.id,{preferences:w.preferences,notifications:w.notifications}),Q.success(e?"تم حفظ الإعدادات بنجاح":"Settings saved successfully")}catch(k){console.error("Error saving settings:",k),Q.error(e?"حدث خطأ أثناء حفظ الإعدادات":"Error saving settings")}},x=()=>{if(s!=null&&s.id){if(w.security.newPassword!==w.security.confirmPassword){Q.error(e?"كلمات المرور غير متطابقة":"Passwords do not match");return}if(w.security.newPassword.length<6){Q.error(e?"كلمة المرور يجب أن تكون 6 أحرف على الأقل":"Password must be at least 6 characters");return}try{Q.success(e?"تم تغيير كلمة المرور بنجاح":"Password changed successfully"),y(k=>({...k,security:{currentPassword:"",newPassword:"",confirmPassword:""}}))}catch(k){console.error("Error changing password:",k),Q.error(e?"حدث خطأ أثناء تغيير كلمة المرور":"Error changing password")}}},b=(k,E,O)=>{y($=>({...$,[k]:{...$[k],[E]:O}})),k==="preferences"&&E==="language"&&r(O)};return a.jsxs("div",{className:"space-y-6",children:[a.jsxs("div",{children:[a.jsx("h1",{className:`text-3xl font-bold text-gray-900 ${e?"font-arabic":""}`,children:"إعدادات الحساب"}),a.jsx("p",{className:`mt-2 text-gray-600 ${e?"font-arabic":""}`,children:"إدارة تفضيلاتك وإعدادات الأمان"})]}),a.jsxs("div",{className:"bg-white rounded-lg shadow-sm border border-gray-200",children:[a.jsx("div",{className:"border-b border-gray-200",children:a.jsx("nav",{className:`flex space-x-8 ${e?"space-x-reverse":""} px-6`,children:g.map(k=>{const E=k.icon;return a.jsxs("button",{onClick:()=>i(k.id),className:`
                    flex items-center py-4 px-1 border-b-2 font-medium text-sm transition-colors
                    ${n===k.id?"border-black text-black":"border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300"}
                    ${e?"flex-row-reverse":""}
                  `,children:[a.jsx(E,{className:`h-5 w-5 ${e?"ml-2":"mr-2"}`}),a.jsx("span",{className:e?"font-arabic":"",children:k.label})]},k.id)})})}),a.jsxs("div",{className:"p-6",children:[n==="preferences"&&a.jsxs("div",{className:"space-y-6",children:[a.jsx("h3",{className:`text-lg font-semibold text-gray-900 ${e?"font-arabic":""}`,children:"التفضيلات العامة"}),a.jsxs("div",{className:"grid grid-cols-1 md:grid-cols-2 gap-6",children:[a.jsxs("div",{children:[a.jsx("label",{className:`block text-sm font-medium text-gray-700 mb-2 ${e?"font-arabic":""}`,children:"اللغة"}),a.jsxs("select",{value:w.preferences.language,onChange:k=>b("preferences","language",k.target.value),className:`w-full px-4 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-black focus:border-transparent ${e?"font-arabic":""}`,children:[a.jsx("option",{value:"ar",children:"العربية"}),a.jsx("option",{value:"en",children:"English"})]})]}),a.jsxs("div",{children:[a.jsx("label",{className:`block text-sm font-medium text-gray-700 mb-2 ${e?"font-arabic":""}`,children:"العملة"}),a.jsxs("select",{value:w.preferences.currency,onChange:k=>b("preferences","currency",k.target.value),className:`w-full px-4 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-black focus:border-transparent ${e?"font-arabic":""}`,children:[a.jsx("option",{value:"SAR",children:"ريال سعودي (SAR)"}),a.jsx("option",{value:"USD",children:"دولار أمريكي (USD)"}),a.jsx("option",{value:"EUR",children:"يورو (EUR)"})]})]}),a.jsxs("div",{children:[a.jsx("label",{className:`block text-sm font-medium text-gray-700 mb-2 ${e?"font-arabic":""}`,children:"المنطقة الزمنية"}),a.jsxs("select",{value:w.preferences.timezone,onChange:k=>b("preferences","timezone",k.target.value),className:`w-full px-4 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-black focus:border-transparent ${e?"font-arabic":""}`,children:[a.jsx("option",{value:"Asia/Riyadh",children:"الرياض (GMT+3)"}),a.jsx("option",{value:"Asia/Dubai",children:"دبي (GMT+4)"}),a.jsx("option",{value:"Europe/London",children:"لندن (GMT+0)"})]})]})]})]}),n==="notifications"&&a.jsxs("div",{className:"space-y-6",children:[a.jsx("h3",{className:`text-lg font-semibold text-gray-900 ${e?"font-arabic":""}`,children:"إعدادات الإشعارات"}),a.jsxs("div",{className:"space-y-4",children:[a.jsxs("div",{className:`flex items-center justify-between ${e?"flex-row-reverse":""}`,children:[a.jsxs("div",{children:[a.jsx("h4",{className:`font-medium text-gray-900 ${e?"font-arabic":""}`,children:"تحديثات الطلبات"}),a.jsx("p",{className:`text-sm text-gray-500 ${e?"font-arabic":""}`,children:"تلقي إشعارات حول حالة طلباتك"})]}),a.jsxs("label",{className:"relative inline-flex items-center cursor-pointer",children:[a.jsx("input",{type:"checkbox",checked:w.notifications.orderUpdates,onChange:k=>b("notifications","orderUpdates",k.target.checked),className:"sr-only peer"}),a.jsx("div",{className:"w-11 h-6 bg-gray-200 peer-focus:outline-none peer-focus:ring-4 peer-focus:ring-black/20 rounded-full peer peer-checked:after:translate-x-full peer-checked:after:border-white after:content-[''] after:absolute after:top-[2px] after:left-[2px] after:bg-white after:border-gray-300 after:border after:rounded-full after:h-5 after:w-5 after:transition-all peer-checked:bg-black"})]})]}),a.jsxs("div",{className:`flex items-center justify-between ${e?"flex-row-reverse":""}`,children:[a.jsxs("div",{children:[a.jsx("h4",{className:`font-medium text-gray-900 ${e?"font-arabic":""}`,children:"العروض والخصومات"}),a.jsx("p",{className:`text-sm text-gray-500 ${e?"font-arabic":""}`,children:"تلقي إشعارات حول العروض الخاصة"})]}),a.jsxs("label",{className:"relative inline-flex items-center cursor-pointer",children:[a.jsx("input",{type:"checkbox",checked:w.notifications.promotions,onChange:k=>b("notifications","promotions",k.target.checked),className:"sr-only peer"}),a.jsx("div",{className:"w-11 h-6 bg-gray-200 peer-focus:outline-none peer-focus:ring-4 peer-focus:ring-black/20 rounded-full peer peer-checked:after:translate-x-full peer-checked:after:border-white after:content-[''] after:absolute after:top-[2px] after:left-[2px] after:bg-white after:border-gray-300 after:border after:rounded-full after:h-5 after:w-5 after:transition-all peer-checked:bg-black"})]})]}),a.jsxs("div",{className:`flex items-center justify-between ${e?"flex-row-reverse":""}`,children:[a.jsxs("div",{children:[a.jsx("h4",{className:`font-medium text-gray-900 ${e?"font-arabic":""}`,children:"الإشعارات عبر الرسائل النصية"}),a.jsx("p",{className:`text-sm text-gray-500 ${e?"font-arabic":""}`,children:"تلقي رسائل نصية للتحديثات المهمة"})]}),a.jsxs("label",{className:"relative inline-flex items-center cursor-pointer",children:[a.jsx("input",{type:"checkbox",checked:w.notifications.smsNotifications,onChange:k=>b("notifications","smsNotifications",k.target.checked),className:"sr-only peer"}),a.jsx("div",{className:"w-11 h-6 bg-gray-200 peer-focus:outline-none peer-focus:ring-4 peer-focus:ring-black/20 rounded-full peer peer-checked:after:translate-x-full peer-checked:after:border-white after:content-[''] after:absolute after:top-[2px] after:left-[2px] after:bg-white after:border-gray-300 after:border after:rounded-full after:h-5 after:w-5 after:transition-all peer-checked:bg-black"})]})]})]})]}),n==="security"&&a.jsxs("div",{className:"space-y-6",children:[a.jsx("h3",{className:`text-lg font-semibold text-gray-900 ${e?"font-arabic":""}`,children:"تغيير كلمة المرور"}),a.jsxs("div",{className:"max-w-md space-y-4",children:[a.jsxs("div",{children:[a.jsx("label",{className:`block text-sm font-medium text-gray-700 mb-2 ${e?"font-arabic":""}`,children:"كلمة المرور الحالية"}),a.jsxs("div",{className:"relative",children:[a.jsx("input",{type:o?"text":"password",value:w.security.currentPassword,onChange:k=>b("security","currentPassword",k.target.value),className:"w-full px-4 py-2 pr-10 border border-gray-300 rounded-lg focus:ring-2 focus:ring-black focus:border-transparent"}),a.jsx("button",{type:"button",onClick:()=>l(!o),className:"absolute inset-y-0 right-0 pr-3 flex items-center",children:o?a.jsx(po,{className:"h-5 w-5 text-gray-400"}):a.jsx(ms,{className:"h-5 w-5 text-gray-400"})})]})]}),a.jsxs("div",{children:[a.jsx("label",{className:`block text-sm font-medium text-gray-700 mb-2 ${e?"font-arabic":""}`,children:"كلمة المرور الجديدة"}),a.jsxs("div",{className:"relative",children:[a.jsx("input",{type:c?"text":"password",value:w.security.newPassword,onChange:k=>b("security","newPassword",k.target.value),className:"w-full px-4 py-2 pr-10 border border-gray-300 rounded-lg focus:ring-2 focus:ring-black focus:border-transparent"}),a.jsx("button",{type:"button",onClick:()=>u(!c),className:"absolute inset-y-0 right-0 pr-3 flex items-center",children:c?a.jsx(po,{className:"h-5 w-5 text-gray-400"}):a.jsx(ms,{className:"h-5 w-5 text-gray-400"})})]})]}),a.jsxs("div",{children:[a.jsx("label",{className:`block text-sm font-medium text-gray-700 mb-2 ${e?"font-arabic":""}`,children:"تأكيد كلمة المرور الجديدة"}),a.jsxs("div",{className:"relative",children:[a.jsx("input",{type:d?"text":"password",value:w.security.confirmPassword,onChange:k=>b("security","confirmPassword",k.target.value),className:"w-full px-4 py-2 pr-10 border border-gray-300 rounded-lg focus:ring-2 focus:ring-black focus:border-transparent"}),a.jsx("button",{type:"button",onClick:()=>f(!d),className:"absolute inset-y-0 right-0 pr-3 flex items-center",children:d?a.jsx(po,{className:"h-5 w-5 text-gray-400"}):a.jsx(ms,{className:"h-5 w-5 text-gray-400"})})]})]}),a.jsx("button",{onClick:x,className:`w-full px-4 py-2 bg-black text-white rounded-lg hover:bg-gray-800 transition-colors ${e?"font-arabic":""}`,children:"تغيير كلمة المرور"})]})]})]}),a.jsx("div",{className:"px-6 py-4 bg-gray-50 border-t border-gray-200 rounded-b-lg",children:a.jsx("button",{onClick:h,className:`px-6 py-2 bg-black text-white rounded-lg hover:bg-gray-800 transition-colors ${e?"font-arabic":""}`,children:"حفظ الإعدادات"})})]})]})},l1=()=>{const{user:e}=at(),{isRTL:t}=oe(),[r,s]=j.useState([]),[n,i]=j.useState(!0),[o,l]=j.useState(!1),[c,u]=j.useState(null),[d,f]=j.useState({cardNumber:"",cardholderName:"",expiryDate:"",cvv:"",isDefault:!1}),m=new Li;j.useEffect(()=>{v()},[e==null?void 0:e.id]);const v=()=>{if(e!=null&&e.id){i(!0);try{const $=m.getCustomerPaymentCards(e.id);s($)}catch($){console.error("Error loading payment cards:",$),Q.error(t?"خطأ في تحميل بطاقات الدفع":"Error loading payment cards")}finally{i(!1)}}},g=$=>{const C=$.replace(/\s/g,"");return C.startsWith("4")?"visa":C.startsWith("5")||C.startsWith("2")?"mastercard":C.startsWith("3")?"amex":"visa"},w=$=>{const C=$.replace(/\s+/g,"").replace(/[^0-9]/gi,""),D=C.match(/\d{4,16}/g),U=D&&D[0]||"",W=[];for(let I=0,Y=U.length;I<Y;I+=4)W.push(U.substring(I,I+4));return W.length?W.join(" "):C},y=$=>{const{name:C,value:D,type:U,checked:W}=$.target;if(C==="cardNumber"){const I=w(D);f(Y=>({...Y,[C]:I}))}else if(C==="expiryDate"){let I=D.replace(/\D/g,"");I.length>=2&&(I=I.substring(0,2)+"/"+I.substring(2,4)),f(Y=>({...Y,[C]:I}))}else f(I=>({...I,[C]:U==="checkbox"?W:D}))},p=$=>{if($.preventDefault(),!!(e!=null&&e.id))try{const C={cardNumber:d.cardNumber,cardholderName:d.cardholderName,expiryDate:d.expiryDate,cardType:g(d.cardNumber),isDefault:d.isDefault};c?(m.updatePaymentCard(e.id,c.id,C),Q.success(t?"تم تحديث البطاقة بنجاح":"Card updated successfully")):(m.addPaymentCard(e.id,C),Q.success(t?"تم إضافة البطاقة بنجاح":"Card added successfully")),v(),k()}catch(C){console.error("Error saving payment card:",C),Q.error(t?"خطأ في حفظ البطاقة":"Error saving card")}},h=$=>{u($),f({cardNumber:$.cardNumber,cardholderName:$.cardholderName,expiryDate:$.expiryDate,cvv:"",isDefault:$.isDefault}),l(!0)},x=$=>{if(e!=null&&e.id&&window.confirm(t?"هل أنت متأكد من حذف هذه البطاقة؟":"Are you sure you want to delete this card?"))try{m.deletePaymentCard(e.id,$),Q.success(t?"تم حذف البطاقة بنجاح":"Card deleted successfully"),v()}catch(C){console.error("Error deleting payment card:",C),Q.error(t?"خطأ في حذف البطاقة":"Error deleting card")}},b=$=>{if(e!=null&&e.id)try{m.setDefaultPaymentCard(e.id,$),Q.success(t?"تم تعيين البطاقة الافتراضية":"Default card set successfully"),v()}catch(C){console.error("Error setting default card:",C),Q.error(t?"خطأ في تعيين البطاقة الافتراضية":"Error setting default card")}},k=()=>{f({cardNumber:"",cardholderName:"",expiryDate:"",cvv:"",isDefault:!1}),u(null),l(!1)},E=$=>"**** **** **** "+$.replace(/\s/g,"").slice(-4),O=$=>{switch($){case"visa":return"💳";case"mastercard":return"💳";case"amex":return"💳";default:return"💳"}};return n?a.jsxs("div",{className:"space-y-6",children:[a.jsx("div",{className:"flex items-center justify-between",children:a.jsx("h1",{className:`text-2xl font-bold text-gray-900 ${t?"font-arabic":""}`,children:t?"بطاقات الدفع":"Payment Cards"})}),a.jsx("div",{className:"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6",children:[...Array(3)].map(($,C)=>a.jsxs("div",{className:"bg-white rounded-lg shadow-sm border border-gray-200 p-6 animate-pulse",children:[a.jsx("div",{className:"h-4 bg-gray-200 rounded mb-2"}),a.jsx("div",{className:"h-8 bg-gray-200 rounded"})]},C))})]}):a.jsxs("div",{className:"space-y-6",children:[a.jsxs("div",{className:`flex items-center justify-between ${t?"flex-row-reverse":""}`,children:[a.jsx("h1",{className:`text-2xl font-bold text-gray-900 ${t?"font-arabic":""}`,children:t?"بطاقات الدفع":"Payment Cards"}),a.jsxs("button",{onClick:()=>l(!0),className:`flex items-center px-4 py-2 bg-black text-white rounded-lg hover:bg-gray-800 transition-colors ${t?"flex-row-reverse":""}`,children:[a.jsx(Ur,{className:`h-5 w-5 ${t?"ml-2":"mr-2"}`}),a.jsx("span",{className:t?"font-arabic":"",children:t?"إضافة بطاقة":"Add Card"})]})]}),a.jsx("div",{className:`bg-blue-50 border border-blue-200 rounded-lg p-4 ${t?"text-right":"text-left"}`,children:a.jsxs("div",{className:`flex items-start ${t?"flex-row-reverse":""}`,children:[a.jsx(Nh,{className:`h-5 w-5 text-blue-600 ${t?"ml-3":"mr-3"} mt-0.5`}),a.jsxs("div",{children:[a.jsx("h3",{className:`text-sm font-medium text-blue-900 ${t?"font-arabic":""}`,children:t?"أمان البيانات":"Data Security"}),a.jsx("p",{className:`text-sm text-blue-700 mt-1 ${t?"font-arabic":""}`,children:t?"جميع بيانات البطاقات محمية ومشفرة بأعلى معايير الأمان":"All card data is protected and encrypted with the highest security standards"})]})]})}),r.length===0?a.jsxs("div",{className:`text-center py-12 ${t?"text-right":"text-left"}`,children:[a.jsx(_n,{className:"h-12 w-12 text-gray-400 mx-auto mb-4"}),a.jsx("h3",{className:`text-lg font-medium text-gray-900 mb-2 ${t?"font-arabic":""}`,children:t?"لا توجد بطاقات دفع":"No payment cards"}),a.jsx("p",{className:`text-gray-600 mb-6 ${t?"font-arabic":""}`,children:t?"أضف بطاقة دفع لتسهيل عملية الشراء":"Add a payment card to make checkout easier"}),a.jsxs("button",{onClick:()=>l(!0),className:`inline-flex items-center px-4 py-2 bg-black text-white rounded-lg hover:bg-gray-800 transition-colors ${t?"flex-row-reverse":""}`,children:[a.jsx(Ur,{className:`h-5 w-5 ${t?"ml-2":"mr-2"}`}),a.jsx("span",{className:t?"font-arabic":"",children:t?"إضافة بطاقة":"Add Card"})]})]}):a.jsx("div",{className:"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6",children:r.map($=>a.jsxs("div",{className:"bg-white rounded-lg shadow-sm border border-gray-200 overflow-hidden",children:[a.jsxs("div",{className:"bg-gradient-to-r from-gray-800 to-black text-white p-6",children:[a.jsxs("div",{className:`flex items-center justify-between mb-4 ${t?"flex-row-reverse":""}`,children:[a.jsx("span",{className:"text-2xl",children:O($.cardType)}),$.isDefault&&a.jsx("span",{className:`text-xs bg-green-500 text-white px-2 py-1 rounded ${t?"font-arabic":""}`,children:t?"افتراضية":"Default"})]}),a.jsxs("div",{className:`space-y-2 ${t?"text-right":"text-left"}`,children:[a.jsx("p",{className:"text-lg font-mono tracking-wider",children:E($.cardNumber)}),a.jsxs("div",{className:`flex justify-between items-center ${t?"flex-row-reverse":""}`,children:[a.jsx("p",{className:`text-sm ${t?"font-arabic":""}`,children:$.cardholderName}),a.jsx("p",{className:"text-sm font-mono",children:$.expiryDate})]})]})]}),a.jsx("div",{className:"p-4",children:a.jsxs("div",{className:`flex items-center space-x-2 ${t?"flex-row-reverse space-x-reverse":""}`,children:[!$.isDefault&&a.jsx("button",{onClick:()=>b($.id),className:`text-sm text-blue-600 hover:text-blue-800 ${t?"font-arabic":""}`,children:t?"تعيين كافتراضية":"Set as Default"}),a.jsx("button",{onClick:()=>h($),className:"p-2 text-gray-600 hover:text-gray-800",children:a.jsx(Pi,{className:"h-4 w-4"})}),a.jsx("button",{onClick:()=>x($.id),className:"p-2 text-red-600 hover:text-red-800",children:a.jsx($n,{className:"h-4 w-4"})})]})})]},$.id))}),o&&a.jsx("div",{className:"fixed inset-0 z-50 overflow-y-auto",children:a.jsxs("div",{className:"flex items-center justify-center min-h-screen pt-4 px-4 pb-20 text-center sm:block sm:p-0",children:[a.jsx("div",{className:"fixed inset-0 transition-opacity","aria-hidden":"true",children:a.jsx("div",{className:"absolute inset-0 bg-gray-500 opacity-75",onClick:k})}),a.jsx("div",{className:"inline-block align-bottom bg-white rounded-lg text-left overflow-hidden shadow-xl transform transition-all sm:my-8 sm:align-middle sm:max-w-lg sm:w-full",children:a.jsxs("form",{onSubmit:p,children:[a.jsxs("div",{className:"bg-white px-4 pt-5 pb-4 sm:p-6 sm:pb-4",children:[a.jsx("h3",{className:`text-lg font-medium text-gray-900 mb-4 ${t?"font-arabic text-right":"text-left"}`,children:c?t?"تعديل البطاقة":"Edit Card":t?"إضافة بطاقة جديدة":"Add New Card"}),a.jsxs("div",{className:"space-y-4",children:[a.jsxs("div",{children:[a.jsx("label",{className:`block text-sm font-medium text-gray-700 mb-1 ${t?"font-arabic text-right":"text-left"}`,children:t?"رقم البطاقة":"Card Number"}),a.jsx("input",{type:"text",name:"cardNumber",value:d.cardNumber,onChange:y,placeholder:"1234 5678 9012 3456",maxLength:19,className:`w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-black focus:border-transparent ${t?"text-right font-arabic":"text-left"}`,required:!0})]}),a.jsxs("div",{children:[a.jsx("label",{className:`block text-sm font-medium text-gray-700 mb-1 ${t?"font-arabic text-right":"text-left"}`,children:t?"اسم حامل البطاقة":"Cardholder Name"}),a.jsx("input",{type:"text",name:"cardholderName",value:d.cardholderName,onChange:y,placeholder:t?"أحمد محمد":"John Doe",className:`w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-black focus:border-transparent ${t?"text-right font-arabic":"text-left"}`,required:!0})]}),a.jsxs("div",{className:"grid grid-cols-2 gap-4",children:[a.jsxs("div",{children:[a.jsx("label",{className:`block text-sm font-medium text-gray-700 mb-1 ${t?"font-arabic text-right":"text-left"}`,children:t?"تاريخ الانتهاء":"Expiry Date"}),a.jsx("input",{type:"text",name:"expiryDate",value:d.expiryDate,onChange:y,placeholder:"MM/YY",maxLength:5,className:`w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-black focus:border-transparent ${t?"text-right":"text-left"}`,required:!0})]}),a.jsxs("div",{children:[a.jsx("label",{className:`block text-sm font-medium text-gray-700 mb-1 ${t?"font-arabic text-right":"text-left"}`,children:"CVV"}),a.jsx("input",{type:"text",name:"cvv",value:d.cvv,onChange:y,placeholder:"123",maxLength:4,className:`w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-black focus:border-transparent ${t?"text-right":"text-left"}`,required:!0})]})]}),a.jsxs("div",{className:`flex items-center ${t?"flex-row-reverse":""}`,children:[a.jsx("input",{type:"checkbox",name:"isDefault",checked:d.isDefault,onChange:y,className:`h-4 w-4 text-black focus:ring-black border-gray-300 rounded ${t?"ml-2":"mr-2"}`}),a.jsx("label",{className:`text-sm text-gray-700 ${t?"font-arabic":""}`,children:t?"تعيين كبطاقة افتراضية":"Set as default card"})]})]})]}),a.jsxs("div",{className:`bg-gray-50 px-4 py-3 sm:px-6 sm:flex sm:flex-row-reverse ${t?"sm:flex-row":""}`,children:[a.jsx("button",{type:"submit",className:`w-full inline-flex justify-center rounded-md border border-transparent shadow-sm px-4 py-2 bg-black text-base font-medium text-white hover:bg-gray-800 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-black sm:ml-3 sm:w-auto sm:text-sm ${t?"font-arabic sm:mr-3 sm:ml-0":""}`,children:c?t?"تحديث":"Update":t?"إضافة":"Add"}),a.jsx("button",{type:"button",onClick:k,className:`mt-3 w-full inline-flex justify-center rounded-md border border-gray-300 shadow-sm px-4 py-2 bg-white text-base font-medium text-gray-700 hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-black sm:mt-0 sm:ml-3 sm:w-auto sm:text-sm ${t?"font-arabic sm:mr-3 sm:ml-0":""}`,children:t?"إلغاء":"Cancel"})]})]})})]})})]})},c1=({stats:e,loading:t,isRTL:r,user:s,navigate:n})=>a.jsxs("div",{className:"space-y-6",children:[a.jsxs("div",{className:`bg-gradient-to-r from-black to-gray-800 text-white rounded-lg p-6 ${r?"text-right":"text-left"}`,children:[a.jsx("h1",{className:`text-2xl font-bold mb-2 ${r?"font-arabic":""}`,children:r?`مرحباً، ${(s==null?void 0:s.firstName)||"عميل"}`:`Welcome, ${(s==null?void 0:s.firstName)||"Customer"}`}),a.jsx("p",{className:`text-gray-300 ${r?"font-arabic":""}`,children:r?"إدارة حسابك وطلباتك من هنا":"Manage your account and orders from here"})]}),t?a.jsx("div",{className:"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6",children:[...Array(4)].map((i,o)=>a.jsxs("div",{className:"bg-white rounded-lg shadow-sm border border-gray-200 p-6 animate-pulse",children:[a.jsx("div",{className:"h-4 bg-gray-200 rounded mb-2"}),a.jsx("div",{className:"h-8 bg-gray-200 rounded"})]},o))}):a.jsxs("div",{className:"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6",children:[a.jsx("div",{className:"bg-white rounded-lg shadow-sm border border-gray-200 p-6",children:a.jsxs("div",{className:`flex items-center justify-between ${r?"flex-row-reverse":""}`,children:[a.jsxs("div",{className:r?"text-right":"text-left",children:[a.jsx("p",{className:`text-sm text-gray-600 ${r?"font-arabic":""}`,children:r?"إجمالي الطلبات":"Total Orders"}),a.jsx("p",{className:"text-2xl font-bold text-gray-900",children:e.totalOrders})]}),a.jsx(Je,{className:"h-8 w-8 text-blue-600"})]})}),a.jsx("div",{className:"bg-white rounded-lg shadow-sm border border-gray-200 p-6",children:a.jsxs("div",{className:`flex items-center justify-between ${r?"flex-row-reverse":""}`,children:[a.jsxs("div",{className:r?"text-right":"text-left",children:[a.jsx("p",{className:`text-sm text-gray-600 ${r?"font-arabic":""}`,children:r?"إجمالي المبلغ المنفق":"Total Spent"}),a.jsxs("p",{className:"text-2xl font-bold text-gray-900",children:[e.totalSpent.toLocaleString()," ",r?"ر.س":"SAR"]})]}),a.jsx($i,{className:"h-8 w-8 text-green-600"})]})}),a.jsx("div",{className:"bg-white rounded-lg shadow-sm border border-gray-200 p-6",children:a.jsxs("div",{className:`flex items-center justify-between ${r?"flex-row-reverse":""}`,children:[a.jsxs("div",{className:r?"text-right":"text-left",children:[a.jsx("p",{className:`text-sm text-gray-600 ${r?"font-arabic":""}`,children:r?"المفضلة":"Wishlist Items"}),a.jsx("p",{className:"text-2xl font-bold text-gray-900",children:e.wishlistCount})]}),a.jsx(Fn,{className:"h-8 w-8 text-red-600"})]})}),a.jsx("div",{className:"bg-white rounded-lg shadow-sm border border-gray-200 p-6",children:a.jsxs("div",{className:`flex items-center justify-between ${r?"flex-row-reverse":""}`,children:[a.jsxs("div",{className:r?"text-right":"text-left",children:[a.jsx("p",{className:`text-sm text-gray-600 ${r?"font-arabic":""}`,children:r?"العناوين المحفوظة":"Saved Addresses"}),a.jsx("p",{className:"text-2xl font-bold text-gray-900",children:e.addressCount})]}),a.jsx(wr,{className:"h-8 w-8 text-purple-600"})]})})]}),a.jsxs("div",{className:"bg-white rounded-lg shadow-sm border border-gray-200 p-6",children:[a.jsx("h2",{className:`text-lg font-semibold text-gray-900 mb-4 ${r?"font-arabic text-right":"text-left"}`,children:r?"إجراءات سريعة":"Quick Actions"}),a.jsxs("div",{className:"grid grid-cols-1 md:grid-cols-3 gap-4",children:[a.jsxs("button",{onClick:()=>n("/products"),className:`flex items-center p-4 border border-gray-200 rounded-lg hover:bg-gray-50 transition-colors ${r?"flex-row-reverse text-right":"text-left"}`,children:[a.jsx(kc,{className:`h-6 w-6 text-gray-600 ${r?"ml-3":"mr-3"}`}),a.jsxs("div",{children:[a.jsx("p",{className:`font-medium text-gray-900 ${r?"font-arabic":""}`,children:r?"تسوق الآن":"Shop Now"}),a.jsx("p",{className:`text-sm text-gray-600 ${r?"font-arabic":""}`,children:r?"اكتشف منتجاتنا الجديدة":"Discover our new products"})]})]}),a.jsxs("button",{onClick:()=>n("/customer/dashboard/orders"),className:`flex items-center p-4 border border-gray-200 rounded-lg hover:bg-gray-50 transition-colors ${r?"flex-row-reverse text-right":"text-left"}`,children:[a.jsx(Je,{className:`h-6 w-6 text-gray-600 ${r?"ml-3":"mr-3"}`}),a.jsxs("div",{children:[a.jsx("p",{className:`font-medium text-gray-900 ${r?"font-arabic":""}`,children:r?"تتبع الطلبات":"Track Orders"}),a.jsx("p",{className:`text-sm text-gray-600 ${r?"font-arabic":""}`,children:r?"تابع حالة طلباتك":"Follow your order status"})]})]}),a.jsxs("button",{onClick:()=>n("/customer/dashboard/addresses"),className:`flex items-center p-4 border border-gray-200 rounded-lg hover:bg-gray-50 transition-colors ${r?"flex-row-reverse text-right":"text-left"}`,children:[a.jsx(wr,{className:`h-6 w-6 text-gray-600 ${r?"ml-3":"mr-3"}`}),a.jsxs("div",{children:[a.jsx("p",{className:`font-medium text-gray-900 ${r?"font-arabic":""}`,children:r?"إدارة العناوين":"Manage Addresses"}),a.jsx("p",{className:`text-sm text-gray-600 ${r?"font-arabic":""}`,children:r?"أضف أو عدل عناوينك":"Add or edit your addresses"})]})]})]})]})]}),u1=()=>{const{user:e,logout:t}=at(),{isRTL:r}=oe(),s=Dt(),n=bt(),[i,o]=j.useState(!1),[l,c]=j.useState({totalOrders:0,totalSpent:0,avgOrderValue:0,wishlistCount:0,addressCount:0}),[u,d]=j.useState(!0);j.useEffect(()=>{e!=null&&e.id&&f()},[e==null?void 0:e.id]);const f=async()=>{if(e!=null&&e.id){d(!0);try{await new Promise(p=>setTimeout(p,1e3)),c({totalOrders:12,totalSpent:2450.75,avgOrderValue:204.23,wishlistCount:8,addressCount:3})}catch(y){console.error("Error loading customer stats:",y),Q.error(r?"خطأ في تحميل البيانات":"Error loading data")}finally{d(!1)}}},m=()=>{t(),s("/"),Q.success(r?"تم تسجيل الخروج بنجاح":"Logged out successfully")},v=[{id:"overview",label:r?"نظرة عامة":"Overview",icon:In,path:"/customer/dashboard"},{id:"orders",label:r?"طلباتي":"My Orders",icon:Je,path:"/customer/dashboard/orders"},{id:"wishlist",label:r?"المفضلة":"Wishlist",icon:Fn,path:"/customer/dashboard/wishlist"},{id:"addresses",label:r?"عناويني":"My Addresses",icon:wr,path:"/customer/dashboard/addresses"},{id:"payment-cards",label:r?"بطاقات الدفع":"Payment Cards",icon:_n,path:"/customer/dashboard/payment-cards"},{id:"settings",label:r?"الإعدادات":"Settings",icon:bc,path:"/customer/dashboard/settings"}],g=y=>y==="/customer/dashboard"?n.pathname==="/customer/dashboard":n.pathname.startsWith(y),w=y=>{s(y),o(!1)};return e?a.jsxs("div",{className:"min-h-screen bg-gray-50",children:[i&&a.jsx("div",{className:"fixed inset-0 z-40 bg-black bg-opacity-50 lg:hidden",onClick:()=>o(!1)}),a.jsxs("div",{className:`
        fixed inset-y-0 z-50 w-64 bg-white shadow-lg transform transition-transform duration-300 ease-in-out lg:translate-x-0 lg:static lg:inset-0
        ${i?"translate-x-0":r?"translate-x-full":"-translate-x-full"}
        ${r?"right-0":"left-0"}
      `,children:[a.jsxs("div",{className:"flex items-center justify-between h-16 px-6 border-b border-gray-200",children:[a.jsx("h2",{className:`text-lg font-semibold text-gray-900 ${r?"font-arabic":""}`,children:r?"لوحة العميل":"Customer Dashboard"}),a.jsx("button",{onClick:()=>o(!1),className:"lg:hidden p-2 rounded-md text-gray-400 hover:text-gray-600",children:a.jsx(Ai,{className:"h-5 w-5"})})]}),a.jsxs("nav",{className:"mt-6 px-3",children:[a.jsx("div",{className:"space-y-1",children:v.map(y=>{const p=y.icon,h=g(y.path);return a.jsxs("button",{onClick:()=>w(y.path),className:`
                    w-full flex items-center px-3 py-2 text-sm font-medium rounded-md transition-colors
                    ${r?"flex-row-reverse text-right":"text-left"}
                    ${h?"bg-black text-white":"text-gray-700 hover:bg-gray-100"}
                  `,children:[a.jsx(p,{className:`h-5 w-5 ${r?"ml-3":"mr-3"}`}),a.jsx("span",{className:r?"font-arabic":"",children:y.label})]},y.id)})}),a.jsx("div",{className:"mt-8 pt-6 border-t border-gray-200",children:a.jsxs("button",{onClick:m,className:`
                w-full flex items-center px-3 py-2 text-sm font-medium text-red-600 rounded-md hover:bg-red-50 transition-colors
                ${r?"flex-row-reverse text-right":"text-left"}
              `,children:[a.jsx(yh,{className:`h-5 w-5 ${r?"ml-3":"mr-3"}`}),a.jsx("span",{className:r?"font-arabic":"",children:r?"تسجيل الخروج":"Logout"})]})})]})]}),a.jsxs("div",{className:`lg:${r?"pr":"pl"}-64`,children:[a.jsx("div",{className:"bg-white shadow-sm border-b border-gray-200",children:a.jsx("div",{className:"px-4 sm:px-6 lg:px-8",children:a.jsxs("div",{className:`flex items-center justify-between h-16 ${r?"flex-row-reverse":""}`,children:[a.jsx("button",{onClick:()=>o(!0),className:"lg:hidden p-2 rounded-md text-gray-400 hover:text-gray-600",children:a.jsx(jc,{className:"h-6 w-6"})}),a.jsx("div",{className:`flex items-center ${r?"flex-row-reverse":""}`,children:a.jsxs("div",{className:`flex items-center ${r?"flex-row-reverse":""}`,children:[a.jsx("div",{className:"h-8 w-8 bg-black rounded-full flex items-center justify-center",children:a.jsx(Sc,{className:"h-5 w-5 text-white"})}),a.jsxs("div",{className:`${r?"mr-3 text-right":"ml-3 text-left"}`,children:[a.jsxs("p",{className:`text-sm font-medium text-gray-900 ${r?"font-arabic":""}`,children:[e.firstName," ",e.lastName]}),a.jsx("p",{className:"text-xs text-gray-500",children:e.email})]})]})})]})})}),a.jsx("main",{className:"p-4 sm:p-6 lg:p-8",children:a.jsxs(Ps,{children:[a.jsx(ee,{path:"/",element:a.jsx(c1,{stats:l,loading:u,isRTL:r,user:e,navigate:s})}),a.jsx(ee,{path:"/orders",element:a.jsx(n1,{})}),a.jsx(ee,{path:"/wishlist",element:a.jsx(a1,{})}),a.jsx(ee,{path:"/addresses",element:a.jsx(i1,{})}),a.jsx(ee,{path:"/payment-cards",element:a.jsx(l1,{})}),a.jsx(ee,{path:"/settings",element:a.jsx(o1,{})})]})})]})]}):(s("/login"),null)};function d1(){return a.jsx(Wy,{children:a.jsx(wy,{children:a.jsx(jy,{children:a.jsx($y,{children:a.jsxs(v0,{future:{v7_startTransition:!0,v7_relativeSplatPath:!0},children:[a.jsxs("div",{className:"min-h-screen bg-white flex flex-col",children:[a.jsx(hv,{}),a.jsx("main",{className:"flex-1 font-arabic",children:a.jsxs(Ps,{children:[a.jsx(ee,{path:"/",element:a.jsx(gv,{})}),a.jsx(ee,{path:"/products",element:a.jsx(xv,{})}),a.jsx(ee,{path:"/products/:id",element:a.jsx(vv,{})}),a.jsx(ee,{path:"/cart",element:a.jsx(wv,{})}),a.jsx(ee,{path:"/checkout",element:a.jsx(Nv,{})}),a.jsx(ee,{path:"/login",element:a.jsx(Hv,{})}),a.jsx(ee,{path:"/register",element:a.jsx(Wv,{})}),a.jsx(ee,{path:"/profile",element:a.jsx(mo,{children:a.jsx(qv,{})})}),a.jsx(ee,{path:"/customer/*",element:a.jsx(mo,{requiredRole:"customer",children:a.jsx(u1,{})})}),a.jsx(ee,{path:"/admin/*",element:a.jsx(mo,{requiredRole:"admin",children:a.jsx(s1,{})})})]})}),a.jsx(pv,{})]}),a.jsx(gy,{position:"top-right"})]})})})})})}const K=e=>typeof e=="string",zs=()=>{let e,t;const r=new Promise((s,n)=>{e=s,t=n});return r.resolve=e,r.reject=t,r},xd=e=>e==null?"":""+e,f1=(e,t,r)=>{e.forEach(s=>{t[s]&&(r[s]=t[s])})},m1=/###/g,yd=e=>e&&e.indexOf("###")>-1?e.replace(m1,"."):e,vd=e=>!e||K(e),an=(e,t,r)=>{const s=K(t)?t.split("."):t;let n=0;for(;n<s.length-1;){if(vd(e))return{};const i=yd(s[n]);!e[i]&&r&&(e[i]=new r),Object.prototype.hasOwnProperty.call(e,i)?e=e[i]:e={},++n}return vd(e)?{}:{obj:e,k:yd(s[n])}},wd=(e,t,r)=>{const{obj:s,k:n}=an(e,t,Object);if(s!==void 0||t.length===1){s[n]=r;return}let i=t[t.length-1],o=t.slice(0,t.length-1),l=an(e,o,Object);for(;l.obj===void 0&&o.length;)i=`${o[o.length-1]}.${i}`,o=o.slice(0,o.length-1),l=an(e,o,Object),l!=null&&l.obj&&typeof l.obj[`${l.k}.${i}`]<"u"&&(l.obj=void 0);l.obj[`${l.k}.${i}`]=r},h1=(e,t,r,s)=>{const{obj:n,k:i}=an(e,t,Object);n[i]=n[i]||[],n[i].push(r)},ri=(e,t)=>{const{obj:r,k:s}=an(e,t);if(r&&Object.prototype.hasOwnProperty.call(r,s))return r[s]},p1=(e,t,r)=>{const s=ri(e,r);return s!==void 0?s:ri(t,r)},Lh=(e,t,r)=>{for(const s in t)s!=="__proto__"&&s!=="constructor"&&(s in e?K(e[s])||e[s]instanceof String||K(t[s])||t[s]instanceof String?r&&(e[s]=t[s]):Lh(e[s],t[s],r):e[s]=t[s]);return e},qr=e=>e.replace(/[\-\[\]\/\{\}\(\)\*\+\?\.\\\^\$\|]/g,"\\$&");var g1={"&":"&amp;","<":"&lt;",">":"&gt;",'"':"&quot;","'":"&#39;","/":"&#x2F;"};const x1=e=>K(e)?e.replace(/[&<>"'\/]/g,t=>g1[t]):e;class y1{constructor(t){this.capacity=t,this.regExpMap=new Map,this.regExpQueue=[]}getRegExp(t){const r=this.regExpMap.get(t);if(r!==void 0)return r;const s=new RegExp(t);return this.regExpQueue.length===this.capacity&&this.regExpMap.delete(this.regExpQueue.shift()),this.regExpMap.set(t,s),this.regExpQueue.push(t),s}}const v1=[" ",",","?","!",";"],w1=new y1(20),N1=(e,t,r)=>{t=t||"",r=r||"";const s=v1.filter(o=>t.indexOf(o)<0&&r.indexOf(o)<0);if(s.length===0)return!0;const n=w1.getRegExp(`(${s.map(o=>o==="?"?"\\?":o).join("|")})`);let i=!n.test(e);if(!i){const o=e.indexOf(r);o>0&&!n.test(e.substring(0,o))&&(i=!0)}return i},Sl=(e,t,r=".")=>{if(!e)return;if(e[t])return Object.prototype.hasOwnProperty.call(e,t)?e[t]:void 0;const s=t.split(r);let n=e;for(let i=0;i<s.length;){if(!n||typeof n!="object")return;let o,l="";for(let c=i;c<s.length;++c)if(c!==i&&(l+=r),l+=s[c],o=n[l],o!==void 0){if(["string","number","boolean"].indexOf(typeof o)>-1&&c<s.length-1)continue;i+=c-i+1;break}n=o}return n},Pn=e=>e==null?void 0:e.replace("_","-"),j1={type:"logger",log(e){this.output("log",e)},warn(e){this.output("warn",e)},error(e){this.output("error",e)},output(e,t){var r,s;(s=(r=console==null?void 0:console[e])==null?void 0:r.apply)==null||s.call(r,console,t)}};class si{constructor(t,r={}){this.init(t,r)}init(t,r={}){this.prefix=r.prefix||"i18next:",this.logger=t||j1,this.options=r,this.debug=r.debug}log(...t){return this.forward(t,"log","",!0)}warn(...t){return this.forward(t,"warn","",!0)}error(...t){return this.forward(t,"error","")}deprecate(...t){return this.forward(t,"warn","WARNING DEPRECATED: ",!0)}forward(t,r,s,n){return n&&!this.debug?null:(K(t[0])&&(t[0]=`${s}${this.prefix} ${t[0]}`),this.logger[r](t))}create(t){return new si(this.logger,{prefix:`${this.prefix}:${t}:`,...this.options})}clone(t){return t=t||this.options,t.prefix=t.prefix||this.prefix,new si(this.logger,t)}}var Et=new si;class Ti{constructor(){this.observers={}}on(t,r){return t.split(" ").forEach(s=>{this.observers[s]||(this.observers[s]=new Map);const n=this.observers[s].get(r)||0;this.observers[s].set(r,n+1)}),this}off(t,r){if(this.observers[t]){if(!r){delete this.observers[t];return}this.observers[t].delete(r)}}emit(t,...r){this.observers[t]&&Array.from(this.observers[t].entries()).forEach(([n,i])=>{for(let o=0;o<i;o++)n(...r)}),this.observers["*"]&&Array.from(this.observers["*"].entries()).forEach(([n,i])=>{for(let o=0;o<i;o++)n.apply(n,[t,...r])})}}class Nd extends Ti{constructor(t,r={ns:["translation"],defaultNS:"translation"}){super(),this.data=t||{},this.options=r,this.options.keySeparator===void 0&&(this.options.keySeparator="."),this.options.ignoreJSONStructure===void 0&&(this.options.ignoreJSONStructure=!0)}addNamespaces(t){this.options.ns.indexOf(t)<0&&this.options.ns.push(t)}removeNamespaces(t){const r=this.options.ns.indexOf(t);r>-1&&this.options.ns.splice(r,1)}getResource(t,r,s,n={}){var u,d;const i=n.keySeparator!==void 0?n.keySeparator:this.options.keySeparator,o=n.ignoreJSONStructure!==void 0?n.ignoreJSONStructure:this.options.ignoreJSONStructure;let l;t.indexOf(".")>-1?l=t.split("."):(l=[t,r],s&&(Array.isArray(s)?l.push(...s):K(s)&&i?l.push(...s.split(i)):l.push(s)));const c=ri(this.data,l);return!c&&!r&&!s&&t.indexOf(".")>-1&&(t=l[0],r=l[1],s=l.slice(2).join(".")),c||!o||!K(s)?c:Sl((d=(u=this.data)==null?void 0:u[t])==null?void 0:d[r],s,i)}addResource(t,r,s,n,i={silent:!1}){const o=i.keySeparator!==void 0?i.keySeparator:this.options.keySeparator;let l=[t,r];s&&(l=l.concat(o?s.split(o):s)),t.indexOf(".")>-1&&(l=t.split("."),n=r,r=l[1]),this.addNamespaces(r),wd(this.data,l,n),i.silent||this.emit("added",t,r,s,n)}addResources(t,r,s,n={silent:!1}){for(const i in s)(K(s[i])||Array.isArray(s[i]))&&this.addResource(t,r,i,s[i],{silent:!0});n.silent||this.emit("added",t,r,s)}addResourceBundle(t,r,s,n,i,o={silent:!1,skipCopy:!1}){let l=[t,r];t.indexOf(".")>-1&&(l=t.split("."),n=s,s=r,r=l[1]),this.addNamespaces(r);let c=ri(this.data,l)||{};o.skipCopy||(s=JSON.parse(JSON.stringify(s))),n?Lh(c,s,i):c={...c,...s},wd(this.data,l,c),o.silent||this.emit("added",t,r,s)}removeResourceBundle(t,r){this.hasResourceBundle(t,r)&&delete this.data[t][r],this.removeNamespaces(r),this.emit("removed",t,r)}hasResourceBundle(t,r){return this.getResource(t,r)!==void 0}getResourceBundle(t,r){return r||(r=this.options.defaultNS),this.getResource(t,r)}getDataByLanguage(t){return this.data[t]}hasLanguageSomeTranslations(t){const r=this.getDataByLanguage(t);return!!(r&&Object.keys(r)||[]).find(n=>r[n]&&Object.keys(r[n]).length>0)}toJSON(){return this.data}}var Th={processors:{},addPostProcessor(e){this.processors[e.name]=e},handle(e,t,r,s,n){return e.forEach(i=>{var o;t=((o=this.processors[i])==null?void 0:o.process(t,r,s,n))??t}),t}};const jd={},bd=e=>!K(e)&&typeof e!="boolean"&&typeof e!="number";class ni extends Ti{constructor(t,r={}){super(),f1(["resourceStore","languageUtils","pluralResolver","interpolator","backendConnector","i18nFormat","utils"],t,this),this.options=r,this.options.keySeparator===void 0&&(this.options.keySeparator="."),this.logger=Et.create("translator")}changeLanguage(t){t&&(this.language=t)}exists(t,r={interpolation:{}}){const s={...r};if(t==null)return!1;const n=this.resolve(t,s);return(n==null?void 0:n.res)!==void 0}extractFromKey(t,r){let s=r.nsSeparator!==void 0?r.nsSeparator:this.options.nsSeparator;s===void 0&&(s=":");const n=r.keySeparator!==void 0?r.keySeparator:this.options.keySeparator;let i=r.ns||this.options.defaultNS||[];const o=s&&t.indexOf(s)>-1,l=!this.options.userDefinedKeySeparator&&!r.keySeparator&&!this.options.userDefinedNsSeparator&&!r.nsSeparator&&!N1(t,s,n);if(o&&!l){const c=t.match(this.interpolator.nestingRegexp);if(c&&c.length>0)return{key:t,namespaces:K(i)?[i]:i};const u=t.split(s);(s!==n||s===n&&this.options.ns.indexOf(u[0])>-1)&&(i=u.shift()),t=u.join(n)}return{key:t,namespaces:K(i)?[i]:i}}translate(t,r,s){let n=typeof r=="object"?{...r}:r;if(typeof n!="object"&&this.options.overloadTranslationOptionHandler&&(n=this.options.overloadTranslationOptionHandler(arguments)),typeof options=="object"&&(n={...n}),n||(n={}),t==null)return"";Array.isArray(t)||(t=[String(t)]);const i=n.returnDetails!==void 0?n.returnDetails:this.options.returnDetails,o=n.keySeparator!==void 0?n.keySeparator:this.options.keySeparator,{key:l,namespaces:c}=this.extractFromKey(t[t.length-1],n),u=c[c.length-1];let d=n.nsSeparator!==void 0?n.nsSeparator:this.options.nsSeparator;d===void 0&&(d=":");const f=n.lng||this.language,m=n.appendNamespaceToCIMode||this.options.appendNamespaceToCIMode;if((f==null?void 0:f.toLowerCase())==="cimode")return m?i?{res:`${u}${d}${l}`,usedKey:l,exactUsedKey:l,usedLng:f,usedNS:u,usedParams:this.getUsedParamsDetails(n)}:`${u}${d}${l}`:i?{res:l,usedKey:l,exactUsedKey:l,usedLng:f,usedNS:u,usedParams:this.getUsedParamsDetails(n)}:l;const v=this.resolve(t,n);let g=v==null?void 0:v.res;const w=(v==null?void 0:v.usedKey)||l,y=(v==null?void 0:v.exactUsedKey)||l,p=["[object Number]","[object Function]","[object RegExp]"],h=n.joinArrays!==void 0?n.joinArrays:this.options.joinArrays,x=!this.i18nFormat||this.i18nFormat.handleAsObject,b=n.count!==void 0&&!K(n.count),k=ni.hasDefaultValue(n),E=b?this.pluralResolver.getSuffix(f,n.count,n):"",O=n.ordinal&&b?this.pluralResolver.getSuffix(f,n.count,{ordinal:!1}):"",$=b&&!n.ordinal&&n.count===0,C=$&&n[`defaultValue${this.options.pluralSeparator}zero`]||n[`defaultValue${E}`]||n[`defaultValue${O}`]||n.defaultValue;let D=g;x&&!g&&k&&(D=C);const U=bd(D),W=Object.prototype.toString.apply(D);if(x&&D&&U&&p.indexOf(W)<0&&!(K(h)&&Array.isArray(D))){if(!n.returnObjects&&!this.options.returnObjects){this.options.returnedObjectHandler||this.logger.warn("accessing an object - but returnObjects options is not enabled!");const I=this.options.returnedObjectHandler?this.options.returnedObjectHandler(w,D,{...n,ns:c}):`key '${l} (${this.language})' returned an object instead of string.`;return i?(v.res=I,v.usedParams=this.getUsedParamsDetails(n),v):I}if(o){const I=Array.isArray(D),Y=I?[]:{},Ne=I?y:w;for(const te in D)if(Object.prototype.hasOwnProperty.call(D,te)){const ue=`${Ne}${o}${te}`;k&&!g?Y[te]=this.translate(ue,{...n,defaultValue:bd(C)?C[te]:void 0,joinArrays:!1,ns:c}):Y[te]=this.translate(ue,{...n,joinArrays:!1,ns:c}),Y[te]===ue&&(Y[te]=D[te])}g=Y}}else if(x&&K(h)&&Array.isArray(g))g=g.join(h),g&&(g=this.extendTranslation(g,t,n,s));else{let I=!1,Y=!1;!this.isValidLookup(g)&&k&&(I=!0,g=C),this.isValidLookup(g)||(Y=!0,g=l);const te=(n.missingKeyNoValueFallbackToKey||this.options.missingKeyNoValueFallbackToKey)&&Y?void 0:g,ue=k&&C!==g&&this.options.updateMissing;if(Y||I||ue){if(this.logger.log(ue?"updateKey":"missingKey",f,u,l,ue?C:g),o){const J=this.resolve(l,{...n,keySeparator:!1});J&&J.res&&this.logger.warn("Seems the loaded translations were in flat JSON format instead of nested. Either set keySeparator: false on init or make sure your translations are published in nested format.")}let T=[];const V=this.languageUtils.getFallbackCodes(this.options.fallbackLng,n.lng||this.language);if(this.options.saveMissingTo==="fallback"&&V&&V[0])for(let J=0;J<V.length;J++)T.push(V[J]);else this.options.saveMissingTo==="all"?T=this.languageUtils.toResolveHierarchy(n.lng||this.language):T.push(n.lng||this.language);const q=(J,ne,Xe)=>{var Lt;const it=k&&Xe!==g?Xe:te;this.options.missingKeyHandler?this.options.missingKeyHandler(J,u,ne,it,ue,n):(Lt=this.backendConnector)!=null&&Lt.saveMissing&&this.backendConnector.saveMissing(J,u,ne,it,ue,n),this.emit("missingKey",J,u,ne,g)};this.options.saveMissing&&(this.options.saveMissingPlurals&&b?T.forEach(J=>{const ne=this.pluralResolver.getSuffixes(J,n);$&&n[`defaultValue${this.options.pluralSeparator}zero`]&&ne.indexOf(`${this.options.pluralSeparator}zero`)<0&&ne.push(`${this.options.pluralSeparator}zero`),ne.forEach(Xe=>{q([J],l+Xe,n[`defaultValue${Xe}`]||C)})}):q(T,l,C))}g=this.extendTranslation(g,t,n,v,s),Y&&g===l&&this.options.appendNamespaceToMissingKey&&(g=`${u}${d}${l}`),(Y||I)&&this.options.parseMissingKeyHandler&&(g=this.options.parseMissingKeyHandler(this.options.appendNamespaceToMissingKey?`${u}${d}${l}`:l,I?g:void 0,n))}return i?(v.res=g,v.usedParams=this.getUsedParamsDetails(n),v):g}extendTranslation(t,r,s,n,i){var c,u;if((c=this.i18nFormat)!=null&&c.parse)t=this.i18nFormat.parse(t,{...this.options.interpolation.defaultVariables,...s},s.lng||this.language||n.usedLng,n.usedNS,n.usedKey,{resolved:n});else if(!s.skipInterpolation){s.interpolation&&this.interpolator.init({...s,interpolation:{...this.options.interpolation,...s.interpolation}});const d=K(t)&&(((u=s==null?void 0:s.interpolation)==null?void 0:u.skipOnVariables)!==void 0?s.interpolation.skipOnVariables:this.options.interpolation.skipOnVariables);let f;if(d){const v=t.match(this.interpolator.nestingRegexp);f=v&&v.length}let m=s.replace&&!K(s.replace)?s.replace:s;if(this.options.interpolation.defaultVariables&&(m={...this.options.interpolation.defaultVariables,...m}),t=this.interpolator.interpolate(t,m,s.lng||this.language||n.usedLng,s),d){const v=t.match(this.interpolator.nestingRegexp),g=v&&v.length;f<g&&(s.nest=!1)}!s.lng&&n&&n.res&&(s.lng=this.language||n.usedLng),s.nest!==!1&&(t=this.interpolator.nest(t,(...v)=>(i==null?void 0:i[0])===v[0]&&!s.context?(this.logger.warn(`It seems you are nesting recursively key: ${v[0]} in key: ${r[0]}`),null):this.translate(...v,r),s)),s.interpolation&&this.interpolator.reset()}const o=s.postProcess||this.options.postProcess,l=K(o)?[o]:o;return t!=null&&(l!=null&&l.length)&&s.applyPostProcessor!==!1&&(t=Th.handle(l,t,r,this.options&&this.options.postProcessPassResolved?{i18nResolved:{...n,usedParams:this.getUsedParamsDetails(s)},...s}:s,this)),t}resolve(t,r={}){let s,n,i,o,l;return K(t)&&(t=[t]),t.forEach(c=>{if(this.isValidLookup(s))return;const u=this.extractFromKey(c,r),d=u.key;n=d;let f=u.namespaces;this.options.fallbackNS&&(f=f.concat(this.options.fallbackNS));const m=r.count!==void 0&&!K(r.count),v=m&&!r.ordinal&&r.count===0,g=r.context!==void 0&&(K(r.context)||typeof r.context=="number")&&r.context!=="",w=r.lngs?r.lngs:this.languageUtils.toResolveHierarchy(r.lng||this.language,r.fallbackLng);f.forEach(y=>{var p,h;this.isValidLookup(s)||(l=y,!jd[`${w[0]}-${y}`]&&((p=this.utils)!=null&&p.hasLoadedNamespace)&&!((h=this.utils)!=null&&h.hasLoadedNamespace(l))&&(jd[`${w[0]}-${y}`]=!0,this.logger.warn(`key "${n}" for languages "${w.join(", ")}" won't get resolved as namespace "${l}" was not yet loaded`,"This means something IS WRONG in your setup. You access the t function before i18next.init / i18next.loadNamespace / i18next.changeLanguage was done. Wait for the callback or Promise to resolve before accessing it!!!")),w.forEach(x=>{var E;if(this.isValidLookup(s))return;o=x;const b=[d];if((E=this.i18nFormat)!=null&&E.addLookupKeys)this.i18nFormat.addLookupKeys(b,d,x,y,r);else{let O;m&&(O=this.pluralResolver.getSuffix(x,r.count,r));const $=`${this.options.pluralSeparator}zero`,C=`${this.options.pluralSeparator}ordinal${this.options.pluralSeparator}`;if(m&&(b.push(d+O),r.ordinal&&O.indexOf(C)===0&&b.push(d+O.replace(C,this.options.pluralSeparator)),v&&b.push(d+$)),g){const D=`${d}${this.options.contextSeparator}${r.context}`;b.push(D),m&&(b.push(D+O),r.ordinal&&O.indexOf(C)===0&&b.push(D+O.replace(C,this.options.pluralSeparator)),v&&b.push(D+$))}}let k;for(;k=b.pop();)this.isValidLookup(s)||(i=k,s=this.getResource(x,y,k,r))}))})}),{res:s,usedKey:n,exactUsedKey:i,usedLng:o,usedNS:l}}isValidLookup(t){return t!==void 0&&!(!this.options.returnNull&&t===null)&&!(!this.options.returnEmptyString&&t==="")}getResource(t,r,s,n={}){var i;return(i=this.i18nFormat)!=null&&i.getResource?this.i18nFormat.getResource(t,r,s,n):this.resourceStore.getResource(t,r,s,n)}getUsedParamsDetails(t={}){const r=["defaultValue","ordinal","context","replace","lng","lngs","fallbackLng","ns","keySeparator","nsSeparator","returnObjects","returnDetails","joinArrays","postProcess","interpolation"],s=t.replace&&!K(t.replace);let n=s?t.replace:t;if(s&&typeof t.count<"u"&&(n.count=t.count),this.options.interpolation.defaultVariables&&(n={...this.options.interpolation.defaultVariables,...n}),!s){n={...n};for(const i of r)delete n[i]}return n}static hasDefaultValue(t){const r="defaultValue";for(const s in t)if(Object.prototype.hasOwnProperty.call(t,s)&&r===s.substring(0,r.length)&&t[s]!==void 0)return!0;return!1}}class kd{constructor(t){this.options=t,this.supportedLngs=this.options.supportedLngs||!1,this.logger=Et.create("languageUtils")}getScriptPartFromCode(t){if(t=Pn(t),!t||t.indexOf("-")<0)return null;const r=t.split("-");return r.length===2||(r.pop(),r[r.length-1].toLowerCase()==="x")?null:this.formatLanguageCode(r.join("-"))}getLanguagePartFromCode(t){if(t=Pn(t),!t||t.indexOf("-")<0)return t;const r=t.split("-");return this.formatLanguageCode(r[0])}formatLanguageCode(t){if(K(t)&&t.indexOf("-")>-1){let r;try{r=Intl.getCanonicalLocales(t)[0]}catch{}return r&&this.options.lowerCaseLng&&(r=r.toLowerCase()),r||(this.options.lowerCaseLng?t.toLowerCase():t)}return this.options.cleanCode||this.options.lowerCaseLng?t.toLowerCase():t}isSupportedCode(t){return(this.options.load==="languageOnly"||this.options.nonExplicitSupportedLngs)&&(t=this.getLanguagePartFromCode(t)),!this.supportedLngs||!this.supportedLngs.length||this.supportedLngs.indexOf(t)>-1}getBestMatchFromCodes(t){if(!t)return null;let r;return t.forEach(s=>{if(r)return;const n=this.formatLanguageCode(s);(!this.options.supportedLngs||this.isSupportedCode(n))&&(r=n)}),!r&&this.options.supportedLngs&&t.forEach(s=>{if(r)return;const n=this.getScriptPartFromCode(s);if(this.isSupportedCode(n))return r=n;const i=this.getLanguagePartFromCode(s);if(this.isSupportedCode(i))return r=i;r=this.options.supportedLngs.find(o=>{if(o===i)return o;if(!(o.indexOf("-")<0&&i.indexOf("-")<0)&&(o.indexOf("-")>0&&i.indexOf("-")<0&&o.substring(0,o.indexOf("-"))===i||o.indexOf(i)===0&&i.length>1))return o})}),r||(r=this.getFallbackCodes(this.options.fallbackLng)[0]),r}getFallbackCodes(t,r){if(!t)return[];if(typeof t=="function"&&(t=t(r)),K(t)&&(t=[t]),Array.isArray(t))return t;if(!r)return t.default||[];let s=t[r];return s||(s=t[this.getScriptPartFromCode(r)]),s||(s=t[this.formatLanguageCode(r)]),s||(s=t[this.getLanguagePartFromCode(r)]),s||(s=t.default),s||[]}toResolveHierarchy(t,r){const s=this.getFallbackCodes((r===!1?[]:r)||this.options.fallbackLng||[],t),n=[],i=o=>{o&&(this.isSupportedCode(o)?n.push(o):this.logger.warn(`rejecting language code not found in supportedLngs: ${o}`))};return K(t)&&(t.indexOf("-")>-1||t.indexOf("_")>-1)?(this.options.load!=="languageOnly"&&i(this.formatLanguageCode(t)),this.options.load!=="languageOnly"&&this.options.load!=="currentOnly"&&i(this.getScriptPartFromCode(t)),this.options.load!=="currentOnly"&&i(this.getLanguagePartFromCode(t))):K(t)&&i(this.formatLanguageCode(t)),s.forEach(o=>{n.indexOf(o)<0&&i(this.formatLanguageCode(o))}),n}}const Sd={zero:0,one:1,two:2,few:3,many:4,other:5},Cd={select:e=>e===1?"one":"other",resolvedOptions:()=>({pluralCategories:["one","other"]})};class b1{constructor(t,r={}){this.languageUtils=t,this.options=r,this.logger=Et.create("pluralResolver"),this.pluralRulesCache={}}addRule(t,r){this.rules[t]=r}clearCache(){this.pluralRulesCache={}}getRule(t,r={}){const s=Pn(t==="dev"?"en":t),n=r.ordinal?"ordinal":"cardinal",i=JSON.stringify({cleanedCode:s,type:n});if(i in this.pluralRulesCache)return this.pluralRulesCache[i];let o;try{o=new Intl.PluralRules(s,{type:n})}catch{if(!Intl)return this.logger.error("No Intl support, please use an Intl polyfill!"),Cd;if(!t.match(/-|_/))return Cd;const c=this.languageUtils.getLanguagePartFromCode(t);o=this.getRule(c,r)}return this.pluralRulesCache[i]=o,o}needsPlural(t,r={}){let s=this.getRule(t,r);return s||(s=this.getRule("dev",r)),(s==null?void 0:s.resolvedOptions().pluralCategories.length)>1}getPluralFormsOfKey(t,r,s={}){return this.getSuffixes(t,s).map(n=>`${r}${n}`)}getSuffixes(t,r={}){let s=this.getRule(t,r);return s||(s=this.getRule("dev",r)),s?s.resolvedOptions().pluralCategories.sort((n,i)=>Sd[n]-Sd[i]).map(n=>`${this.options.prepend}${r.ordinal?`ordinal${this.options.prepend}`:""}${n}`):[]}getSuffix(t,r,s={}){const n=this.getRule(t,s);return n?`${this.options.prepend}${s.ordinal?`ordinal${this.options.prepend}`:""}${n.select(r)}`:(this.logger.warn(`no plural rule found for: ${t}`),this.getSuffix("dev",r,s))}}const $d=(e,t,r,s=".",n=!0)=>{let i=p1(e,t,r);return!i&&n&&K(r)&&(i=Sl(e,r,s),i===void 0&&(i=Sl(t,r,s))),i},yo=e=>e.replace(/\$/g,"$$$$");class k1{constructor(t={}){var r;this.logger=Et.create("interpolator"),this.options=t,this.format=((r=t==null?void 0:t.interpolation)==null?void 0:r.format)||(s=>s),this.init(t)}init(t={}){t.interpolation||(t.interpolation={escapeValue:!0});const{escape:r,escapeValue:s,useRawValueToEscape:n,prefix:i,prefixEscaped:o,suffix:l,suffixEscaped:c,formatSeparator:u,unescapeSuffix:d,unescapePrefix:f,nestingPrefix:m,nestingPrefixEscaped:v,nestingSuffix:g,nestingSuffixEscaped:w,nestingOptionsSeparator:y,maxReplaces:p,alwaysFormat:h}=t.interpolation;this.escape=r!==void 0?r:x1,this.escapeValue=s!==void 0?s:!0,this.useRawValueToEscape=n!==void 0?n:!1,this.prefix=i?qr(i):o||"{{",this.suffix=l?qr(l):c||"}}",this.formatSeparator=u||",",this.unescapePrefix=d?"":f||"-",this.unescapeSuffix=this.unescapePrefix?"":d||"",this.nestingPrefix=m?qr(m):v||qr("$t("),this.nestingSuffix=g?qr(g):w||qr(")"),this.nestingOptionsSeparator=y||",",this.maxReplaces=p||1e3,this.alwaysFormat=h!==void 0?h:!1,this.resetRegExp()}reset(){this.options&&this.init(this.options)}resetRegExp(){const t=(r,s)=>(r==null?void 0:r.source)===s?(r.lastIndex=0,r):new RegExp(s,"g");this.regexp=t(this.regexp,`${this.prefix}(.+?)${this.suffix}`),this.regexpUnescape=t(this.regexpUnescape,`${this.prefix}${this.unescapePrefix}(.+?)${this.unescapeSuffix}${this.suffix}`),this.nestingRegexp=t(this.nestingRegexp,`${this.nestingPrefix}(.+?)${this.nestingSuffix}`)}interpolate(t,r,s,n){var v;let i,o,l;const c=this.options&&this.options.interpolation&&this.options.interpolation.defaultVariables||{},u=g=>{if(g.indexOf(this.formatSeparator)<0){const h=$d(r,c,g,this.options.keySeparator,this.options.ignoreJSONStructure);return this.alwaysFormat?this.format(h,void 0,s,{...n,...r,interpolationkey:g}):h}const w=g.split(this.formatSeparator),y=w.shift().trim(),p=w.join(this.formatSeparator).trim();return this.format($d(r,c,y,this.options.keySeparator,this.options.ignoreJSONStructure),p,s,{...n,...r,interpolationkey:y})};this.resetRegExp();const d=(n==null?void 0:n.missingInterpolationHandler)||this.options.missingInterpolationHandler,f=((v=n==null?void 0:n.interpolation)==null?void 0:v.skipOnVariables)!==void 0?n.interpolation.skipOnVariables:this.options.interpolation.skipOnVariables;return[{regex:this.regexpUnescape,safeValue:g=>yo(g)},{regex:this.regexp,safeValue:g=>this.escapeValue?yo(this.escape(g)):yo(g)}].forEach(g=>{for(l=0;i=g.regex.exec(t);){const w=i[1].trim();if(o=u(w),o===void 0)if(typeof d=="function"){const p=d(t,i,n);o=K(p)?p:""}else if(n&&Object.prototype.hasOwnProperty.call(n,w))o="";else if(f){o=i[0];continue}else this.logger.warn(`missed to pass in variable ${w} for interpolating ${t}`),o="";else!K(o)&&!this.useRawValueToEscape&&(o=xd(o));const y=g.safeValue(o);if(t=t.replace(i[0],y),f?(g.regex.lastIndex+=o.length,g.regex.lastIndex-=i[0].length):g.regex.lastIndex=0,l++,l>=this.maxReplaces)break}}),t}nest(t,r,s={}){let n,i,o;const l=(c,u)=>{const d=this.nestingOptionsSeparator;if(c.indexOf(d)<0)return c;const f=c.split(new RegExp(`${d}[ ]*{`));let m=`{${f[1]}`;c=f[0],m=this.interpolate(m,o);const v=m.match(/'/g),g=m.match(/"/g);(((v==null?void 0:v.length)??0)%2===0&&!g||g.length%2!==0)&&(m=m.replace(/'/g,'"'));try{o=JSON.parse(m),u&&(o={...u,...o})}catch(w){return this.logger.warn(`failed parsing options string in nesting for key ${c}`,w),`${c}${d}${m}`}return o.defaultValue&&o.defaultValue.indexOf(this.prefix)>-1&&delete o.defaultValue,c};for(;n=this.nestingRegexp.exec(t);){let c=[];o={...s},o=o.replace&&!K(o.replace)?o.replace:o,o.applyPostProcessor=!1,delete o.defaultValue;const u=/{.*}/.test(n[1])?n[1].lastIndexOf("}")+1:n[1].indexOf(this.formatSeparator);if(u!==-1&&(c=n[1].slice(u).split(this.formatSeparator).map(d=>d.trim()).filter(Boolean),n[1]=n[1].slice(0,u)),i=r(l.call(this,n[1].trim(),o),o),i&&n[0]===t&&!K(i))return i;K(i)||(i=xd(i)),i||(this.logger.warn(`missed to resolve ${n[1]} for nesting ${t}`),i=""),c.length&&(i=c.reduce((d,f)=>this.format(d,f,s.lng,{...s,interpolationkey:n[1].trim()}),i.trim())),t=t.replace(n[0],i),this.regexp.lastIndex=0}return t}}const S1=e=>{let t=e.toLowerCase().trim();const r={};if(e.indexOf("(")>-1){const s=e.split("(");t=s[0].toLowerCase().trim();const n=s[1].substring(0,s[1].length-1);t==="currency"&&n.indexOf(":")<0?r.currency||(r.currency=n.trim()):t==="relativetime"&&n.indexOf(":")<0?r.range||(r.range=n.trim()):n.split(";").forEach(o=>{if(o){const[l,...c]=o.split(":"),u=c.join(":").trim().replace(/^'+|'+$/g,""),d=l.trim();r[d]||(r[d]=u),u==="false"&&(r[d]=!1),u==="true"&&(r[d]=!0),isNaN(u)||(r[d]=parseInt(u,10))}})}return{formatName:t,formatOptions:r}},Pd=e=>{const t={};return(r,s,n)=>{let i=n;n&&n.interpolationkey&&n.formatParams&&n.formatParams[n.interpolationkey]&&n[n.interpolationkey]&&(i={...i,[n.interpolationkey]:void 0});const o=s+JSON.stringify(i);let l=t[o];return l||(l=e(Pn(s),n),t[o]=l),l(r)}},C1=e=>(t,r,s)=>e(Pn(r),s)(t);class $1{constructor(t={}){this.logger=Et.create("formatter"),this.options=t,this.init(t)}init(t,r={interpolation:{}}){this.formatSeparator=r.interpolation.formatSeparator||",";const s=r.cacheInBuiltFormats?Pd:C1;this.formats={number:s((n,i)=>{const o=new Intl.NumberFormat(n,{...i});return l=>o.format(l)}),currency:s((n,i)=>{const o=new Intl.NumberFormat(n,{...i,style:"currency"});return l=>o.format(l)}),datetime:s((n,i)=>{const o=new Intl.DateTimeFormat(n,{...i});return l=>o.format(l)}),relativetime:s((n,i)=>{const o=new Intl.RelativeTimeFormat(n,{...i});return l=>o.format(l,i.range||"day")}),list:s((n,i)=>{const o=new Intl.ListFormat(n,{...i});return l=>o.format(l)})}}add(t,r){this.formats[t.toLowerCase().trim()]=r}addCached(t,r){this.formats[t.toLowerCase().trim()]=Pd(r)}format(t,r,s,n={}){const i=r.split(this.formatSeparator);if(i.length>1&&i[0].indexOf("(")>1&&i[0].indexOf(")")<0&&i.find(l=>l.indexOf(")")>-1)){const l=i.findIndex(c=>c.indexOf(")")>-1);i[0]=[i[0],...i.splice(1,l)].join(this.formatSeparator)}return i.reduce((l,c)=>{var f;const{formatName:u,formatOptions:d}=S1(c);if(this.formats[u]){let m=l;try{const v=((f=n==null?void 0:n.formatParams)==null?void 0:f[n.interpolationkey])||{},g=v.locale||v.lng||n.locale||n.lng||s;m=this.formats[u](l,g,{...d,...n,...v})}catch(v){this.logger.warn(v)}return m}else this.logger.warn(`there was no format function for ${u}`);return l},t)}}const P1=(e,t)=>{e.pending[t]!==void 0&&(delete e.pending[t],e.pendingCount--)};class E1 extends Ti{constructor(t,r,s,n={}){var i,o;super(),this.backend=t,this.store=r,this.services=s,this.languageUtils=s.languageUtils,this.options=n,this.logger=Et.create("backendConnector"),this.waitingReads=[],this.maxParallelReads=n.maxParallelReads||10,this.readingCalls=0,this.maxRetries=n.maxRetries>=0?n.maxRetries:5,this.retryTimeout=n.retryTimeout>=1?n.retryTimeout:350,this.state={},this.queue=[],(o=(i=this.backend)==null?void 0:i.init)==null||o.call(i,s,n.backend,n)}queueLoad(t,r,s,n){const i={},o={},l={},c={};return t.forEach(u=>{let d=!0;r.forEach(f=>{const m=`${u}|${f}`;!s.reload&&this.store.hasResourceBundle(u,f)?this.state[m]=2:this.state[m]<0||(this.state[m]===1?o[m]===void 0&&(o[m]=!0):(this.state[m]=1,d=!1,o[m]===void 0&&(o[m]=!0),i[m]===void 0&&(i[m]=!0),c[f]===void 0&&(c[f]=!0)))}),d||(l[u]=!0)}),(Object.keys(i).length||Object.keys(o).length)&&this.queue.push({pending:o,pendingCount:Object.keys(o).length,loaded:{},errors:[],callback:n}),{toLoad:Object.keys(i),pending:Object.keys(o),toLoadLanguages:Object.keys(l),toLoadNamespaces:Object.keys(c)}}loaded(t,r,s){const n=t.split("|"),i=n[0],o=n[1];r&&this.emit("failedLoading",i,o,r),!r&&s&&this.store.addResourceBundle(i,o,s,void 0,void 0,{skipCopy:!0}),this.state[t]=r?-1:2,r&&s&&(this.state[t]=0);const l={};this.queue.forEach(c=>{h1(c.loaded,[i],o),P1(c,t),r&&c.errors.push(r),c.pendingCount===0&&!c.done&&(Object.keys(c.loaded).forEach(u=>{l[u]||(l[u]={});const d=c.loaded[u];d.length&&d.forEach(f=>{l[u][f]===void 0&&(l[u][f]=!0)})}),c.done=!0,c.errors.length?c.callback(c.errors):c.callback())}),this.emit("loaded",l),this.queue=this.queue.filter(c=>!c.done)}read(t,r,s,n=0,i=this.retryTimeout,o){if(!t.length)return o(null,{});if(this.readingCalls>=this.maxParallelReads){this.waitingReads.push({lng:t,ns:r,fcName:s,tried:n,wait:i,callback:o});return}this.readingCalls++;const l=(u,d)=>{if(this.readingCalls--,this.waitingReads.length>0){const f=this.waitingReads.shift();this.read(f.lng,f.ns,f.fcName,f.tried,f.wait,f.callback)}if(u&&d&&n<this.maxRetries){setTimeout(()=>{this.read.call(this,t,r,s,n+1,i*2,o)},i);return}o(u,d)},c=this.backend[s].bind(this.backend);if(c.length===2){try{const u=c(t,r);u&&typeof u.then=="function"?u.then(d=>l(null,d)).catch(l):l(null,u)}catch(u){l(u)}return}return c(t,r,l)}prepareLoading(t,r,s={},n){if(!this.backend)return this.logger.warn("No backend was added via i18next.use. Will not load resources."),n&&n();K(t)&&(t=this.languageUtils.toResolveHierarchy(t)),K(r)&&(r=[r]);const i=this.queueLoad(t,r,s,n);if(!i.toLoad.length)return i.pending.length||n(),null;i.toLoad.forEach(o=>{this.loadOne(o)})}load(t,r,s){this.prepareLoading(t,r,{},s)}reload(t,r,s){this.prepareLoading(t,r,{reload:!0},s)}loadOne(t,r=""){const s=t.split("|"),n=s[0],i=s[1];this.read(n,i,"read",void 0,void 0,(o,l)=>{o&&this.logger.warn(`${r}loading namespace ${i} for language ${n} failed`,o),!o&&l&&this.logger.log(`${r}loaded namespace ${i} for language ${n}`,l),this.loaded(t,o,l)})}saveMissing(t,r,s,n,i,o={},l=()=>{}){var c,u,d,f,m;if((u=(c=this.services)==null?void 0:c.utils)!=null&&u.hasLoadedNamespace&&!((f=(d=this.services)==null?void 0:d.utils)!=null&&f.hasLoadedNamespace(r))){this.logger.warn(`did not save key "${s}" as the namespace "${r}" was not yet loaded`,"This means something IS WRONG in your setup. You access the t function before i18next.init / i18next.loadNamespace / i18next.changeLanguage was done. Wait for the callback or Promise to resolve before accessing it!!!");return}if(!(s==null||s==="")){if((m=this.backend)!=null&&m.create){const v={...o,isUpdate:i},g=this.backend.create.bind(this.backend);if(g.length<6)try{let w;g.length===5?w=g(t,r,s,n,v):w=g(t,r,s,n),w&&typeof w.then=="function"?w.then(y=>l(null,y)).catch(l):l(null,w)}catch(w){l(w)}else g(t,r,s,n,l,v)}!t||!t[0]||this.store.addResource(t[0],r,s,n)}}}const Ed=()=>({debug:!1,initAsync:!0,ns:["translation"],defaultNS:["translation"],fallbackLng:["dev"],fallbackNS:!1,supportedLngs:!1,nonExplicitSupportedLngs:!1,load:"all",preload:!1,simplifyPluralSuffix:!0,keySeparator:".",nsSeparator:":",pluralSeparator:"_",contextSeparator:"_",partialBundledLanguages:!1,saveMissing:!1,updateMissing:!1,saveMissingTo:"fallback",saveMissingPlurals:!0,missingKeyHandler:!1,missingInterpolationHandler:!1,postProcess:!1,postProcessPassResolved:!1,returnNull:!1,returnEmptyString:!0,returnObjects:!1,joinArrays:!1,returnedObjectHandler:!1,parseMissingKeyHandler:!1,appendNamespaceToMissingKey:!1,appendNamespaceToCIMode:!1,overloadTranslationOptionHandler:e=>{let t={};if(typeof e[1]=="object"&&(t=e[1]),K(e[1])&&(t.defaultValue=e[1]),K(e[2])&&(t.tDescription=e[2]),typeof e[2]=="object"||typeof e[3]=="object"){const r=e[3]||e[2];Object.keys(r).forEach(s=>{t[s]=r[s]})}return t},interpolation:{escapeValue:!0,format:e=>e,prefix:"{{",suffix:"}}",formatSeparator:",",unescapePrefix:"-",nestingPrefix:"$t(",nestingSuffix:")",nestingOptionsSeparator:",",maxReplaces:1e3,skipOnVariables:!0},cacheInBuiltFormats:!0}),Od=e=>{var t,r;return K(e.ns)&&(e.ns=[e.ns]),K(e.fallbackLng)&&(e.fallbackLng=[e.fallbackLng]),K(e.fallbackNS)&&(e.fallbackNS=[e.fallbackNS]),((r=(t=e.supportedLngs)==null?void 0:t.indexOf)==null?void 0:r.call(t,"cimode"))<0&&(e.supportedLngs=e.supportedLngs.concat(["cimode"])),typeof e.initImmediate=="boolean"&&(e.initAsync=e.initImmediate),e},oa=()=>{},O1=e=>{Object.getOwnPropertyNames(Object.getPrototypeOf(e)).forEach(r=>{typeof e[r]=="function"&&(e[r]=e[r].bind(e))})};class En extends Ti{constructor(t={},r){if(super(),this.options=Od(t),this.services={},this.logger=Et,this.modules={external:[]},O1(this),r&&!this.isInitialized&&!t.isClone){if(!this.options.initAsync)return this.init(t,r),this;setTimeout(()=>{this.init(t,r)},0)}}init(t={},r){this.isInitializing=!0,typeof t=="function"&&(r=t,t={}),t.defaultNS==null&&t.ns&&(K(t.ns)?t.defaultNS=t.ns:t.ns.indexOf("translation")<0&&(t.defaultNS=t.ns[0]));const s=Ed();this.options={...s,...this.options,...Od(t)},this.options.interpolation={...s.interpolation,...this.options.interpolation},t.keySeparator!==void 0&&(this.options.userDefinedKeySeparator=t.keySeparator),t.nsSeparator!==void 0&&(this.options.userDefinedNsSeparator=t.nsSeparator);const n=u=>u?typeof u=="function"?new u:u:null;if(!this.options.isClone){this.modules.logger?Et.init(n(this.modules.logger),this.options):Et.init(null,this.options);let u;this.modules.formatter?u=this.modules.formatter:u=$1;const d=new kd(this.options);this.store=new Nd(this.options.resources,this.options);const f=this.services;f.logger=Et,f.resourceStore=this.store,f.languageUtils=d,f.pluralResolver=new b1(d,{prepend:this.options.pluralSeparator,simplifyPluralSuffix:this.options.simplifyPluralSuffix}),this.options.interpolation.format&&this.options.interpolation.format!==s.interpolation.format&&this.logger.warn("init: you are still using the legacy format function, please use the new approach: https://www.i18next.com/translation-function/formatting"),u&&(!this.options.interpolation.format||this.options.interpolation.format===s.interpolation.format)&&(f.formatter=n(u),f.formatter.init&&f.formatter.init(f,this.options),this.options.interpolation.format=f.formatter.format.bind(f.formatter)),f.interpolator=new k1(this.options),f.utils={hasLoadedNamespace:this.hasLoadedNamespace.bind(this)},f.backendConnector=new E1(n(this.modules.backend),f.resourceStore,f,this.options),f.backendConnector.on("*",(v,...g)=>{this.emit(v,...g)}),this.modules.languageDetector&&(f.languageDetector=n(this.modules.languageDetector),f.languageDetector.init&&f.languageDetector.init(f,this.options.detection,this.options)),this.modules.i18nFormat&&(f.i18nFormat=n(this.modules.i18nFormat),f.i18nFormat.init&&f.i18nFormat.init(this)),this.translator=new ni(this.services,this.options),this.translator.on("*",(v,...g)=>{this.emit(v,...g)}),this.modules.external.forEach(v=>{v.init&&v.init(this)})}if(this.format=this.options.interpolation.format,r||(r=oa),this.options.fallbackLng&&!this.services.languageDetector&&!this.options.lng){const u=this.services.languageUtils.getFallbackCodes(this.options.fallbackLng);u.length>0&&u[0]!=="dev"&&(this.options.lng=u[0])}!this.services.languageDetector&&!this.options.lng&&this.logger.warn("init: no languageDetector is used and no lng is defined"),["getResource","hasResourceBundle","getResourceBundle","getDataByLanguage"].forEach(u=>{this[u]=(...d)=>this.store[u](...d)}),["addResource","addResources","addResourceBundle","removeResourceBundle"].forEach(u=>{this[u]=(...d)=>(this.store[u](...d),this)});const l=zs(),c=()=>{const u=(d,f)=>{this.isInitializing=!1,this.isInitialized&&!this.initializedStoreOnce&&this.logger.warn("init: i18next is already initialized. You should call init just once!"),this.isInitialized=!0,this.options.isClone||this.logger.log("initialized",this.options),this.emit("initialized",this.options),l.resolve(f),r(d,f)};if(this.languages&&!this.isInitialized)return u(null,this.t.bind(this));this.changeLanguage(this.options.lng,u)};return this.options.resources||!this.options.initAsync?c():setTimeout(c,0),l}loadResources(t,r=oa){var i,o;let s=r;const n=K(t)?t:this.language;if(typeof t=="function"&&(s=t),!this.options.resources||this.options.partialBundledLanguages){if((n==null?void 0:n.toLowerCase())==="cimode"&&(!this.options.preload||this.options.preload.length===0))return s();const l=[],c=u=>{if(!u||u==="cimode")return;this.services.languageUtils.toResolveHierarchy(u).forEach(f=>{f!=="cimode"&&l.indexOf(f)<0&&l.push(f)})};n?c(n):this.services.languageUtils.getFallbackCodes(this.options.fallbackLng).forEach(d=>c(d)),(o=(i=this.options.preload)==null?void 0:i.forEach)==null||o.call(i,u=>c(u)),this.services.backendConnector.load(l,this.options.ns,u=>{!u&&!this.resolvedLanguage&&this.language&&this.setResolvedLanguage(this.language),s(u)})}else s(null)}reloadResources(t,r,s){const n=zs();return typeof t=="function"&&(s=t,t=void 0),typeof r=="function"&&(s=r,r=void 0),t||(t=this.languages),r||(r=this.options.ns),s||(s=oa),this.services.backendConnector.reload(t,r,i=>{n.resolve(),s(i)}),n}use(t){if(!t)throw new Error("You are passing an undefined module! Please check the object you are passing to i18next.use()");if(!t.type)throw new Error("You are passing a wrong module! Please check the object you are passing to i18next.use()");return t.type==="backend"&&(this.modules.backend=t),(t.type==="logger"||t.log&&t.warn&&t.error)&&(this.modules.logger=t),t.type==="languageDetector"&&(this.modules.languageDetector=t),t.type==="i18nFormat"&&(this.modules.i18nFormat=t),t.type==="postProcessor"&&Th.addPostProcessor(t),t.type==="formatter"&&(this.modules.formatter=t),t.type==="3rdParty"&&this.modules.external.push(t),this}setResolvedLanguage(t){if(!(!t||!this.languages)&&!(["cimode","dev"].indexOf(t)>-1)){for(let r=0;r<this.languages.length;r++){const s=this.languages[r];if(!(["cimode","dev"].indexOf(s)>-1)&&this.store.hasLanguageSomeTranslations(s)){this.resolvedLanguage=s;break}}!this.resolvedLanguage&&this.languages.indexOf(t)<0&&this.store.hasLanguageSomeTranslations(t)&&(this.resolvedLanguage=t,this.languages.unshift(t))}}changeLanguage(t,r){this.isLanguageChangingTo=t;const s=zs();this.emit("languageChanging",t);const n=l=>{this.language=l,this.languages=this.services.languageUtils.toResolveHierarchy(l),this.resolvedLanguage=void 0,this.setResolvedLanguage(l)},i=(l,c)=>{c?this.isLanguageChangingTo===t&&(n(c),this.translator.changeLanguage(c),this.isLanguageChangingTo=void 0,this.emit("languageChanged",c),this.logger.log("languageChanged",c)):this.isLanguageChangingTo=void 0,s.resolve((...u)=>this.t(...u)),r&&r(l,(...u)=>this.t(...u))},o=l=>{var d,f;!t&&!l&&this.services.languageDetector&&(l=[]);const c=K(l)?l:l&&l[0],u=this.store.hasLanguageSomeTranslations(c)?c:this.services.languageUtils.getBestMatchFromCodes(K(l)?[l]:l);u&&(this.language||n(u),this.translator.language||this.translator.changeLanguage(u),(f=(d=this.services.languageDetector)==null?void 0:d.cacheUserLanguage)==null||f.call(d,u)),this.loadResources(u,m=>{i(m,u)})};return!t&&this.services.languageDetector&&!this.services.languageDetector.async?o(this.services.languageDetector.detect()):!t&&this.services.languageDetector&&this.services.languageDetector.async?this.services.languageDetector.detect.length===0?this.services.languageDetector.detect().then(o):this.services.languageDetector.detect(o):o(t),s}getFixedT(t,r,s){const n=(i,o,...l)=>{let c;typeof o!="object"?c=this.options.overloadTranslationOptionHandler([i,o].concat(l)):c={...o},c.lng=c.lng||n.lng,c.lngs=c.lngs||n.lngs,c.ns=c.ns||n.ns,c.keyPrefix!==""&&(c.keyPrefix=c.keyPrefix||s||n.keyPrefix);const u=this.options.keySeparator||".";let d;return c.keyPrefix&&Array.isArray(i)?d=i.map(f=>`${c.keyPrefix}${u}${f}`):d=c.keyPrefix?`${c.keyPrefix}${u}${i}`:i,this.t(d,c)};return K(t)?n.lng=t:n.lngs=t,n.ns=r,n.keyPrefix=s,n}t(...t){var r;return(r=this.translator)==null?void 0:r.translate(...t)}exists(...t){var r;return(r=this.translator)==null?void 0:r.exists(...t)}setDefaultNamespace(t){this.options.defaultNS=t}hasLoadedNamespace(t,r={}){if(!this.isInitialized)return this.logger.warn("hasLoadedNamespace: i18next was not initialized",this.languages),!1;if(!this.languages||!this.languages.length)return this.logger.warn("hasLoadedNamespace: i18n.languages were undefined or empty",this.languages),!1;const s=r.lng||this.resolvedLanguage||this.languages[0],n=this.options?this.options.fallbackLng:!1,i=this.languages[this.languages.length-1];if(s.toLowerCase()==="cimode")return!0;const o=(l,c)=>{const u=this.services.backendConnector.state[`${l}|${c}`];return u===-1||u===0||u===2};if(r.precheck){const l=r.precheck(this,o);if(l!==void 0)return l}return!!(this.hasResourceBundle(s,t)||!this.services.backendConnector.backend||this.options.resources&&!this.options.partialBundledLanguages||o(s,t)&&(!n||o(i,t)))}loadNamespaces(t,r){const s=zs();return this.options.ns?(K(t)&&(t=[t]),t.forEach(n=>{this.options.ns.indexOf(n)<0&&this.options.ns.push(n)}),this.loadResources(n=>{s.resolve(),r&&r(n)}),s):(r&&r(),Promise.resolve())}loadLanguages(t,r){const s=zs();K(t)&&(t=[t]);const n=this.options.preload||[],i=t.filter(o=>n.indexOf(o)<0&&this.services.languageUtils.isSupportedCode(o));return i.length?(this.options.preload=n.concat(i),this.loadResources(o=>{s.resolve(),r&&r(o)}),s):(r&&r(),Promise.resolve())}dir(t){var n,i;if(t||(t=this.resolvedLanguage||(((n=this.languages)==null?void 0:n.length)>0?this.languages[0]:this.language)),!t)return"rtl";try{const o=new Intl.Locale(t);if(o&&o.getTextInfo){const l=o.getTextInfo();if(l&&l.direction)return l.direction}}catch{}const r=["ar","shu","sqr","ssh","xaa","yhd","yud","aao","abh","abv","acm","acq","acw","acx","acy","adf","ads","aeb","aec","afb","ajp","apc","apd","arb","arq","ars","ary","arz","auz","avl","ayh","ayl","ayn","ayp","bbz","pga","he","iw","ps","pbt","pbu","pst","prp","prd","ug","ur","ydd","yds","yih","ji","yi","hbo","men","xmn","fa","jpr","peo","pes","prs","dv","sam","ckb"],s=((i=this.services)==null?void 0:i.languageUtils)||new kd(Ed());return t.toLowerCase().indexOf("-latn")>1?"ltr":r.indexOf(s.getLanguagePartFromCode(t))>-1||t.toLowerCase().indexOf("-arab")>1?"rtl":"ltr"}static createInstance(t={},r){return new En(t,r)}cloneInstance(t={},r=oa){const s=t.forkResourceStore;s&&delete t.forkResourceStore;const n={...this.options,...t,isClone:!0},i=new En(n);if((t.debug!==void 0||t.prefix!==void 0)&&(i.logger=i.logger.clone(t)),["store","services","language"].forEach(l=>{i[l]=this[l]}),i.services={...this.services},i.services.utils={hasLoadedNamespace:i.hasLoadedNamespace.bind(i)},s){const l=Object.keys(this.store.data).reduce((c,u)=>(c[u]={...this.store.data[u]},c[u]=Object.keys(c[u]).reduce((d,f)=>(d[f]={...c[u][f]},d),c[u]),c),{});i.store=new Nd(l,n),i.services.resourceStore=i.store}return i.translator=new ni(i.services,n),i.translator.on("*",(l,...c)=>{i.emit(l,...c)}),i.init(n,r),i.translator.options=n,i.translator.backendConnector.services.utils={hasLoadedNamespace:i.hasLoadedNamespace.bind(i)},i}toJSON(){return{options:this.options,store:this.store,language:this.language,languages:this.languages,resolvedLanguage:this.resolvedLanguage}}}const Ie=En.createInstance();Ie.createInstance=En.createInstance;Ie.createInstance;Ie.dir;Ie.init;Ie.loadResources;Ie.reloadResources;Ie.use;Ie.changeLanguage;Ie.getFixedT;Ie.t;Ie.exists;Ie.setDefaultNamespace;Ie.hasLoadedNamespace;Ie.loadNamespaces;Ie.loadLanguages;const{slice:A1,forEach:D1}=[];function L1(e){return D1.call(A1.call(arguments,1),t=>{if(t)for(const r in t)e[r]===void 0&&(e[r]=t[r])}),e}function T1(e){return typeof e!="string"?!1:[/<\s*script.*?>/i,/<\s*\/\s*script\s*>/i,/<\s*img.*?on\w+\s*=/i,/<\s*\w+\s*on\w+\s*=.*?>/i,/javascript\s*:/i,/vbscript\s*:/i,/expression\s*\(/i,/eval\s*\(/i,/alert\s*\(/i,/document\.cookie/i,/document\.write\s*\(/i,/window\.location/i,/innerHTML/i].some(r=>r.test(e))}const Ad=/^[\u0009\u0020-\u007e\u0080-\u00ff]+$/,R1=function(e,t){const s=arguments.length>2&&arguments[2]!==void 0?arguments[2]:{path:"/"},n=encodeURIComponent(t);let i=`${e}=${n}`;if(s.maxAge>0){const o=s.maxAge-0;if(Number.isNaN(o))throw new Error("maxAge should be a Number");i+=`; Max-Age=${Math.floor(o)}`}if(s.domain){if(!Ad.test(s.domain))throw new TypeError("option domain is invalid");i+=`; Domain=${s.domain}`}if(s.path){if(!Ad.test(s.path))throw new TypeError("option path is invalid");i+=`; Path=${s.path}`}if(s.expires){if(typeof s.expires.toUTCString!="function")throw new TypeError("option expires is invalid");i+=`; Expires=${s.expires.toUTCString()}`}if(s.httpOnly&&(i+="; HttpOnly"),s.secure&&(i+="; Secure"),s.sameSite)switch(typeof s.sameSite=="string"?s.sameSite.toLowerCase():s.sameSite){case!0:i+="; SameSite=Strict";break;case"lax":i+="; SameSite=Lax";break;case"strict":i+="; SameSite=Strict";break;case"none":i+="; SameSite=None";break;default:throw new TypeError("option sameSite is invalid")}return s.partitioned&&(i+="; Partitioned"),i},Dd={create(e,t,r,s){let n=arguments.length>4&&arguments[4]!==void 0?arguments[4]:{path:"/",sameSite:"strict"};r&&(n.expires=new Date,n.expires.setTime(n.expires.getTime()+r*60*1e3)),s&&(n.domain=s),document.cookie=R1(e,t,n)},read(e){const t=`${e}=`,r=document.cookie.split(";");for(let s=0;s<r.length;s++){let n=r[s];for(;n.charAt(0)===" ";)n=n.substring(1,n.length);if(n.indexOf(t)===0)return n.substring(t.length,n.length)}return null},remove(e,t){this.create(e,"",-1,t)}};var _1={name:"cookie",lookup(e){let{lookupCookie:t}=e;if(t&&typeof document<"u")return Dd.read(t)||void 0},cacheUserLanguage(e,t){let{lookupCookie:r,cookieMinutes:s,cookieDomain:n,cookieOptions:i}=t;r&&typeof document<"u"&&Dd.create(r,e,s,n,i)}},F1={name:"querystring",lookup(e){var s;let{lookupQuerystring:t}=e,r;if(typeof window<"u"){let{search:n}=window.location;!window.location.search&&((s=window.location.hash)==null?void 0:s.indexOf("?"))>-1&&(n=window.location.hash.substring(window.location.hash.indexOf("?")));const o=n.substring(1).split("&");for(let l=0;l<o.length;l++){const c=o[l].indexOf("=");c>0&&o[l].substring(0,c)===t&&(r=o[l].substring(c+1))}}return r}},I1={name:"hash",lookup(e){var n;let{lookupHash:t,lookupFromHashIndex:r}=e,s;if(typeof window<"u"){const{hash:i}=window.location;if(i&&i.length>2){const o=i.substring(1);if(t){const l=o.split("&");for(let c=0;c<l.length;c++){const u=l[c].indexOf("=");u>0&&l[c].substring(0,u)===t&&(s=l[c].substring(u+1))}}if(s)return s;if(!s&&r>-1){const l=i.match(/\/([a-zA-Z-]*)/g);return Array.isArray(l)?(n=l[typeof r=="number"?r:0])==null?void 0:n.replace("/",""):void 0}}}return s}};let Kr=null;const Ld=()=>{if(Kr!==null)return Kr;try{if(Kr=typeof window<"u"&&window.localStorage!==null,!Kr)return!1;const e="i18next.translate.boo";window.localStorage.setItem(e,"foo"),window.localStorage.removeItem(e)}catch{Kr=!1}return Kr};var M1={name:"localStorage",lookup(e){let{lookupLocalStorage:t}=e;if(t&&Ld())return window.localStorage.getItem(t)||void 0},cacheUserLanguage(e,t){let{lookupLocalStorage:r}=t;r&&Ld()&&window.localStorage.setItem(r,e)}};let Qr=null;const Td=()=>{if(Qr!==null)return Qr;try{if(Qr=typeof window<"u"&&window.sessionStorage!==null,!Qr)return!1;const e="i18next.translate.boo";window.sessionStorage.setItem(e,"foo"),window.sessionStorage.removeItem(e)}catch{Qr=!1}return Qr};var U1={name:"sessionStorage",lookup(e){let{lookupSessionStorage:t}=e;if(t&&Td())return window.sessionStorage.getItem(t)||void 0},cacheUserLanguage(e,t){let{lookupSessionStorage:r}=t;r&&Td()&&window.sessionStorage.setItem(r,e)}},z1={name:"navigator",lookup(e){const t=[];if(typeof navigator<"u"){const{languages:r,userLanguage:s,language:n}=navigator;if(r)for(let i=0;i<r.length;i++)t.push(r[i]);s&&t.push(s),n&&t.push(n)}return t.length>0?t:void 0}},V1={name:"htmlTag",lookup(e){let{htmlTag:t}=e,r;const s=t||(typeof document<"u"?document.documentElement:null);return s&&typeof s.getAttribute=="function"&&(r=s.getAttribute("lang")),r}},B1={name:"path",lookup(e){var n;let{lookupFromPathIndex:t}=e;if(typeof window>"u")return;const r=window.location.pathname.match(/\/([a-zA-Z-]*)/g);return Array.isArray(r)?(n=r[typeof t=="number"?t:0])==null?void 0:n.replace("/",""):void 0}},H1={name:"subdomain",lookup(e){var n,i;let{lookupFromSubdomainIndex:t}=e;const r=typeof t=="number"?t+1:1,s=typeof window<"u"&&((i=(n=window.location)==null?void 0:n.hostname)==null?void 0:i.match(/^(\w{2,5})\.(([a-z0-9-]{1,63}\.[a-z]{2,6})|localhost)/i));if(s)return s[r]}};let Rh=!1;try{document.cookie,Rh=!0}catch{}const _h=["querystring","cookie","localStorage","sessionStorage","navigator","htmlTag"];Rh||_h.splice(1,1);const W1=()=>({order:_h,lookupQuerystring:"lng",lookupCookie:"i18next",lookupLocalStorage:"i18nextLng",lookupSessionStorage:"i18nextLng",caches:["localStorage"],excludeCacheFor:["cimode"],convertDetectedLanguage:e=>e});class Fh{constructor(t){let r=arguments.length>1&&arguments[1]!==void 0?arguments[1]:{};this.type="languageDetector",this.detectors={},this.init(t,r)}init(){let t=arguments.length>0&&arguments[0]!==void 0?arguments[0]:{languageUtils:{}},r=arguments.length>1&&arguments[1]!==void 0?arguments[1]:{},s=arguments.length>2&&arguments[2]!==void 0?arguments[2]:{};this.services=t,this.options=L1(r,this.options||{},W1()),typeof this.options.convertDetectedLanguage=="string"&&this.options.convertDetectedLanguage.indexOf("15897")>-1&&(this.options.convertDetectedLanguage=n=>n.replace("-","_")),this.options.lookupFromUrlIndex&&(this.options.lookupFromPathIndex=this.options.lookupFromUrlIndex),this.i18nOptions=s,this.addDetector(_1),this.addDetector(F1),this.addDetector(M1),this.addDetector(U1),this.addDetector(z1),this.addDetector(V1),this.addDetector(B1),this.addDetector(H1),this.addDetector(I1)}addDetector(t){return this.detectors[t.name]=t,this}detect(){let t=arguments.length>0&&arguments[0]!==void 0?arguments[0]:this.options.order,r=[];return t.forEach(s=>{if(this.detectors[s]){let n=this.detectors[s].lookup(this.options);n&&typeof n=="string"&&(n=[n]),n&&(r=r.concat(n))}}),r=r.filter(s=>s!=null&&!T1(s)).map(s=>this.options.convertDetectedLanguage(s)),this.services&&this.services.languageUtils&&this.services.languageUtils.getBestMatchFromCodes?r:r.length>0?r[0]:null}cacheUserLanguage(t){let r=arguments.length>1&&arguments[1]!==void 0?arguments[1]:this.options.caches;r&&(this.options.excludeCacheFor&&this.options.excludeCacheFor.indexOf(t)>-1||r.forEach(s=>{this.detectors[s]&&this.detectors[s].cacheUserLanguage(t,this.options)}))}}Fh.type="languageDetector";const q1={loading:"Loading...",error:"Error occurred",success:"Success",cancel:"Cancel",confirm:"Confirm",save:"Save",edit:"Edit",delete:"Delete",search:"Search",filter:"Filter",sort:"Sort",clear:"Clear",apply:"Apply",close:"Close",back:"Back",next:"Next",previous:"Previous",home:"Home",about:"About",contact:"Contact",privacy:"Privacy",terms:"Terms & Conditions",none:"None"},K1={home:"Home",products:"Products",categories:"Categories",cart:"Cart",profile:"Profile",login:"Login",register:"Register",logout:"Logout",dashboard:"Dashboard",orders:"Orders",wishlist:"Wishlist",back:"Back",backToProducts:"Back to Products"},Q1={searchPlaceholder:"Search for products...",cartItems:"items in cart",cartEmpty:"Cart is empty",viewCart:"View Cart",checkout:"Checkout",userMenu:"User Menu",language:"Language",currency:"Currency"},Y1={companyName:"Clothing Store",description:"Your destination for modern fashion and high quality",quickLinks:"Quick Links",customerService:"Customer Service",followUs:"Follow Us",newsletter:"Newsletter",newsletterText:"Subscribe to get the latest offers and new products",emailPlaceholder:"Enter your email",subscribe:"Subscribe",allRightsReserved:"All rights reserved",paymentMethods:"Accepted Payment Methods"},G1={heroTitle:"Premium Fashion",heroSubtitle:"For Every Occasion",heroDescription:"Discover our curated collection of high-quality clothing designed for the modern lifestyle. Style, comfort, and elegance in every piece.",shopNow:"Shop Now",viewFeatured:"View Featured",featuredProducts:"Featured Products",featuredDescription:"Handpicked items from our latest collection. These pieces represent the perfect blend of style and quality.",viewAllProducts:"View All Products",shopByCategory:"Shop by Category",categoryDescription:"Find exactly what you're looking for",findWhatYouLooking:"Find exactly what you're looking for",categories:{shirts:"Shirts",pants:"Pants",dresses:"Dresses"},features:{freeShipping:"Free Shipping",freeShippingDesc:"Free shipping on orders over 500 SAR",securePayment:"Secure Payment",securePaymentDesc:"100% secure payment processing",easyReturns:"Easy Returns",easyReturnsDesc:"30-day return policy",support:"24/7 Support",supportDesc:"Customer support available anytime"}},J1={title:"Products",allProducts:"All Products",featuredProducts:"Featured Products",searchResults:"Search results for",productsFound:"products found",noProductsFound:"No products found",sortBy:"Sort by",sortOptions:{name:"Name",priceLow:"Price: Low to High",priceHigh:"Price: High to Low",rating:"Highest Rated",newest:"Newest First"},viewModes:{grid:"Grid",list:"List"},addToCart:"Add to Cart",addToWishlist:"Add to Wishlist",quickView:"Quick View",outOfStock:"Out of Stock",inStock:"In Stock",sale:"Sale",new:"New",rating:"Rating",reviews:"reviews",viewDetails:"View Details",more:"more",notFound:"Product not found",selectVariant:"Please select size and color",selectSize:"Select Size",quantity:"Quantity",off:"OFF",details:"Details",category:"Category",sku:"SKU",tags:"Tags",product:"Product",variant:"Variant",price:"Price"},X1={description:"Description",specifications:"Specifications",reviews:"Reviews",shipping:"Shipping",returns:"Returns",size:"Size",color:"Color",quantity:"Quantity",addToCart:"Add to Cart",buyNow:"Buy Now",addToWishlist:"Add to Wishlist",shareProduct:"Share Product",relatedProducts:"Related Products",customerReviews:"Customer Reviews",writeReview:"Write Review",helpful:"Helpful",notHelpful:"Not Helpful"},Z1={title:"Shopping Cart",emptyCart:"Your cart is empty",continueShopping:"Continue Shopping",item:"item",items:"items",subtotal:"Subtotal",shipping:"Shipping",tax:"Tax",total:"Total",proceedToCheckout:"Proceed to Checkout",updateCart:"Update Cart",removeItem:"Remove Item",quantity:"Quantity",price:"Price",freeShipping:"Free Shipping",shippingCalculated:"Shipping calculated at checkout",addToCart:"Add to Cart",addedToCart:"Product added to cart"},ew={chooseOption:"Choose Purchase Option",buyNow:"Buy Now"},tw={inquiry:"WhatsApp Inquiry",pleaseProvideInfo:"Please provide additional information if needed"},rw={login:"Login",register:"Register",logout:"Logout",email:"Email",password:"Password",confirmPassword:"Confirm Password",firstName:"First Name",lastName:"Last Name",phone:"Phone Number",rememberMe:"Remember me",forgotPassword:"Forgot your password?",createAccount:"Create account",haveAccount:"Already have an account?",noAccount:"Don't have an account?",signInToAccount:"Sign in to your account",createNewAccount:"Create your account",agreeToTerms:"I agree to the Terms and Conditions and Privacy Policy",loginSuccessful:"Login successful!",registerSuccessful:"Registration successful!",invalidCredentials:"Invalid credentials",emailRequired:"Email is required",passwordRequired:"Password is required",passwordMinLength:"Password must be at least 6 characters",passwordsNotMatch:"Passwords do not match",invalidEmail:"Invalid email address"},sw={title:"Profile",personalInfo:"Personal Information",orderHistory:"Order History",addresses:"Addresses",paymentMethods:"Payment Methods",preferences:"Preferences",security:"Security",notifications:"Notifications",editProfile:"Edit Profile",changePassword:"Change Password",currentPassword:"Current Password",newPassword:"New Password",confirmNewPassword:"Confirm New Password"},nw={dashboard:"Dashboard",adminPanel:"Admin Panel",dashboardOverview:"Dashboard Overview",dashboardDescription:"Welcome to your store dashboard",products:"Products",orders:"Orders",customers:"Customers",analytics:"Analytics",inventory:"Inventory",settings:"Settings",totalRevenue:"Total Revenue",totalOrders:"Total Orders",totalCustomers:"Total Customers",totalProducts:"Total Products",recentOrders:"Recent Orders",lowStockAlerts:"Low Stock Alerts",threshold:"Threshold",inStock:"In Stock",productManagement:"Product Management",orderManagement:"Order Management",customerManagement:"Customer Management",salesAnalytics:"Sales Analytics",inventoryManagement:"Inventory Management",storeSettings:"Store Settings",addProduct:"Add Product",editProduct:"Edit Product",deleteProduct:"Delete Product",productName:"Product Name",productDescription:"Product Description",productPrice:"Product Price",productCategory:"Product Category",productImages:"Product Images",productStock:"Product Stock",orderStatus:"Order Status",orderDate:"Order Date",customerName:"Customer Name",orderTotal:"Order Total",orderNumber:"Order Number",all:"All",pending:"Pending",processing:"Processing",shipped:"Shipped",delivered:"Delivered",cancelled:"Cancelled",manageYourProducts:"Manage your products",manageCustomerOrders:"Manage customer orders",manageCustomerAccounts:"Manage customer accounts",trackYourSalesPerformance:"Track your sales performance",manageProductInventory:"Manage product inventory",configureYourStore:"Configure your store",searchProducts:"Search products",searchOrders:"Search orders",searchCustomers:"Search customers",noProductsFound:"No products found",noOrdersFound:"No orders found",noCustomersFound:"No customers found",confirmDeleteProduct:"Are you sure you want to delete this product?",productFormComingSoon:"Product form coming soon",productViewComingSoon:"Product view coming soon",orderViewComingSoon:"Order view coming soon",customerViewComingSoon:"Customer view coming soon",chartComingSoon:"Charts coming soon",viewProduct:"View Product",viewOrder:"View Order",viewCustomer:"View Customer",product:"Product",customer:"Customer",contact:"Contact",totalSpent:"Total Spent",status:"Status",active:"Active",inactive:"Inactive",activeCustomers:"Active Customers",averageSpent:"Average Spent",newCustomers:"New Customers",avgOrderValue:"Avg Order Value",topProducts:"Top Products",sales:"sales",salesByCategory:"Sales by Category",ofTotalSales:"of total sales",salesChart:"Sales Chart",allProducts:"All Products",lowStockItems:"Low Stock Items",outOfStock:"Out of Stock",inventoryValue:"Inventory Value",stockStatus:"Stock Status",goodStock:"Good Stock",sku:"SKU",currentStock:"Current Stock",unitPrice:"Unit Price",totalValue:"Total Value",updateStock:"Update Stock",restock:"Restock",generalSettings:"General Settings",paymentMethods:"Payment Methods",shippingOptions:"Shipping Options",notifications:"Notifications",generalInformation:"General Information",storeNameArabic:"Store Name (Arabic)",storeNameEnglish:"Store Name (English)",email:"Email",phone:"Phone",descriptionArabic:"Description (Arabic)",descriptionEnglish:"Description (English)",paymentConfiguration:"Payment Configuration",creditCardPayments:"Credit Card Payments",acceptCreditCards:"Accept credit cards",cashOnDelivery:"Cash on Delivery",allowCashPayment:"Allow cash payment",taxRate:"Tax Rate",shippingConfiguration:"Shipping Configuration",freeShippingThreshold:"Free Shipping Threshold",localShippingCost:"Local Shipping Cost",processingTime:"Processing Time",shippingTime:"Shipping Time",days:"days",notificationSettings:"Notification Settings",orderConfirmationEmails:"Order Confirmation Emails",sendOrderConfirmation:"Send order confirmation",notifyLowStock:"Notify low stock",settingsSaved:"Settings saved successfully",stock:"Stock"},aw={sar:"Saudi Riyal",symbol:"SAR"},iw={credentials:"Demo Credentials:",admin:"Admin",customer:"Customer"},ow={thisWeek:"This Week",thisMonth:"This Month",thisQuarter:"This Quarter",thisYear:"This Year"},lw={profile:"Profile",orders:"My Orders",wishlist:"Wishlist",addresses:"My Addresses",settings:"Settings",myProfile:"My Profile",myOrders:"My Orders",myWishlist:"My Wishlist",manageYourPersonalInfo:"Manage your personal information",trackYourOrders:"Track your orders and review purchase history",savedItems:"Saved items",personalInformation:"Personal Information",firstName:"First Name",lastName:"Last Name",email:"Email",phone:"Phone",dateOfBirth:"Date of Birth",gender:"Gender",male:"Male",female:"Female",selectGender:"Select Gender",notProvided:"Not Provided",profileUpdated:"Profile updated successfully",accountSummary:"Account Summary",totalOrders:"Total Orders",totalSpent:"Total Spent",wishlistItems:"Wishlist Items",memberSince:"Member Since",recentActivity:"Recent Activity",orderItems:"Order Items",quantity:"Quantity",orderSummary:"Order Summary",noOrdersFound:"No orders found",orderDetails:"Order Details",orderDetailsComingSoon:"Order details coming soon",searchOrders:"Search orders...",items:"items",addAllToCart:"Add All to Cart",clearWishlist:"Clear Wishlist",removeFromWishlist:"Remove from Wishlist",emptyWishlist:"Your wishlist is empty",startAddingItems:"Start adding items you love",browseProducts:"Browse Products",addedOn:"Added on",allItemsAddedToCart:"All items added to cart",confirmClearWishlist:"Are you sure you want to clear your wishlist?"},cw={common:q1,navigation:K1,header:Q1,footer:Y1,home:G1,products:J1,productDetail:X1,cart:Z1,purchase:ew,whatsapp:tw,auth:rw,profile:sw,admin:nw,currency:aw,demo:iw,analytics:ow,customer:lw},uw={loading:"جاري التحميل...",error:"حدث خطأ",success:"تم بنجاح",cancel:"إلغاء",confirm:"تأكيد",save:"حفظ",edit:"تعديل",delete:"حذف",search:"بحث",filter:"تصفية",sort:"ترتيب",clear:"مسح",apply:"تطبيق",close:"إغلاق",back:"رجوع",next:"التالي",previous:"السابق",home:"الرئيسية",about:"حول",contact:"اتصل بنا",privacy:"الخصوصية",terms:"الشروط والأحكام",none:"لا يوجد"},dw={home:"الرئيسية",products:"المنتجات",categories:"الأقسام",cart:"السلة",profile:"الملف الشخصي",login:"تسجيل الدخول",register:"إنشاء حساب",logout:"تسجيل الخروج",dashboard:"لوحة التحكم",orders:"الطلبات",wishlist:"المفضلة",back:"رجوع",backToProducts:"العودة للمنتجات"},fw={searchPlaceholder:"ابحث عن المنتجات...",cartItems:"عنصر في السلة",cartEmpty:"السلة فارغة",viewCart:"عرض السلة",checkout:"الدفع",userMenu:"قائمة المستخدم",language:"اللغة",currency:"العملة"},mw={companyName:"متجر الملابس",description:"وجهتك الأولى للأزياء العصرية والجودة العالية",quickLinks:"روابط سريعة",customerService:"خدمة العملاء",followUs:"تابعنا",newsletter:"النشرة الإخبارية",newsletterText:"اشترك للحصول على آخر العروض والمنتجات الجديدة",emailPlaceholder:"أدخل بريدك الإلكتروني",subscribe:"اشتراك",allRightsReserved:"جميع الحقوق محفوظة",paymentMethods:"طرق الدفع المقبولة",sizeGuide:"دليل المقاسات",faq:"الأسئلة الشائعة",shippingInfo:"معلومات الشحن",returnsExchanges:"الإرجاع والاستبدال",contactInfo:"معلومات التواصل",address:"الرياض، المملكة العربية السعودية",phone:"+966 11 123 4567",email:"<EMAIL>"},hw={heroTitle:"الأزياء المميزة",heroSubtitle:"لكل مناسبة",heroDescription:"اكتشف مجموعتنا المختارة من الملابس عالية الجودة المصممة للحياة العصرية. الأناقة والراحة والجمال في كل قطعة.",shopNow:"تسوق الآن",viewFeatured:"عرض المميز",featuredProducts:"المنتجات المميزة",featuredDescription:"قطع مختارة بعناية من أحدث مجموعاتنا. هذه القطع تمثل المزيج المثالي بين الأناقة والجودة.",viewAllProducts:"عرض جميع المنتجات",shopByCategory:"تسوق حسب القسم",categoryDescription:"اعثر على ما تبحث عنه بالضبط",findWhatYouLooking:"اعثر على ما تبحث عنه بالضبط",categories:{shirts:"القمصان",pants:"البناطيل",dresses:"الفساتين"},features:{freeShipping:"شحن مجاني",freeShippingDesc:"شحن مجاني للطلبات أكثر من 500 ريال",securePayment:"دفع آمن",securePaymentDesc:"معالجة دفع آمنة 100%",easyReturns:"إرجاع سهل",easyReturnsDesc:"سياسة إرجاع لمدة 30 يوم",support:"دعم 24/7",supportDesc:"دعم العملاء متاح في أي وقت"}},pw={title:"المنتجات",allProducts:"جميع المنتجات",featuredProducts:"المنتجات المميزة",searchResults:"نتائج البحث عن",productsFound:"منتج موجود",noProductsFound:"لم يتم العثور على منتجات",sortBy:"ترتيب حسب",sortOptions:{name:"الاسم",priceLow:"السعر: من الأقل للأعلى",priceHigh:"السعر: من الأعلى للأقل",rating:"الأعلى تقييماً",newest:"الأحدث أولاً"},viewModes:{grid:"شبكة",list:"قائمة"},addToCart:"أضف للسلة",addToWishlist:"أضف للمفضلة",quickView:"عرض سريع",outOfStock:"نفد من المخزن",inStock:"متوفر",sale:"تخفيض",new:"جديد",rating:"التقييم",reviews:"مراجعة",viewDetails:"عرض التفاصيل",more:"أكثر",notFound:"المنتج غير موجود",selectVariant:"يرجى اختيار المقاس واللون",selectSize:"اختر المقاس",quantity:"الكمية",off:"خصم",details:"التفاصيل",category:"الفئة",sku:"رمز المنتج",tags:"العلامات",product:"المنتج",variant:"النوع",price:"السعر"},gw={description:"الوصف",specifications:"المواصفات",reviews:"المراجعات",shipping:"الشحن",returns:"الإرجاع",size:"المقاس",color:"اللون",quantity:"الكمية",addToCart:"أضف للسلة",buyNow:"اشتري الآن",addToWishlist:"أضف للمفضلة",shareProduct:"شارك المنتج",relatedProducts:"منتجات ذات صلة",customerReviews:"مراجعات العملاء",writeReview:"اكتب مراجعة",helpful:"مفيد",notHelpful:"غير مفيد"},xw={title:"سلة التسوق",emptyCart:"سلة التسوق فارغة",continueShopping:"متابعة التسوق",item:"عنصر",items:"عناصر",subtotal:"المجموع الفرعي",shipping:"الشحن",tax:"الضريبة",total:"المجموع الكلي",proceedToCheckout:"متابعة للدفع",updateCart:"تحديث السلة",removeItem:"إزالة العنصر",quantity:"الكمية",price:"السعر",freeShipping:"شحن مجاني",shippingCalculated:"يتم حساب الشحن عند الدفع",addToCart:"أضف للسلة",addedToCart:"تم إضافة المنتج للسلة"},yw={chooseOption:"اختر طريقة الشراء",buyNow:"شراء الآن"},vw={inquiry:"استفسار واتساب",pleaseProvideInfo:"يرجى تقديم معلومات إضافية إذا لزم الأمر"},ww={login:"تسجيل الدخول",register:"إنشاء حساب",logout:"تسجيل الخروج",email:"البريد الإلكتروني",password:"كلمة المرور",confirmPassword:"تأكيد كلمة المرور",firstName:"الاسم الأول",lastName:"الاسم الأخير",phone:"رقم الهاتف",rememberMe:"تذكرني",forgotPassword:"نسيت كلمة المرور؟",createAccount:"إنشاء حساب",haveAccount:"لديك حساب؟",noAccount:"ليس لديك حساب؟",signInToAccount:"تسجيل الدخول إلى حسابك",createNewAccount:"إنشاء حساب جديد",agreeToTerms:"أوافق على الشروط والأحكام وسياسة الخصوصية",loginSuccessful:"تم تسجيل الدخول بنجاح!",registerSuccessful:"تم إنشاء الحساب بنجاح!",invalidCredentials:"بيانات الدخول غير صحيحة",emailRequired:"البريد الإلكتروني مطلوب",passwordRequired:"كلمة المرور مطلوبة",passwordMinLength:"كلمة المرور يجب أن تكون 6 أحرف على الأقل",passwordsNotMatch:"كلمات المرور غير متطابقة",invalidEmail:"البريد الإلكتروني غير صحيح"},Nw={title:"الملف الشخصي",personalInfo:"المعلومات الشخصية",orderHistory:"تاريخ الطلبات",addresses:"العناوين",paymentMethods:"طرق الدفع",preferences:"التفضيلات",security:"الأمان",notifications:"الإشعارات",editProfile:"تعديل الملف الشخصي",changePassword:"تغيير كلمة المرور",currentPassword:"كلمة المرور الحالية",newPassword:"كلمة المرور الجديدة",confirmNewPassword:"تأكيد كلمة المرور الجديدة"},jw={dashboard:"لوحة التحكم",adminPanel:"لوحة الإدارة",dashboardOverview:"نظرة عامة على لوحة التحكم",dashboardDescription:"مرحباً بك في لوحة تحكم المتجر",products:"المنتجات",orders:"الطلبات",customers:"العملاء",analytics:"التحليلات",inventory:"المخزون",settings:"الإعدادات",totalRevenue:"إجمالي الإيرادات",totalOrders:"إجمالي الطلبات",totalCustomers:"إجمالي العملاء",totalProducts:"إجمالي المنتجات",recentOrders:"الطلبات الأخيرة",lowStockAlerts:"تنبيهات المخزون المنخفض",threshold:"الحد الأدنى",inStock:"متوفر",productManagement:"إدارة المنتجات",orderManagement:"إدارة الطلبات",customerManagement:"إدارة العملاء",salesAnalytics:"تحليلات المبيعات",inventoryManagement:"إدارة المخزون",storeSettings:"إعدادات المتجر",addProduct:"إضافة منتج",editProduct:"تعديل منتج",deleteProduct:"حذف منتج",productName:"اسم المنتج",productDescription:"وصف المنتج",productPrice:"سعر المنتج",productCategory:"قسم المنتج",productImages:"صور المنتج",productStock:"مخزون المنتج",orderStatus:"حالة الطلب",orderDate:"تاريخ الطلب",customerName:"اسم العميل",orderTotal:"إجمالي الطلب",orderNumber:"رقم الطلب",all:"الكل",pending:"في الانتظار",processing:"قيد المعالجة",shipped:"تم الشحن",delivered:"تم التسليم",cancelled:"ملغي",manageYourProducts:"إدارة منتجاتك",manageCustomerOrders:"إدارة طلبات العملاء",manageCustomerAccounts:"إدارة حسابات العملاء",trackYourSalesPerformance:"تتبع أداء مبيعاتك",confirmDeleteProduct:"هل أنت متأكد من حذف هذا المنتج؟",productDeletedSuccessfully:"تم حذف المنتج بنجاح",errorDeletingProduct:"خطأ في حذف المنتج",errorLoadingProducts:"خطأ في تحميل المنتجات",searchProducts:"البحث في المنتجات",noProductsFound:"لم يتم العثور على منتجات",lowStock:"مخزون منخفض",outOfStock:"نفد المخزون",totalValue:"القيمة الإجمالية",stock:"المخزون",orderStatusUpdated:"تم تحديث حالة الطلب بنجاح",errorUpdatingOrder:"خطأ في تحديث الطلب",errorLoadingOrders:"خطأ في تحميل الطلبات",errorLoadingCustomers:"خطأ في تحميل العملاء",newCustomersThisMonth:"عملاء جدد هذا الشهر",averageSpent:"متوسط الإنفاق",thisMonthOrders:"طلبات هذا الشهر",pendingOrders:"الطلبات المعلقة",manageProductInventory:"إدارة مخزون المنتجات",configureYourStore:"تكوين متجرك",searchOrders:"البحث في الطلبات",searchCustomers:"البحث في العملاء",noOrdersFound:"لم يتم العثور على طلبات",noCustomersFound:"لم يتم العثور على عملاء",productFormComingSoon:"نموذج إضافة المنتج قريباً",productViewComingSoon:"عرض تفاصيل المنتج قريباً",orderViewComingSoon:"عرض تفاصيل الطلب قريباً",customerViewComingSoon:"عرض تفاصيل العميل قريباً",chartComingSoon:"الرسوم البيانية قريباً",viewProduct:"عرض المنتج",viewOrder:"عرض الطلب",viewCustomer:"عرض العميل",product:"المنتج",customer:"العميل",contact:"التواصل",totalSpent:"إجمالي الإنفاق",status:"الحالة",active:"نشط",inactive:"غير نشط",activeCustomers:"العملاء النشطون",newCustomers:"عملاء جدد",avgOrderValue:"متوسط قيمة الطلب",topProducts:"أفضل المنتجات",sales:"مبيعات",salesByCategory:"المبيعات حسب الفئة",ofTotalSales:"من إجمالي المبيعات",salesChart:"مخطط المبيعات",allProducts:"جميع المنتجات",lowStockItems:"عناصر مخزون منخفض",inventoryValue:"قيمة المخزون",stockStatus:"حالة المخزون",goodStock:"مخزون جيد",sku:"رمز المنتج",currentStock:"المخزون الحالي",unitPrice:"سعر الوحدة",updateStock:"تحديث المخزون",restock:"إعادة التخزين",generalSettings:"الإعدادات العامة",paymentMethods:"طرق الدفع",shippingOptions:"خيارات الشحن",notifications:"الإشعارات",generalInformation:"المعلومات العامة",storeNameArabic:"اسم المتجر بالعربية",storeNameEnglish:"اسم المتجر بالإنجليزية",email:"البريد الإلكتروني",phone:"الهاتف",descriptionArabic:"الوصف بالعربية",descriptionEnglish:"الوصف بالإنجليزية",paymentConfiguration:"تكوين الدفع",creditCardPayments:"مدفوعات بطاقة الائتمان",acceptCreditCards:"قبول بطاقات الائتمان",cashOnDelivery:"الدفع عند الاستلام",allowCashPayment:"السماح بالدفع النقدي",taxRate:"معدل الضريبة",shippingConfiguration:"تكوين الشحن",freeShippingThreshold:"حد الشحن المجاني",localShippingCost:"تكلفة الشحن المحلي",processingTime:"وقت المعالجة",shippingTime:"وقت الشحن",days:"أيام",notificationSettings:"إعدادات الإشعارات",orderConfirmationEmails:"رسائل تأكيد الطلب",sendOrderConfirmation:"إرسال تأكيد الطلب",notifyLowStock:"إشعار المخزون المنخفض",settingsSaved:"تم حفظ الإعدادات بنجاح"},bw={sar:"ريال سعودي",symbol:"ر.س"},kw={credentials:"بيانات تجريبية:",admin:"مدير",customer:"عميل"},Sw={thisWeek:"هذا الأسبوع",thisMonth:"هذا الشهر",thisQuarter:"هذا الربع",thisYear:"هذا العام"},Cw={profile:"الملف الشخصي",orders:"طلباتي",wishlist:"المفضلة",addresses:"عناويني",settings:"الإعدادات",myProfile:"ملفي الشخصي",myOrders:"طلباتي",myWishlist:"قائمة المفضلة",manageYourPersonalInfo:"إدارة معلوماتك الشخصية",trackYourOrders:"تتبع طلباتك ومراجعة تاريخ الشراء",savedItems:"العناصر المحفوظة",personalInformation:"المعلومات الشخصية",firstName:"الاسم الأول",lastName:"الاسم الأخير",email:"البريد الإلكتروني",phone:"رقم الهاتف",dateOfBirth:"تاريخ الميلاد",gender:"الجنس",male:"ذكر",female:"أنثى",selectGender:"اختر الجنس",notProvided:"غير محدد",profileUpdated:"تم تحديث الملف الشخصي",accountSummary:"ملخص الحساب",totalOrders:"إجمالي الطلبات",totalSpent:"إجمالي المبلغ المنفق",wishlistItems:"عناصر المفضلة",memberSince:"عضو منذ",recentActivity:"النشاط الأخير",orderItems:"عناصر الطلب",quantity:"الكمية",orderSummary:"ملخص الطلب",noOrdersFound:"لم يتم العثور على طلبات",orderDetails:"تفاصيل الطلب",orderDetailsComingSoon:"تفاصيل الطلب ستكون متاحة قريباً",searchOrders:"البحث في الطلبات...",items:"عناصر",addAllToCart:"إضافة الكل للسلة",noOrdersYet:"لا توجد طلبات بعد",startShoppingMessage:"ابدأ التسوق الآن واكتشف مجموعتنا الرائعة من الملابس",startShopping:"ابدأ التسوق",emptyWishlist:"قائمة المفضلة فارغة",startAddingItemsToWishlist:"ابدأ بإضافة المنتجات المفضلة لديك",browseProducts:"تصفح المنتجات",removedFromWishlist:"تم حذف المنتج من المفضلة",errorRemovingFromWishlist:"خطأ في حذف المنتج من المفضلة",wishlistCleared:"تم مسح قائمة المفضلة",errorClearingWishlist:"خطأ في مسح قائمة المفضلة",allItemsAddedToCart:"تم إضافة جميع العناصر للسلة",confirmClearWishlist:"هل أنت متأكد من مسح قائمة المفضلة؟",clearWishlist:"مسح المفضلة",removeFromWishlist:"إزالة من المفضلة",addedOn:"أضيف في"},$w={common:uw,navigation:dw,header:fw,footer:mw,home:hw,products:pw,productDetail:gw,cart:xw,purchase:yw,whatsapp:vw,auth:ww,profile:Nw,admin:jw,currency:bw,demo:kw,analytics:Sw,customer:Cw},Pw={en:{translation:cw},ar:{translation:$w}};Ie.use(Fh).use(My).init({resources:Pw,fallbackLng:"ar",lng:"ar",debug:!1,interpolation:{escapeValue:!1},detection:{order:["localStorage","navigator","htmlTag"],caches:["localStorage"],lookupLocalStorage:"i18nextLng"},react:{useSuspense:!1}});vo.createRoot(document.getElementById("root")).render(a.jsx(Re.StrictMode,{children:a.jsx(d1,{})}));
