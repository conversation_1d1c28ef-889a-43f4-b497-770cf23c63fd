import React, { useState, useEffect } from 'react';
import { MapPin, Plus, Edit, Trash2, Home, Building, X } from 'lucide-react';
import { useLanguage } from '../../contexts/LanguageContext';
import { useAuth } from '../../contexts/AuthContext';
import { CustomerService } from '../../services/customerService';
import { Address } from '../../services/dataService';
import toast from 'react-hot-toast';

const CustomerAddresses: React.FC = () => {
  const { isRTL } = useLanguage();
  const { user } = useAuth();
  const [addresses, setAddresses] = useState<Address[]>([]);
  const [loading, setLoading] = useState(true);

  const customerService = new CustomerService();

  // Load addresses on component mount
  useEffect(() => {
    if (user?.id) {
      loadAddresses();
    }
  }, [user?.id]);

  const loadAddresses = () => {
    if (!user?.id) return;

    setLoading(true);
    try {
      const addresses = customerService.getCustomerAddresses(user.id);
      setAddresses(addresses);
    } catch (error) {
      console.error('Error loading addresses:', error);
    } finally {
      setLoading(false);
    }
  };

  const [showForm, setShowForm] = useState(false);
  const [editingAddress, setEditingAddress] = useState<Address | null>(null);

  const getAddressTypeIcon = (type: string) => {
    switch (type) {
      case 'home':
        return <Home className="h-5 w-5" />;
      case 'work':
        return <Building className="h-5 w-5" />;
      default:
        return <MapPin className="h-5 w-5" />;
    }
  };

  const getAddressTypeLabel = (type: string) => {
    switch (type) {
      case 'home':
        return isRTL ? 'المنزل' : 'Home';
      case 'work':
        return isRTL ? 'العمل' : 'Work';
      default:
        return isRTL ? 'أخرى' : 'Other';
    }
  };

  const deleteAddress = (addressId: string) => {
    if (!user?.id) return;

    if (window.confirm(isRTL ? 'هل أنت متأكد من حذف هذا العنوان؟' : 'Are you sure you want to delete this address?')) {
      try {
        const success = customerService.deleteAddress(user.id, addressId);
        if (success) {
          toast.success(isRTL ? 'تم حذف العنوان بنجاح' : 'Address deleted successfully');
          loadAddresses(); // Reload addresses
        } else {
          toast.error(isRTL ? 'حدث خطأ أثناء حذف العنوان' : 'Error deleting address');
        }
      } catch (error) {
        console.error('Error deleting address:', error);
        toast.error(isRTL ? 'حدث خطأ أثناء حذف العنوان' : 'Error deleting address');
      }
    }
  };

  const setDefaultAddress = (addressId: string) => {
    if (!user?.id) return;

    try {
      const success = customerService.setDefaultAddress(user.id, addressId);
      if (success) {
        toast.success(isRTL ? 'تم تعيين العنوان الافتراضي' : 'Default address set successfully');
        loadAddresses(); // Reload addresses
      } else {
        toast.error(isRTL ? 'حدث خطأ أثناء تعيين العنوان الافتراضي' : 'Error setting default address');
      }
    } catch (error) {
      console.error('Error setting default address:', error);
      toast.error(isRTL ? 'حدث خطأ أثناء تعيين العنوان الافتراضي' : 'Error setting default address');
    }
  };

  return (
    <div className="space-y-6">
      {/* Header */}
      <div className={`flex items-center justify-between ${isRTL ? 'flex-row-reverse' : ''}`}>
        <div>
          <h1 className={`text-3xl font-bold text-gray-900 ${isRTL ? 'font-arabic' : ''}`}>
            {isRTL ? 'عناويني' : 'My Addresses'}
          </h1>
          <p className={`mt-2 text-gray-600 ${isRTL ? 'font-arabic' : ''}`}>
            {isRTL ? 'إدارة عناوين الشحن والفوترة' : 'Manage your shipping and billing addresses'}
          </p>
        </div>
        
        <button
          onClick={() => setShowForm(true)}
          className={`
            flex items-center px-4 py-2 bg-black text-white rounded-lg hover:bg-gray-800 transition-colors
            ${isRTL ? 'flex-row-reverse' : ''}
          `}
        >
          <Plus className={`h-5 w-5 ${isRTL ? 'ml-2' : 'mr-2'}`} />
          <span className={isRTL ? 'font-arabic' : ''}>{isRTL ? 'إضافة عنوان جديد' : 'Add New Address'}</span>
        </button>
      </div>

      {/* Loading state */}
      {loading && (
        <div className="flex items-center justify-center py-12">
          <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-black"></div>
        </div>
      )}

      {/* Empty state */}
      {!loading && addresses.length === 0 && (
        <div className="text-center py-12">
          <MapPin className="mx-auto h-12 w-12 text-gray-400 mb-4" />
          <h2 className={`text-xl font-semibold text-gray-900 mb-2 ${isRTL ? 'font-arabic' : ''}`}>
            {isRTL ? 'لا توجد عناوين محفوظة' : 'No saved addresses'}
          </h2>
          <p className={`text-gray-600 mb-4 ${isRTL ? 'font-arabic' : ''}`}>
            {isRTL ? 'أضف عنوانك الأول لتسهيل عملية الشحن' : 'Add your first address to make shipping easier'}
          </p>
          <button
            onClick={() => setShowForm(true)}
            className={`bg-black text-white px-6 py-3 rounded-lg hover:bg-gray-800 transition-colors ${isRTL ? 'font-arabic' : ''}`}
          >
            {isRTL ? 'إضافة عنوان جديد' : 'Add New Address'}
          </button>
        </div>
      )}

      {/* Addresses grid */}
      {!loading && addresses.length > 0 && (
        <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
        {addresses.map((address) => (
          <div key={address.id} className="bg-white rounded-lg shadow-sm border border-gray-200 p-6 relative">
            {/* Default badge */}
            {address.isDefault && (
              <div className={`absolute top-4 ${isRTL ? 'left-4' : 'right-4'} bg-green-100 text-green-800 px-2 py-1 rounded text-xs font-medium ${isRTL ? 'font-arabic' : ''}`}>
                {isRTL ? 'العنوان الافتراضي' : 'Default'}
              </div>
            )}

            {/* Address type */}
            <div className={`flex items-center mb-4 ${isRTL ? 'flex-row-reverse' : ''}`}>
              <div className="h-10 w-10 bg-gray-100 rounded-lg flex items-center justify-center">
                {getAddressTypeIcon(address.type)}
              </div>
              <div className={isRTL ? 'mr-3 text-right' : 'ml-3 text-left'}>
                <h3 className={`font-medium text-gray-900 ${isRTL ? 'font-arabic' : ''}`}>
                  {getAddressTypeLabel(address.type)}
                </h3>
                <p className={`text-sm text-gray-500 ${isRTL ? 'font-arabic' : ''}`}>
                  {address.firstName} {address.lastName}
                </p>
              </div>
            </div>

            {/* Address details */}
            <div className={`space-y-2 mb-4 ${isRTL ? 'text-right' : 'text-left'}`}>
              <p className={`text-sm text-gray-900 ${isRTL ? 'font-arabic' : ''}`}>
                {address.street}
              </p>
              <p className={`text-sm text-gray-900 ${isRTL ? 'font-arabic' : ''}`}>
                {address.city}, {address.state} {address.postalCode}
              </p>
              <p className={`text-sm text-gray-900 ${isRTL ? 'font-arabic' : ''}`}>
                {address.country}
              </p>
              <p className="text-sm text-gray-500">
                {address.phone}
              </p>
            </div>

            {/* Actions */}
            <div className={`flex items-center space-x-3 ${isRTL ? 'space-x-reverse' : ''}`}>
              {!address.isDefault && (
                <button
                  onClick={() => setDefaultAddress(address.id)}
                  className={`text-sm text-blue-600 hover:text-blue-800 ${isRTL ? 'font-arabic' : ''}`}
                >
                  {isRTL ? 'تعيين كافتراضي' : 'Set as Default'}
                </button>
              )}
              
              <button
                onClick={() => {
                  setEditingAddress(address);
                  setShowForm(true);
                }}
                className="text-sm text-gray-600 hover:text-gray-800"
                title={isRTL ? 'تعديل' : 'Edit'}
              >
                <Edit className="h-4 w-4" />
              </button>
              
              <button
                onClick={() => deleteAddress(address.id)}
                className="text-sm text-red-600 hover:text-red-800"
                title={isRTL ? 'حذف' : 'Delete'}
              >
                <Trash2 className="h-4 w-4" />
              </button>
            </div>
          </div>
        ))}
        </div>
      )}

      {/* Add/Edit form modal */}
      {showForm && (
        <div className="fixed inset-0 z-50 overflow-y-auto">
          <div className="flex items-center justify-center min-h-screen px-4 pt-4 pb-20 text-center sm:block sm:p-0">
            <div className="fixed inset-0 transition-opacity bg-gray-500 bg-opacity-75" onClick={() => setShowForm(false)}></div>
            
            <div className="inline-block w-full max-w-md p-6 my-8 overflow-hidden text-left align-middle transition-all transform bg-white shadow-xl rounded-lg">
              <h3 className={`text-lg font-medium text-gray-900 mb-4 ${isRTL ? 'font-arabic text-right' : 'text-left'}`}>
                {editingAddress
                  ? (isRTL ? 'تعديل العنوان' : 'Edit Address')
                  : (isRTL ? 'إضافة عنوان جديد' : 'Add New Address')
                }
              </h3>
              
              <div className="space-y-4">
                <div className="grid grid-cols-2 gap-4">
                  <div>
                    <label className={`block text-sm font-medium text-gray-700 mb-1 ${isRTL ? 'font-arabic text-right' : 'text-left'}`}>
                      {isRTL ? 'الاسم الأول' : 'First Name'}
                    </label>
                    <input
                      type="text"
                      className={`w-full px-3 py-2 border border-gray-300 rounded-md focus:ring-2 focus:ring-black focus:border-transparent ${isRTL ? 'text-right font-arabic' : 'text-left'}`}
                      defaultValue={editingAddress?.firstName}
                    />
                  </div>
                  
                  <div>
                    <label className={`block text-sm font-medium text-gray-700 mb-1 ${isRTL ? 'font-arabic text-right' : 'text-left'}`}>
                      الاسم الأخير
                    </label>
                    <input
                      type="text"
                      className={`w-full px-3 py-2 border border-gray-300 rounded-md focus:ring-2 focus:ring-black focus:border-transparent ${isRTL ? 'text-right font-arabic' : 'text-left'}`}
                      defaultValue={editingAddress?.lastName}
                    />
                  </div>
                </div>

                <div>
                  <label className={`block text-sm font-medium text-gray-700 mb-1 ${isRTL ? 'font-arabic text-right' : 'text-left'}`}>
                    نوع العنوان
                  </label>
                  <select className={`w-full px-3 py-2 border border-gray-300 rounded-md focus:ring-2 focus:ring-black focus:border-transparent ${isRTL ? 'font-arabic' : ''}`}>
                    <option value="home">المنزل</option>
                    <option value="work">العمل</option>
                    <option value="other">أخرى</option>
                  </select>
                </div>

                <div>
                  <label className={`block text-sm font-medium text-gray-700 mb-1 ${isRTL ? 'font-arabic text-right' : 'text-left'}`}>
                    الشارع والحي
                  </label>
                  <input
                    type="text"
                    className={`w-full px-3 py-2 border border-gray-300 rounded-md focus:ring-2 focus:ring-black focus:border-transparent ${isRTL ? 'text-right font-arabic' : 'text-left'}`}
                    defaultValue={editingAddress?.street}
                  />
                </div>

                <div className="grid grid-cols-2 gap-4">
                  <div>
                    <label className={`block text-sm font-medium text-gray-700 mb-1 ${isRTL ? 'font-arabic text-right' : 'text-left'}`}>
                      المدينة
                    </label>
                    <input
                      type="text"
                      className={`w-full px-3 py-2 border border-gray-300 rounded-md focus:ring-2 focus:ring-black focus:border-transparent ${isRTL ? 'text-right font-arabic' : 'text-left'}`}
                      defaultValue={editingAddress?.city}
                    />
                  </div>
                  
                  <div>
                    <label className={`block text-sm font-medium text-gray-700 mb-1 ${isRTL ? 'font-arabic text-right' : 'text-left'}`}>
                      الرمز البريدي
                    </label>
                    <input
                      type="text"
                      className="w-full px-3 py-2 border border-gray-300 rounded-md focus:ring-2 focus:ring-black focus:border-transparent"
                      defaultValue={editingAddress?.postalCode}
                    />
                  </div>
                </div>
              </div>

              <div className={`flex items-center space-x-3 mt-6 ${isRTL ? 'space-x-reverse' : ''}`}>
                <button
                  onClick={() => {
                    setShowForm(false);
                    setEditingAddress(null);
                    alert('تم حفظ العنوان بنجاح');
                  }}
                  className={`flex-1 px-4 py-2 bg-black text-white rounded-md hover:bg-gray-800 transition-colors ${isRTL ? 'font-arabic' : ''}`}
                >
                  حفظ العنوان
                </button>
                
                <button
                  onClick={() => {
                    setShowForm(false);
                    setEditingAddress(null);
                  }}
                  className={`flex-1 px-4 py-2 bg-gray-200 text-gray-800 rounded-md hover:bg-gray-300 transition-colors ${isRTL ? 'font-arabic' : ''}`}
                >
                  إلغاء
                </button>
              </div>
            </div>
          </div>
        </div>
      )}
    </div>
  );
};

export default CustomerAddresses;
