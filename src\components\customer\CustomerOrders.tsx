import React, { useState, useEffect } from 'react';
import { Package, ShoppingBag } from 'lucide-react';
import { useLanguage } from '../../contexts/LanguageContext';
import { useAuth } from '../../contexts/AuthContext';

interface SimpleOrder {
  id: string;
  orderNumber: string;
  status: string;
  total: number;
  itemCount: number;
  createdAt: string;
}

const CustomerOrders: React.FC = () => {
  const { isRTL } = useLanguage();
  const { user } = useAuth();
  const [orders, setOrders] = useState<SimpleOrder[]>([]);
  const [loading, setLoading] = useState(true);

  useEffect(() => {
    if (user?.id) {
      loadOrders();
    }
  }, [user?.id]);

  const loadOrders = () => {
    if (!user?.id) return;

    setLoading(true);
    try {
      // Mock orders data
      const mockOrders: SimpleOrder[] = [
        {
          id: '1',
          orderNumber: 'ORD-2024-001',
          status: 'delivered',
          total: 339.97,
          itemCount: 3,
          createdAt: '2024-01-15T10:30:00Z'
        },
        {
          id: '2',
          orderNumber: 'ORD-2024-002',
          status: 'shipped',
          total: 199.99,
          itemCount: 1,
          createdAt: '2024-01-20T09:15:00Z'
        },
        {
          id: '3',
          orderNumber: 'ORD-2024-003',
          status: 'processing',
          total: 129.99,
          itemCount: 1,
          createdAt: '2024-01-25T11:00:00Z'
        },
        {
          id: '4',
          orderNumber: 'ORD-2024-004',
          status: 'pending',
          total: 89.99,
          itemCount: 1,
          createdAt: '2024-01-28T14:20:00Z'
        }
      ];

      setOrders(mockOrders);
    } catch (error) {
      console.error('Error loading orders:', error);
    } finally {
      setLoading(false);
    }
  };

  const getStatusText = (status: string) => {
    const statusMap = {
      pending: isRTL ? 'قيد الانتظار' : 'Pending',
      processing: isRTL ? 'قيد المعالجة' : 'Processing',
      shipped: isRTL ? 'تم الشحن' : 'Shipped',
      delivered: isRTL ? 'تم التسليم' : 'Delivered',
      cancelled: isRTL ? 'ملغي' : 'Cancelled'
    };
    return statusMap[status as keyof typeof statusMap] || status;
  };

  const formatPrice = (price: number) => {
    return isRTL ? `${price.toFixed(2)} ر.س` : `SAR ${price.toFixed(2)}`;
  };

  if (loading) {
    return (
      <div className="space-y-4">
        <h2 className={`text-2xl font-bold text-gray-900 ${isRTL ? 'font-arabic' : ''}`}>
          {isRTL ? 'طلباتي' : 'My Orders'}
        </h2>
        <div className="space-y-4">
          {Array.from({ length: 3 }).map((_, index) => (
            <div key={index} className="animate-pulse bg-gray-200 rounded-lg h-32"></div>
          ))}
        </div>
      </div>
    );
  }

  if (orders.length === 0) {
    return (
      <div className="text-center py-12">
        <ShoppingBag className="h-16 w-16 text-gray-400 mx-auto mb-4" />
        <h3 className={`text-xl font-semibold text-gray-900 mb-2 ${isRTL ? 'font-arabic' : ''}`}>
          {isRTL ? 'لا توجد طلبات' : 'No Orders Yet'}
        </h3>
        <p className={`text-gray-500 mb-6 ${isRTL ? 'font-arabic' : ''}`}>
          {isRTL ? 'لم تقم بأي طلبات حتى الآن. ابدأ التسوق الآن!' : 'You haven\'t placed any orders yet. Start shopping now!'}
        </p>
        <button
          onClick={() => window.location.href = '/products'}
          className={`bg-black text-white px-6 py-3 rounded-lg hover:bg-gray-800 transition-colors ${isRTL ? 'font-arabic' : ''}`}
        >
          {isRTL ? 'ابدأ التسوق' : 'Start Shopping'}
        </button>
      </div>
    );
  }

  return (
    <div className="space-y-6">
      <h2 className={`text-2xl font-bold text-gray-900 ${isRTL ? 'font-arabic' : ''}`}>
        {isRTL ? 'طلباتي' : 'My Orders'}
      </h2>

      <div className="space-y-4">
        {orders.map((order) => (
          <div key={order.id} className="bg-white border border-gray-200 rounded-lg p-6 hover:shadow-md transition-shadow">
            <div className={`flex items-start justify-between mb-4 ${isRTL ? 'flex-row-reverse' : ''}`}>
              <div className={`flex items-center space-x-3 ${isRTL ? 'space-x-reverse' : ''}`}>
                <Package className="h-5 w-5 text-gray-600" />
                <div>
                  <h3 className={`font-semibold text-gray-900 ${isRTL ? 'font-arabic' : ''}`}>
                    {isRTL ? `طلب رقم: ${order.orderNumber}` : `Order #${order.orderNumber}`}
                  </h3>
                  <p className={`text-sm text-gray-500 ${isRTL ? 'font-arabic' : ''}`}>
                    {new Date(order.createdAt).toLocaleDateString(isRTL ? 'ar-SA' : 'en-US')}
                  </p>
                </div>
              </div>
              <span className={`inline-block px-3 py-1 rounded-full text-xs font-medium bg-gray-100 text-gray-800 ${isRTL ? 'font-arabic' : ''}`}>
                {getStatusText(order.status)}
              </span>
            </div>

            <div className={`flex items-center justify-between ${isRTL ? 'flex-row-reverse' : ''}`}>
              <div>
                <p className={`text-sm text-gray-600 ${isRTL ? 'font-arabic' : ''}`}>
                  {isRTL ? `${order.itemCount} منتج` : `${order.itemCount} items`}
                </p>
                <p className={`font-semibold text-gray-900 ${isRTL ? 'font-arabic' : ''}`}>
                  {formatPrice(order.total)}
                </p>
              </div>
            </div>
          </div>
        ))}
      </div>
    </div>
  );
};

export default CustomerOrders;