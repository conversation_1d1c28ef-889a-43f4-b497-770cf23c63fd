import React, { useState, useEffect } from 'react';
import { Package, ShoppingBag, Eye, Truck, CheckCircle, Clock, XCircle } from 'lucide-react';
import { useLanguage } from '../../contexts/LanguageContext';
import { useAuth } from '../../contexts/AuthContext';
import { CustomerService } from '../../services/customerService';
import { Order } from '../../services/dataService';
import toast from 'react-hot-toast';

const CustomerOrders: React.FC = () => {
  const { isRTL } = useLanguage();
  const { user } = useAuth();
  const [orders, setOrders] = useState<Order[]>([]);
  const [loading, setLoading] = useState(true);
  const [selectedOrder, setSelectedOrder] = useState<Order | null>(null);
  const [showOrderDetails, setShowOrderDetails] = useState(false);

  const customerService = new CustomerService();

  useEffect(() => {
    if (user?.id) {
      loadOrders();
    }
  }, [user?.id]);

  const loadOrders = () => {
    if (!user?.id) return;

    setLoading(true);
    try {
      const customerData = customerService.getCustomerStats(user.id);
      setOrders(customerData.recentOrders || []);
    } catch (error) {
      console.error('Error loading orders:', error);
      toast.error(isRTL ? 'خطأ في تحميل الطلبات' : 'Error loading orders');
    } finally {
      setLoading(false);
    }
  };

  const getStatusIcon = (status: Order['status']) => {
    switch (status) {
      case 'pending':
        return <Clock className="h-5 w-5 text-yellow-500" />;
      case 'processing':
        return <Package className="h-5 w-5 text-blue-500" />;
      case 'shipped':
        return <Truck className="h-5 w-5 text-purple-500" />;
      case 'delivered':
        return <CheckCircle className="h-5 w-5 text-green-500" />;
      case 'cancelled':
        return <XCircle className="h-5 w-5 text-red-500" />;
      default:
        return <Package className="h-5 w-5 text-gray-500" />;
    }
  };

  const getStatusText = (status: Order['status']) => {
    if (isRTL) {
      switch (status) {
        case 'pending': return 'في الانتظار';
        case 'processing': return 'قيد المعالجة';
        case 'shipped': return 'تم الشحن';
        case 'delivered': return 'تم التسليم';
        case 'cancelled': return 'ملغي';
        default: return status;
      }
    } else {
      switch (status) {
        case 'pending': return 'Pending';
        case 'processing': return 'Processing';
        case 'shipped': return 'Shipped';
        case 'delivered': return 'Delivered';
        case 'cancelled': return 'Cancelled';
        default: return status;
      }
    }
  };

  const getStatusColor = (status: Order['status']) => {
    switch (status) {
      case 'pending': return 'bg-yellow-100 text-yellow-800';
      case 'processing': return 'bg-blue-100 text-blue-800';
      case 'shipped': return 'bg-purple-100 text-purple-800';
      case 'delivered': return 'bg-green-100 text-green-800';
      case 'cancelled': return 'bg-red-100 text-red-800';
      default: return 'bg-gray-100 text-gray-800';
    }
  };

  const viewOrderDetails = (order: Order) => {
    setSelectedOrder(order);
    setShowOrderDetails(true);
  };

  const closeOrderDetails = () => {
    setSelectedOrder(null);
    setShowOrderDetails(false);
  };

  const formatPrice = (price: number) => {
    return isRTL ? `${price.toFixed(2)} ر.س` : `SAR ${price.toFixed(2)}`;
  };

  if (loading) {
    return (
      <div className="space-y-4">
        <h2 className={`text-2xl font-bold text-gray-900 ${isRTL ? 'font-arabic' : ''}`}>
          {isRTL ? 'طلباتي' : 'My Orders'}
        </h2>
        <div className="space-y-4">
          {Array.from({ length: 3 }).map((_, index) => (
            <div key={index} className="animate-pulse bg-gray-200 rounded-lg h-32"></div>
          ))}
        </div>
      </div>
    );
  }

  if (orders.length === 0) {
    return (
      <div className="text-center py-12">
        <ShoppingBag className="h-16 w-16 text-gray-400 mx-auto mb-4" />
        <h3 className={`text-xl font-semibold text-gray-900 mb-2 ${isRTL ? 'font-arabic' : ''}`}>
          {isRTL ? 'لا توجد طلبات' : 'No Orders Yet'}
        </h3>
        <p className={`text-gray-500 mb-6 ${isRTL ? 'font-arabic' : ''}`}>
          {isRTL ? 'لم تقم بأي طلبات حتى الآن. ابدأ التسوق الآن!' : 'You haven\'t placed any orders yet. Start shopping now!'}
        </p>
        <button
          onClick={() => window.location.href = '/products'}
          className={`bg-black text-white px-6 py-3 rounded-lg hover:bg-gray-800 transition-colors ${isRTL ? 'font-arabic' : ''}`}
        >
          {isRTL ? 'ابدأ التسوق' : 'Start Shopping'}
        </button>
      </div>
    );
  }

  return (
    <div className="space-y-6">
      <h2 className={`text-2xl font-bold text-gray-900 ${isRTL ? 'font-arabic' : ''}`}>
        {isRTL ? 'طلباتي' : 'My Orders'}
      </h2>

      <div className="space-y-4">
        {orders.map((order) => (
          <div key={order.id} className="bg-white border border-gray-200 rounded-lg p-6 hover:shadow-md transition-shadow">
            <div className={`flex items-start justify-between mb-4 ${isRTL ? 'flex-row-reverse' : ''}`}>
              <div className={`flex items-center space-x-3 ${isRTL ? 'space-x-reverse' : ''}`}>
                <Package className="h-5 w-5 text-gray-600" />
                <div>
                  <h3 className={`font-semibold text-gray-900 ${isRTL ? 'font-arabic' : ''}`}>
                    {isRTL ? `طلب رقم: ${order.orderNumber}` : `Order #${order.orderNumber}`}
                  </h3>
                  <p className={`text-sm text-gray-500 ${isRTL ? 'font-arabic' : ''}`}>
                    {new Date(order.createdAt).toLocaleDateString(isRTL ? 'ar-SA' : 'en-US')}
                  </p>
                </div>
              </div>
              <div className={`flex items-center space-x-2 ${isRTL ? 'space-x-reverse' : ''}`}>
                {getStatusIcon(order.status)}
                <span className={`inline-block px-3 py-1 rounded-full text-xs font-medium ${getStatusColor(order.status)} ${isRTL ? 'font-arabic' : ''}`}>
                  {getStatusText(order.status)}
                </span>
              </div>
            </div>

            <div className={`flex items-center justify-between ${isRTL ? 'flex-row-reverse' : ''}`}>
              <div>
                <p className={`text-sm text-gray-600 ${isRTL ? 'font-arabic' : ''}`}>
                  {isRTL ? `${order.items.length} منتج` : `${order.items.length} items`}
                </p>
                <p className={`font-semibold text-gray-900 ${isRTL ? 'font-arabic' : ''}`}>
                  {formatPrice(order.total)}
                </p>
              </div>
              <button
                onClick={() => viewOrderDetails(order)}
                className={`
                  flex items-center px-4 py-2 text-black border border-black rounded-lg hover:bg-black hover:text-white transition-colors
                  ${isRTL ? 'flex-row-reverse' : ''}
                `}
              >
                <Eye className={`h-4 w-4 ${isRTL ? 'ml-2' : 'mr-2'}`} />
                <span className={isRTL ? 'font-arabic' : ''}>{isRTL ? 'عرض التفاصيل' : 'View Details'}</span>
              </button>
            </div>
          </div>
        ))}
      </div>

      {/* Order Details Modal */}
      {showOrderDetails && selectedOrder && (
        <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center p-4 z-50">
          <div className="bg-white rounded-lg max-w-2xl w-full max-h-[90vh] overflow-y-auto">
            <div className={`flex items-center justify-between p-6 border-b ${isRTL ? 'flex-row-reverse' : ''}`}>
              <h3 className={`text-xl font-bold text-gray-900 ${isRTL ? 'font-arabic' : ''}`}>
                {isRTL ? 'تفاصيل الطلب' : 'Order Details'}
              </h3>
              <button
                onClick={closeOrderDetails}
                className="text-gray-400 hover:text-gray-600 transition-colors"
              >
                <XCircle className="h-6 w-6" />
              </button>
            </div>

            <div className="p-6 space-y-6">
              {/* Order Info */}
              <div className={`grid grid-cols-2 gap-4 ${isRTL ? 'text-right' : 'text-left'}`}>
                <div>
                  <p className={`text-sm text-gray-500 ${isRTL ? 'font-arabic' : ''}`}>
                    {isRTL ? 'رقم الطلب' : 'Order Number'}
                  </p>
                  <p className={`font-semibold ${isRTL ? 'font-arabic' : ''}`}>{selectedOrder.orderNumber}</p>
                </div>
                <div>
                  <p className={`text-sm text-gray-500 ${isRTL ? 'font-arabic' : ''}`}>
                    {isRTL ? 'تاريخ الطلب' : 'Order Date'}
                  </p>
                  <p className={`font-semibold ${isRTL ? 'font-arabic' : ''}`}>
                    {new Date(selectedOrder.createdAt).toLocaleDateString(isRTL ? 'ar-SA' : 'en-US')}
                  </p>
                </div>
                <div>
                  <p className={`text-sm text-gray-500 ${isRTL ? 'font-arabic' : ''}`}>
                    {isRTL ? 'حالة الطلب' : 'Status'}
                  </p>
                  <div className={`flex items-center space-x-2 ${isRTL ? 'space-x-reverse' : ''}`}>
                    {getStatusIcon(selectedOrder.status)}
                    <span className={`font-semibold ${isRTL ? 'font-arabic' : ''}`}>
                      {getStatusText(selectedOrder.status)}
                    </span>
                  </div>
                </div>
                <div>
                  <p className={`text-sm text-gray-500 ${isRTL ? 'font-arabic' : ''}`}>
                    {isRTL ? 'طريقة الدفع' : 'Payment Method'}
                  </p>
                  <p className={`font-semibold ${isRTL ? 'font-arabic' : ''}`}>
                    {selectedOrder.paymentMethod === 'credit_card'
                      ? (isRTL ? 'بطاقة ائتمان' : 'Credit Card')
                      : (isRTL ? 'الدفع عند الاستلام' : 'Cash on Delivery')
                    }
                  </p>
                </div>
              </div>

              {/* Order Items */}
              <div>
                <h4 className={`text-lg font-semibold text-gray-900 mb-4 ${isRTL ? 'font-arabic text-right' : ''}`}>
                  {isRTL ? 'المنتجات' : 'Items'}
                </h4>
                <div className="space-y-3">
                  {selectedOrder.items.map((item) => (
                    <div key={item.id} className={`flex items-center space-x-4 p-3 bg-gray-50 rounded-lg ${isRTL ? 'space-x-reverse' : ''}`}>
                      <img
                        src={item.image}
                        alt={isRTL ? item.nameAr : item.name}
                        className="w-16 h-16 object-cover rounded-lg"
                        onError={(e) => {
                          const target = e.target as HTMLImageElement;
                          target.src = '/images/placeholder-product.jpg';
                        }}
                      />
                      <div className={`flex-1 ${isRTL ? 'text-right' : 'text-left'}`}>
                        <h5 className={`font-semibold text-gray-900 ${isRTL ? 'font-arabic' : ''}`}>
                          {isRTL ? item.nameAr : item.name}
                        </h5>
                        <p className={`text-sm text-gray-500 ${isRTL ? 'font-arabic' : ''}`}>
                          {isRTL ? 'الكمية:' : 'Quantity:'} {item.quantity}
                        </p>
                        {item.variant && (
                          <p className={`text-sm text-gray-500 ${isRTL ? 'font-arabic' : ''}`}>
                            {item.variant.size && `${isRTL ? 'المقاس:' : 'Size:'} ${item.variant.size}`}
                            {item.variant.color && ` | ${isRTL ? 'اللون:' : 'Color:'} ${item.variant.color}`}
                          </p>
                        )}
                      </div>
                      <div className={`text-right ${isRTL ? 'text-left' : ''}`}>
                        <p className={`font-semibold text-gray-900 ${isRTL ? 'font-arabic' : ''}`}>
                          {formatPrice(item.price * item.quantity)}
                        </p>
                      </div>
                    </div>
                  ))}
                </div>
              </div>

              {/* Shipping Address */}
              <div>
                <h4 className={`text-lg font-semibold text-gray-900 mb-4 ${isRTL ? 'font-arabic text-right' : ''}`}>
                  {isRTL ? 'عنوان الشحن' : 'Shipping Address'}
                </h4>
                <div className={`p-4 bg-gray-50 rounded-lg ${isRTL ? 'text-right' : 'text-left'}`}>
                  <p className={`font-semibold ${isRTL ? 'font-arabic' : ''}`}>
                    {selectedOrder.shippingAddress.firstName} {selectedOrder.shippingAddress.lastName}
                  </p>
                  <p className={`text-gray-600 ${isRTL ? 'font-arabic' : ''}`}>
                    {selectedOrder.shippingAddress.street}
                  </p>
                  <p className={`text-gray-600 ${isRTL ? 'font-arabic' : ''}`}>
                    {selectedOrder.shippingAddress.city}, {selectedOrder.shippingAddress.state} {selectedOrder.shippingAddress.postalCode}
                  </p>
                  <p className={`text-gray-600 ${isRTL ? 'font-arabic' : ''}`}>
                    {selectedOrder.shippingAddress.country}
                  </p>
                  <p className={`text-gray-600 ${isRTL ? 'font-arabic' : ''}`}>
                    {selectedOrder.shippingAddress.phone}
                  </p>
                </div>
              </div>

              {/* Order Summary */}
              <div>
                <h4 className={`text-lg font-semibold text-gray-900 mb-4 ${isRTL ? 'font-arabic text-right' : ''}`}>
                  {isRTL ? 'ملخص الطلب' : 'Order Summary'}
                </h4>
                <div className={`space-y-2 ${isRTL ? 'text-right' : 'text-left'}`}>
                  <div className={`flex justify-between ${isRTL ? 'flex-row-reverse' : ''}`}>
                    <span className={`text-gray-600 ${isRTL ? 'font-arabic' : ''}`}>
                      {isRTL ? 'المجموع الفرعي' : 'Subtotal'}
                    </span>
                    <span className={`font-semibold ${isRTL ? 'font-arabic' : ''}`}>
                      {formatPrice(selectedOrder.subtotal)}
                    </span>
                  </div>
                  <div className={`flex justify-between ${isRTL ? 'flex-row-reverse' : ''}`}>
                    <span className={`text-gray-600 ${isRTL ? 'font-arabic' : ''}`}>
                      {isRTL ? 'الضريبة' : 'Tax'}
                    </span>
                    <span className={`font-semibold ${isRTL ? 'font-arabic' : ''}`}>
                      {formatPrice(selectedOrder.tax)}
                    </span>
                  </div>
                  <div className={`flex justify-between ${isRTL ? 'flex-row-reverse' : ''}`}>
                    <span className={`text-gray-600 ${isRTL ? 'font-arabic' : ''}`}>
                      {isRTL ? 'الشحن' : 'Shipping'}
                    </span>
                    <span className={`font-semibold ${isRTL ? 'font-arabic' : ''}`}>
                      {selectedOrder.shipping === 0 ? (isRTL ? 'مجاني' : 'Free') : formatPrice(selectedOrder.shipping)}
                    </span>
                  </div>
                  <div className={`flex justify-between border-t pt-2 ${isRTL ? 'flex-row-reverse' : ''}`}>
                    <span className={`text-lg font-bold text-gray-900 ${isRTL ? 'font-arabic' : ''}`}>
                      {isRTL ? 'المجموع الكلي' : 'Total'}
                    </span>
                    <span className={`text-lg font-bold text-gray-900 ${isRTL ? 'font-arabic' : ''}`}>
                      {formatPrice(selectedOrder.total)}
                    </span>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>
      )}
    </div>
  );
};

export default CustomerOrders;