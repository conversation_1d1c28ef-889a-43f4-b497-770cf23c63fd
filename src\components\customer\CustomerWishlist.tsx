import React, { useState, useEffect } from 'react';
import { Heart, ShoppingCart, Trash2 } from 'lucide-react';
import { useLanguage } from '../../contexts/LanguageContext';
import { useAuth } from '../../contexts/AuthContext';
import { CustomerService } from '../../services/customerService';
import { WishlistItem } from '../../services/dataService';
import toast from 'react-hot-toast';

const CustomerWishlist: React.FC = () => {
  const { isRTL } = useLanguage();
  const { user } = useAuth();
  const [wishlistItems, setWishlistItems] = useState<WishlistItem[]>([]);
  const [loading, setLoading] = useState(true);

  const customerService = new CustomerService();

  useEffect(() => {
    if (user?.id) {
      loadWishlist();
    }
  }, [user?.id]);

  const loadWishlist = () => {
    if (!user?.id) return;

    setLoading(true);
    try {
      const items = customerService.getCustomerWishlist(user.id);
      setWishlistItems(items);
    } catch (error) {
      console.error('Error loading wishlist:', error);
      toast.error(isRTL ? 'خطأ في تحميل المفضلة' : 'Error loading wishlist');
    } finally {
      setLoading(false);
    }
  };

  const removeFromWishlist = (productId: string) => {
    if (!user?.id) return;

    try {
      customerService.removeFromWishlist(user.id, productId);
      setWishlistItems(prev => prev.filter(item => item.productId !== productId));
      toast.success(isRTL ? 'تم حذف المنتج من المفضلة' : 'Product removed from wishlist');
    } catch (error) {
      console.error('Error removing from wishlist:', error);
      toast.error(isRTL ? 'خطأ في حذف المنتج' : 'Error removing product');
    }
  };

  const addToCart = (item: WishlistItem) => {
    // In a real app, this would add the item to the cart
    toast.success(isRTL ? 'تم إضافة المنتج إلى السلة' : 'Product added to cart');
  };

  const clearWishlist = () => {
    if (!user?.id) return;

    if (window.confirm(isRTL ? 'هل أنت متأكد من حذف جميع المنتجات من المفضلة؟' : 'Are you sure you want to clear your wishlist?')) {
      try {
        customerService.clearWishlist(user.id);
        setWishlistItems([]);
        toast.success(isRTL ? 'تم حذف جميع المنتجات من المفضلة' : 'Wishlist cleared successfully');
      } catch (error) {
        console.error('Error clearing wishlist:', error);
        toast.error(isRTL ? 'خطأ في حذف المفضلة' : 'Error clearing wishlist');
      }
    }
  };

  if (loading) {
    return (
      <div className="flex items-center justify-center py-12">
        <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-black"></div>
      </div>
    );
  }

  if (wishlistItems.length === 0) {
    return (
      <div className="text-center py-12">
        <Heart className="h-16 w-16 text-gray-400 mx-auto mb-4" />
        <h3 className={`text-xl font-semibold text-gray-900 mb-2 ${isRTL ? 'font-arabic' : ''}`}>
          {isRTL ? 'المفضلة فارغة' : 'Your wishlist is empty'}
        </h3>
        <p className={`text-gray-500 mb-6 ${isRTL ? 'font-arabic' : ''}`}>
          {isRTL ? 'ابدأ بإضافة المنتجات التي تعجبك إلى المفضلة' : 'Start adding products you love to your wishlist'}
        </p>
        <button
          onClick={() => window.location.href = '/products'}
          className={`bg-black text-white px-6 py-3 rounded-lg hover:bg-gray-800 transition-colors ${isRTL ? 'font-arabic' : ''}`}
        >
          {isRTL ? 'تصفح المنتجات' : 'Browse Products'}
        </button>
      </div>
    );
  }

  return (
    <div className="space-y-6">
      {/* Header */}
      <div className={`flex items-center justify-between ${isRTL ? 'flex-row-reverse' : ''}`}>
        <div>
          <h1 className={`text-3xl font-bold text-gray-900 ${isRTL ? 'font-arabic' : ''}`}>
            {isRTL ? 'المفضلة' : 'My Wishlist'}
          </h1>
          <p className={`mt-2 text-gray-600 ${isRTL ? 'font-arabic' : ''}`}>
            {isRTL ? `${wishlistItems.length} منتج في المفضلة` : `${wishlistItems.length} items in your wishlist`}
          </p>
        </div>

        {wishlistItems.length > 0 && (
          <button
            onClick={clearWishlist}
            className={`
              flex items-center px-4 py-2 text-red-600 border border-red-600 rounded-lg hover:bg-red-50 transition-colors
              ${isRTL ? 'flex-row-reverse' : ''}
            `}
          >
            <Trash2 className={`h-5 w-5 ${isRTL ? 'ml-2' : 'mr-2'}`} />
            <span className={isRTL ? 'font-arabic' : ''}>{isRTL ? 'حذف الكل' : 'Clear All'}</span>
          </button>
        )}
      </div>

      {/* Wishlist Grid */}
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
        {wishlistItems.map((item) => (
          <div key={item.id} className="bg-white rounded-lg shadow-sm border border-gray-200 overflow-hidden hover:shadow-md transition-shadow">
            {/* Product Image */}
            <div className="relative aspect-square bg-gray-100">
              <img
                src={item.image}
                alt={isRTL ? item.nameAr : item.name}
                className="w-full h-full object-cover"
                onError={(e) => {
                  const target = e.target as HTMLImageElement;
                  target.src = '/images/placeholder-product.jpg';
                }}
              />
              {!item.inStock && (
                <div className="absolute inset-0 bg-black bg-opacity-50 flex items-center justify-center">
                  <span className={`text-white font-semibold ${isRTL ? 'font-arabic' : ''}`}>
                    {isRTL ? 'غير متوفر' : 'Out of Stock'}
                  </span>
                </div>
              )}
              <button
                onClick={() => removeFromWishlist(item.productId)}
                className="absolute top-3 right-3 p-2 bg-white rounded-full shadow-md hover:bg-gray-50 transition-colors"
              >
                <Heart className="h-5 w-5 text-red-500 fill-current" />
              </button>
            </div>

            {/* Product Info */}
            <div className="p-4">
              <div className={`mb-2 ${isRTL ? 'text-right' : 'text-left'}`}>
                <h3 className={`font-semibold text-gray-900 mb-1 ${isRTL ? 'font-arabic' : ''}`}>
                  {isRTL ? item.nameAr : item.name}
                </h3>
                <p className={`text-sm text-gray-500 capitalize ${isRTL ? 'font-arabic' : ''}`}>
                  {item.category}
                </p>
              </div>

              {/* Price */}
              <div className={`flex items-center mb-3 ${isRTL ? 'flex-row-reverse' : ''}`}>
                <span className="text-lg font-bold text-gray-900">
                  {item.price.toFixed(2)} {isRTL ? 'ر.س' : 'SAR'}
                </span>
                {item.originalPrice && item.originalPrice > item.price && (
                  <span className={`text-sm text-gray-500 line-through ${isRTL ? 'mr-2' : 'ml-2'}`}>
                    {item.originalPrice.toFixed(2)} {isRTL ? 'ر.س' : 'SAR'}
                  </span>
                )}
              </div>

              {/* Added Date */}
              <p className={`text-xs text-gray-400 mb-3 ${isRTL ? 'font-arabic text-right' : 'text-left'}`}>
                {isRTL ? 'أضيف في' : 'Added on'} {new Date(item.addedDate).toLocaleDateString(isRTL ? 'ar-SA' : 'en-US')}
              </p>

              {/* Actions */}
              <div className={`flex space-x-2 ${isRTL ? 'flex-row-reverse space-x-reverse' : ''}`}>
                <button
                  onClick={() => addToCart(item)}
                  disabled={!item.inStock}
                  className={`
                    flex-1 flex items-center justify-center px-4 py-2 rounded-lg transition-colors
                    ${item.inStock
                      ? 'bg-black text-white hover:bg-gray-800'
                      : 'bg-gray-200 text-gray-400 cursor-not-allowed'
                    }
                    ${isRTL ? 'flex-row-reverse' : ''}
                  `}
                >
                  <ShoppingCart className={`h-4 w-4 ${isRTL ? 'ml-2' : 'mr-2'}`} />
                  <span className={isRTL ? 'font-arabic' : ''}>{isRTL ? 'أضف للسلة' : 'Add to Cart'}</span>
                </button>
                <button
                  onClick={() => removeFromWishlist(item.productId)}
                  className="p-2 text-gray-400 hover:text-red-500 transition-colors"
                >
                  <Trash2 className="h-4 w-4" />
                </button>
              </div>
            </div>
          </div>
        ))}
      </div>
    </div>
  );
};

export default CustomerWishlist;
