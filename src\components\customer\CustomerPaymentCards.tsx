import React, { useState, useEffect } from 'react';
import { CreditCard, Plus, Edit, Trash2, Shield, X } from 'lucide-react';
import { useAuth } from '../../contexts/AuthContext';
import { useLanguage } from '../../contexts/LanguageContext';
import { CustomerService } from '../../services/customerService';
import { PaymentCard } from '../../services/dataService';
import toast from 'react-hot-toast';

const CustomerPaymentCards: React.FC = () => {
  const { user } = useAuth();
  const { isRTL } = useLanguage();
  const [cards, setCards] = useState<PaymentCard[]>([]);
  const [loading, setLoading] = useState(true);
  const [showAddForm, setShowAddForm] = useState(false);
  const [editingCard, setEditingCard] = useState<PaymentCard | null>(null);
  const [formData, setFormData] = useState({
    cardNumber: '',
    cardholderName: '',
    expiryDate: '',
    cvv: '',
    isDefault: false
  });

  const customerService = new CustomerService();

  useEffect(() => {
    loadPaymentCards();
  }, [user?.id]);

  const loadPaymentCards = () => {
    if (!user?.id) return;

    setLoading(true);
    try {
      const userCards = customerService.getCustomerPaymentCards(user.id);
      setCards(userCards);
    } catch (error) {
      console.error('Error loading payment cards:', error);
      toast.error(isRTL ? 'خطأ في تحميل بطاقات الدفع' : 'Error loading payment cards');
    } finally {
      setLoading(false);
    }
  };

  const getCardType = (cardNumber: string): 'visa' | 'mastercard' | 'amex' => {
    const number = cardNumber.replace(/\s/g, '');
    if (number.startsWith('4')) return 'visa';
    if (number.startsWith('5') || number.startsWith('2')) return 'mastercard';
    if (number.startsWith('3')) return 'amex';
    return 'visa';
  };

  const formatCardNumber = (value: string) => {
    const v = value.replace(/\s+/g, '').replace(/[^0-9]/gi, '');
    const matches = v.match(/\d{4,16}/g);
    const match = matches && matches[0] || '';
    const parts = [];
    for (let i = 0, len = match.length; i < len; i += 4) {
      parts.push(match.substring(i, i + 4));
    }
    if (parts.length) {
      return parts.join(' ');
    } else {
      return v;
    }
  };

  const handleInputChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const { name, value, type, checked } = e.target;
    
    if (name === 'cardNumber') {
      const formatted = formatCardNumber(value);
      setFormData(prev => ({ ...prev, [name]: formatted }));
    } else if (name === 'expiryDate') {
      let formatted = value.replace(/\D/g, '');
      if (formatted.length >= 2) {
        formatted = formatted.substring(0, 2) + '/' + formatted.substring(2, 4);
      }
      setFormData(prev => ({ ...prev, [name]: formatted }));
    } else {
      setFormData(prev => ({
        ...prev,
        [name]: type === 'checkbox' ? checked : value
      }));
    }
  };

  const handleSubmit = (e: React.FormEvent) => {
    e.preventDefault();
    
    if (!user?.id) return;

    try {
      const cardData: Omit<PaymentCard, 'id'> = {
        cardNumber: formData.cardNumber,
        cardholderName: formData.cardholderName,
        expiryDate: formData.expiryDate,
        cardType: getCardType(formData.cardNumber),
        isDefault: formData.isDefault
      };

      if (editingCard) {
        customerService.updatePaymentCard(user.id, editingCard.id, cardData);
        toast.success(isRTL ? 'تم تحديث البطاقة بنجاح' : 'Card updated successfully');
      } else {
        customerService.addPaymentCard(user.id, cardData);
        toast.success(isRTL ? 'تم إضافة البطاقة بنجاح' : 'Card added successfully');
      }

      loadPaymentCards();
      resetForm();
    } catch (error) {
      console.error('Error saving payment card:', error);
      toast.error(isRTL ? 'خطأ في حفظ البطاقة' : 'Error saving card');
    }
  };

  const handleEdit = (card: PaymentCard) => {
    setEditingCard(card);
    setFormData({
      cardNumber: card.cardNumber,
      cardholderName: card.cardholderName,
      expiryDate: card.expiryDate,
      cvv: '',
      isDefault: card.isDefault
    });
    setShowAddForm(true);
  };

  const handleDelete = (cardId: string) => {
    if (!user?.id) return;

    if (window.confirm(isRTL ? 'هل أنت متأكد من حذف هذه البطاقة؟' : 'Are you sure you want to delete this card?')) {
      try {
        customerService.deletePaymentCard(user.id, cardId);
        toast.success(isRTL ? 'تم حذف البطاقة بنجاح' : 'Card deleted successfully');
        loadPaymentCards();
      } catch (error) {
        console.error('Error deleting payment card:', error);
        toast.error(isRTL ? 'خطأ في حذف البطاقة' : 'Error deleting card');
      }
    }
  };

  const handleSetDefault = (cardId: string) => {
    if (!user?.id) return;

    try {
      customerService.setDefaultPaymentCard(user.id, cardId);
      toast.success(isRTL ? 'تم تعيين البطاقة الافتراضية' : 'Default card set successfully');
      loadPaymentCards();
    } catch (error) {
      console.error('Error setting default card:', error);
      toast.error(isRTL ? 'خطأ في تعيين البطاقة الافتراضية' : 'Error setting default card');
    }
  };

  const resetForm = () => {
    setFormData({
      cardNumber: '',
      cardholderName: '',
      expiryDate: '',
      cvv: '',
      isDefault: false
    });
    setEditingCard(null);
    setShowAddForm(false);
  };

  const maskCardNumber = (cardNumber: string) => {
    const cleaned = cardNumber.replace(/\s/g, '');
    return '**** **** **** ' + cleaned.slice(-4);
  };

  const getCardIcon = (cardType: string) => {
    switch (cardType) {
      case 'visa':
        return '💳';
      case 'mastercard':
        return '💳';
      case 'amex':
        return '💳';
      default:
        return '💳';
    }
  };

  if (loading) {
    return (
      <div className="space-y-6">
        <div className="flex items-center justify-between">
          <h1 className={`text-2xl font-bold text-gray-900 ${isRTL ? 'font-arabic' : ''}`}>
            {isRTL ? 'بطاقات الدفع' : 'Payment Cards'}
          </h1>
        </div>
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
          {[...Array(3)].map((_, i) => (
            <div key={i} className="bg-white rounded-lg shadow-sm border border-gray-200 p-6 animate-pulse">
              <div className="h-4 bg-gray-200 rounded mb-2"></div>
              <div className="h-8 bg-gray-200 rounded"></div>
            </div>
          ))}
        </div>
      </div>
    );
  }

  return (
    <div className="space-y-6">
      <div className={`flex items-center justify-between ${isRTL ? 'flex-row-reverse' : ''}`}>
        <h1 className={`text-2xl font-bold text-gray-900 ${isRTL ? 'font-arabic' : ''}`}>
          {isRTL ? 'بطاقات الدفع' : 'Payment Cards'}
        </h1>
        <button
          onClick={() => setShowAddForm(true)}
          className={`flex items-center px-4 py-2 bg-black text-white rounded-lg hover:bg-gray-800 transition-colors ${isRTL ? 'flex-row-reverse' : ''}`}
        >
          <Plus className={`h-5 w-5 ${isRTL ? 'ml-2' : 'mr-2'}`} />
          <span className={isRTL ? 'font-arabic' : ''}>{isRTL ? 'إضافة بطاقة' : 'Add Card'}</span>
        </button>
      </div>

      {/* Security Notice */}
      <div className={`bg-blue-50 border border-blue-200 rounded-lg p-4 ${isRTL ? 'text-right' : 'text-left'}`}>
        <div className={`flex items-start ${isRTL ? 'flex-row-reverse' : ''}`}>
          <Shield className={`h-5 w-5 text-blue-600 ${isRTL ? 'ml-3' : 'mr-3'} mt-0.5`} />
          <div>
            <h3 className={`text-sm font-medium text-blue-900 ${isRTL ? 'font-arabic' : ''}`}>
              {isRTL ? 'أمان البيانات' : 'Data Security'}
            </h3>
            <p className={`text-sm text-blue-700 mt-1 ${isRTL ? 'font-arabic' : ''}`}>
              {isRTL 
                ? 'جميع بيانات البطاقات محمية ومشفرة بأعلى معايير الأمان'
                : 'All card data is protected and encrypted with the highest security standards'
              }
            </p>
          </div>
        </div>
      </div>

      {/* Payment Cards Grid */}
      {cards.length === 0 ? (
        <div className={`text-center py-12 ${isRTL ? 'text-right' : 'text-left'}`}>
          <CreditCard className="h-12 w-12 text-gray-400 mx-auto mb-4" />
          <h3 className={`text-lg font-medium text-gray-900 mb-2 ${isRTL ? 'font-arabic' : ''}`}>
            {isRTL ? 'لا توجد بطاقات دفع' : 'No payment cards'}
          </h3>
          <p className={`text-gray-600 mb-6 ${isRTL ? 'font-arabic' : ''}`}>
            {isRTL ? 'أضف بطاقة دفع لتسهيل عملية الشراء' : 'Add a payment card to make checkout easier'}
          </p>
          <button
            onClick={() => setShowAddForm(true)}
            className={`inline-flex items-center px-4 py-2 bg-black text-white rounded-lg hover:bg-gray-800 transition-colors ${isRTL ? 'flex-row-reverse' : ''}`}
          >
            <Plus className={`h-5 w-5 ${isRTL ? 'ml-2' : 'mr-2'}`} />
            <span className={isRTL ? 'font-arabic' : ''}>{isRTL ? 'إضافة بطاقة' : 'Add Card'}</span>
          </button>
        </div>
      ) : (
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
          {cards.map((card) => (
            <div key={card.id} className="bg-white rounded-lg shadow-sm border border-gray-200 overflow-hidden">
              <div className="bg-gradient-to-r from-gray-800 to-black text-white p-6">
                <div className={`flex items-center justify-between mb-4 ${isRTL ? 'flex-row-reverse' : ''}`}>
                  <span className="text-2xl">{getCardIcon(card.cardType)}</span>
                  {card.isDefault && (
                    <span className={`text-xs bg-green-500 text-white px-2 py-1 rounded ${isRTL ? 'font-arabic' : ''}`}>
                      {isRTL ? 'افتراضية' : 'Default'}
                    </span>
                  )}
                </div>
                <div className={`space-y-2 ${isRTL ? 'text-right' : 'text-left'}`}>
                  <p className="text-lg font-mono tracking-wider">{maskCardNumber(card.cardNumber)}</p>
                  <div className={`flex justify-between items-center ${isRTL ? 'flex-row-reverse' : ''}`}>
                    <p className={`text-sm ${isRTL ? 'font-arabic' : ''}`}>{card.cardholderName}</p>
                    <p className="text-sm font-mono">{card.expiryDate}</p>
                  </div>
                </div>
              </div>
              <div className="p-4">
                <div className={`flex items-center space-x-2 ${isRTL ? 'flex-row-reverse space-x-reverse' : ''}`}>
                  {!card.isDefault && (
                    <button
                      onClick={() => handleSetDefault(card.id)}
                      className={`text-sm text-blue-600 hover:text-blue-800 ${isRTL ? 'font-arabic' : ''}`}
                    >
                      {isRTL ? 'تعيين كافتراضية' : 'Set as Default'}
                    </button>
                  )}
                  <button
                    onClick={() => handleEdit(card)}
                    className="p-2 text-gray-600 hover:text-gray-800"
                  >
                    <Edit className="h-4 w-4" />
                  </button>
                  <button
                    onClick={() => handleDelete(card.id)}
                    className="p-2 text-red-600 hover:text-red-800"
                  >
                    <Trash2 className="h-4 w-4" />
                  </button>
                </div>
              </div>
            </div>
          ))}
        </div>
      )}

      {/* Add/Edit Card Modal */}
      {showAddForm && (
        <div className="fixed inset-0 z-50 overflow-y-auto">
          <div className="flex items-center justify-center min-h-screen pt-4 px-4 pb-20 text-center sm:block sm:p-0">
            <div className="fixed inset-0 transition-opacity" aria-hidden="true">
              <div className="absolute inset-0 bg-gray-500 opacity-75" onClick={resetForm}></div>
            </div>

            <div className="inline-block align-bottom bg-white rounded-lg text-left overflow-hidden shadow-xl transform transition-all sm:my-8 sm:align-middle sm:max-w-lg sm:w-full">
              <form onSubmit={handleSubmit}>
                <div className="bg-white px-4 pt-5 pb-4 sm:p-6 sm:pb-4">
                  <h3 className={`text-lg font-medium text-gray-900 mb-4 ${isRTL ? 'font-arabic text-right' : 'text-left'}`}>
                    {editingCard 
                      ? (isRTL ? 'تعديل البطاقة' : 'Edit Card')
                      : (isRTL ? 'إضافة بطاقة جديدة' : 'Add New Card')
                    }
                  </h3>
                  
                  <div className="space-y-4">
                    <div>
                      <label className={`block text-sm font-medium text-gray-700 mb-1 ${isRTL ? 'font-arabic text-right' : 'text-left'}`}>
                        {isRTL ? 'رقم البطاقة' : 'Card Number'}
                      </label>
                      <input
                        type="text"
                        name="cardNumber"
                        value={formData.cardNumber}
                        onChange={handleInputChange}
                        placeholder={isRTL ? '1234 5678 9012 3456' : '1234 5678 9012 3456'}
                        maxLength={19}
                        className={`w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-black focus:border-transparent ${isRTL ? 'text-right font-arabic' : 'text-left'}`}
                        required
                      />
                    </div>

                    <div>
                      <label className={`block text-sm font-medium text-gray-700 mb-1 ${isRTL ? 'font-arabic text-right' : 'text-left'}`}>
                        {isRTL ? 'اسم حامل البطاقة' : 'Cardholder Name'}
                      </label>
                      <input
                        type="text"
                        name="cardholderName"
                        value={formData.cardholderName}
                        onChange={handleInputChange}
                        placeholder={isRTL ? 'أحمد محمد' : 'John Doe'}
                        className={`w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-black focus:border-transparent ${isRTL ? 'text-right font-arabic' : 'text-left'}`}
                        required
                      />
                    </div>

                    <div className="grid grid-cols-2 gap-4">
                      <div>
                        <label className={`block text-sm font-medium text-gray-700 mb-1 ${isRTL ? 'font-arabic text-right' : 'text-left'}`}>
                          {isRTL ? 'تاريخ الانتهاء' : 'Expiry Date'}
                        </label>
                        <input
                          type="text"
                          name="expiryDate"
                          value={formData.expiryDate}
                          onChange={handleInputChange}
                          placeholder="MM/YY"
                          maxLength={5}
                          className={`w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-black focus:border-transparent ${isRTL ? 'text-right' : 'text-left'}`}
                          required
                        />
                      </div>

                      <div>
                        <label className={`block text-sm font-medium text-gray-700 mb-1 ${isRTL ? 'font-arabic text-right' : 'text-left'}`}>
                          CVV
                        </label>
                        <input
                          type="text"
                          name="cvv"
                          value={formData.cvv}
                          onChange={handleInputChange}
                          placeholder="123"
                          maxLength={4}
                          className={`w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-black focus:border-transparent ${isRTL ? 'text-right' : 'text-left'}`}
                          required
                        />
                      </div>
                    </div>

                    <div className={`flex items-center ${isRTL ? 'flex-row-reverse' : ''}`}>
                      <input
                        type="checkbox"
                        name="isDefault"
                        checked={formData.isDefault}
                        onChange={handleInputChange}
                        className={`h-4 w-4 text-black focus:ring-black border-gray-300 rounded ${isRTL ? 'ml-2' : 'mr-2'}`}
                      />
                      <label className={`text-sm text-gray-700 ${isRTL ? 'font-arabic' : ''}`}>
                        {isRTL ? 'تعيين كبطاقة افتراضية' : 'Set as default card'}
                      </label>
                    </div>
                  </div>
                </div>

                <div className={`bg-gray-50 px-4 py-3 sm:px-6 sm:flex sm:flex-row-reverse ${isRTL ? 'sm:flex-row' : ''}`}>
                  <button
                    type="submit"
                    className={`w-full inline-flex justify-center rounded-md border border-transparent shadow-sm px-4 py-2 bg-black text-base font-medium text-white hover:bg-gray-800 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-black sm:ml-3 sm:w-auto sm:text-sm ${isRTL ? 'font-arabic sm:mr-3 sm:ml-0' : ''}`}
                  >
                    {editingCard 
                      ? (isRTL ? 'تحديث' : 'Update')
                      : (isRTL ? 'إضافة' : 'Add')
                    }
                  </button>
                  <button
                    type="button"
                    onClick={resetForm}
                    className={`mt-3 w-full inline-flex justify-center rounded-md border border-gray-300 shadow-sm px-4 py-2 bg-white text-base font-medium text-gray-700 hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-black sm:mt-0 sm:ml-3 sm:w-auto sm:text-sm ${isRTL ? 'font-arabic sm:mr-3 sm:ml-0' : ''}`}
                  >
                    {isRTL ? 'إلغاء' : 'Cancel'}
                  </button>
                </div>
              </form>
            </div>
          </div>
        </div>
      )}
    </div>
  );
};

export default CustomerPaymentCards;
