import React, { useState, useEffect } from 'react';
import { Routes, Route, useNavigate, useLocation } from 'react-router-dom';
import {
  User,
  Package,
  MapPin,
  Settings,
  CreditCard,
  Heart,
  LogOut,
  Menu,
  X,
  ShoppingBag,
  TrendingUp,
  DollarSign
} from 'lucide-react';
import { useAuth } from '../../contexts/AuthContext';
import { useLanguage } from '../../contexts/LanguageContext';

import CustomerOrders from '../../components/customer/CustomerOrders';
import CustomerWishlist from '../../components/customer/CustomerWishlist';
import CustomerAddresses from '../../components/customer/CustomerAddresses';
import CustomerSettings from '../../components/customer/CustomerSettings';
import CustomerPaymentCards from '../../components/customer/CustomerPaymentCards';
import toast from 'react-hot-toast';

interface CustomerStats {
  totalOrders: number;
  totalSpent: number;
  avgOrderValue: number;
  wishlistCount: number;
  addressCount: number;
}

// Overview Dashboard Component
const OverviewDashboard: React.FC<{ stats: CustomerStats; loading: boolean; isRTL: boolean; user: any; navigate: any }> = ({ stats, loading, isRTL, user, navigate }) => {


  return (
    <div className="space-y-6">
      {/* Welcome Section */}
      <div className={`bg-gradient-to-r from-black to-gray-800 text-white rounded-lg p-6 ${isRTL ? 'text-right' : 'text-left'}`}>
        <h1 className={`text-2xl font-bold mb-2 ${isRTL ? 'font-arabic' : ''}`}>
          {isRTL ? `مرحباً، ${user?.firstName || 'عميل'}` : `Welcome, ${user?.firstName || 'Customer'}`}
        </h1>
        <p className={`text-gray-300 ${isRTL ? 'font-arabic' : ''}`}>
          {isRTL ? 'إدارة حسابك وطلباتك من هنا' : 'Manage your account and orders from here'}
        </p>
      </div>

      {/* Statistics Cards */}
      {loading ? (
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
        {[...Array(4)].map((_, i) => (
          <div key={i} className="bg-white rounded-lg shadow-sm border border-gray-200 p-6 animate-pulse">
            <div className="h-4 bg-gray-200 rounded mb-2"></div>
            <div className="h-8 bg-gray-200 rounded"></div>
          </div>
        ))}
      </div>
    ) : (
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
        <div className="bg-white rounded-lg shadow-sm border border-gray-200 p-6">
          <div className={`flex items-center justify-between ${isRTL ? 'flex-row-reverse' : ''}`}>
            <div className={isRTL ? 'text-right' : 'text-left'}>
              <p className={`text-sm text-gray-600 ${isRTL ? 'font-arabic' : ''}`}>
                {isRTL ? 'إجمالي الطلبات' : 'Total Orders'}
              </p>
              <p className="text-2xl font-bold text-gray-900">{stats.totalOrders}</p>
            </div>
            <Package className="h-8 w-8 text-blue-600" />
          </div>
        </div>

        <div className="bg-white rounded-lg shadow-sm border border-gray-200 p-6">
          <div className={`flex items-center justify-between ${isRTL ? 'flex-row-reverse' : ''}`}>
            <div className={isRTL ? 'text-right' : 'text-left'}>
              <p className={`text-sm text-gray-600 ${isRTL ? 'font-arabic' : ''}`}>
                {isRTL ? 'إجمالي المبلغ المنفق' : 'Total Spent'}
              </p>
              <p className="text-2xl font-bold text-gray-900">
                {stats.totalSpent.toLocaleString()} {isRTL ? 'ر.س' : 'SAR'}
              </p>
            </div>
            <DollarSign className="h-8 w-8 text-green-600" />
          </div>
        </div>

        <div className="bg-white rounded-lg shadow-sm border border-gray-200 p-6">
          <div className={`flex items-center justify-between ${isRTL ? 'flex-row-reverse' : ''}`}>
            <div className={isRTL ? 'text-right' : 'text-left'}>
              <p className={`text-sm text-gray-600 ${isRTL ? 'font-arabic' : ''}`}>
                {isRTL ? 'المفضلة' : 'Wishlist Items'}
              </p>
              <p className="text-2xl font-bold text-gray-900">{stats.wishlistCount}</p>
            </div>
            <Heart className="h-8 w-8 text-red-600" />
          </div>
        </div>

        <div className="bg-white rounded-lg shadow-sm border border-gray-200 p-6">
          <div className={`flex items-center justify-between ${isRTL ? 'flex-row-reverse' : ''}`}>
            <div className={isRTL ? 'text-right' : 'text-left'}>
              <p className={`text-sm text-gray-600 ${isRTL ? 'font-arabic' : ''}`}>
                {isRTL ? 'العناوين المحفوظة' : 'Saved Addresses'}
              </p>
              <p className="text-2xl font-bold text-gray-900">{stats.addressCount}</p>
            </div>
            <MapPin className="h-8 w-8 text-purple-600" />
          </div>
        </div>
      </div>
    )}

    {/* Quick Actions */}
    <div className="bg-white rounded-lg shadow-sm border border-gray-200 p-6">
      <h2 className={`text-lg font-semibold text-gray-900 mb-4 ${isRTL ? 'font-arabic text-right' : 'text-left'}`}>
        {isRTL ? 'إجراءات سريعة' : 'Quick Actions'}
      </h2>
      <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
        <button
          onClick={() => navigate('/products')}
          className={`flex items-center p-4 border border-gray-200 rounded-lg hover:bg-gray-50 transition-colors ${isRTL ? 'flex-row-reverse text-right' : 'text-left'}`}
        >
          <ShoppingBag className={`h-6 w-6 text-gray-600 ${isRTL ? 'ml-3' : 'mr-3'}`} />
          <div>
            <p className={`font-medium text-gray-900 ${isRTL ? 'font-arabic' : ''}`}>
              {isRTL ? 'تسوق الآن' : 'Shop Now'}
            </p>
            <p className={`text-sm text-gray-600 ${isRTL ? 'font-arabic' : ''}`}>
              {isRTL ? 'اكتشف منتجاتنا الجديدة' : 'Discover our new products'}
            </p>
          </div>
        </button>

        <button
          onClick={() => navigate('/customer/dashboard/orders')}
          className={`flex items-center p-4 border border-gray-200 rounded-lg hover:bg-gray-50 transition-colors ${isRTL ? 'flex-row-reverse text-right' : 'text-left'}`}
        >
          <Package className={`h-6 w-6 text-gray-600 ${isRTL ? 'ml-3' : 'mr-3'}`} />
          <div>
            <p className={`font-medium text-gray-900 ${isRTL ? 'font-arabic' : ''}`}>
              {isRTL ? 'تتبع الطلبات' : 'Track Orders'}
            </p>
            <p className={`text-sm text-gray-600 ${isRTL ? 'font-arabic' : ''}`}>
              {isRTL ? 'تابع حالة طلباتك' : 'Follow your order status'}
            </p>
          </div>
        </button>

        <button
          onClick={() => navigate('/customer/dashboard/addresses')}
          className={`flex items-center p-4 border border-gray-200 rounded-lg hover:bg-gray-50 transition-colors ${isRTL ? 'flex-row-reverse text-right' : 'text-left'}`}
        >
          <MapPin className={`h-6 w-6 text-gray-600 ${isRTL ? 'ml-3' : 'mr-3'}`} />
          <div>
            <p className={`font-medium text-gray-900 ${isRTL ? 'font-arabic' : ''}`}>
              {isRTL ? 'إدارة العناوين' : 'Manage Addresses'}
            </p>
            <p className={`text-sm text-gray-600 ${isRTL ? 'font-arabic' : ''}`}>
              {isRTL ? 'أضف أو عدل عناوينك' : 'Add or edit your addresses'}
            </p>
          </div>
        </button>
      </div>
    </div>
  </div>
  );
};

const CustomerDashboard: React.FC = () => {
  const { user, logout } = useAuth();
  const { isRTL } = useLanguage();
  const navigate = useNavigate();
  const location = useLocation();
  const [sidebarOpen, setSidebarOpen] = useState(false);
  const [stats, setStats] = useState<CustomerStats>({
    totalOrders: 0,
    totalSpent: 0,
    avgOrderValue: 0,
    wishlistCount: 0,
    addressCount: 0
  });
  const [loading, setLoading] = useState(true);



  // Load customer statistics
  useEffect(() => {
    if (user?.id) {
      loadCustomerStats();
    }
  }, [user?.id]);

  const loadCustomerStats = async () => {
    if (!user?.id) return;

    setLoading(true);
    try {
      // Simulate loading time
      await new Promise(resolve => setTimeout(resolve, 1000));

      // Mock customer statistics with realistic data
      const mockStats: CustomerStats = {
        totalOrders: 12,
        totalSpent: 2450.75,
        avgOrderValue: 204.23,
        wishlistCount: 8,
        addressCount: 3
      };

      setStats(mockStats);
    } catch (error) {
      console.error('Error loading customer stats:', error);
      toast.error(isRTL ? 'خطأ في تحميل البيانات' : 'Error loading data');
    } finally {
      setLoading(false);
    }
  };

  const handleLogout = () => {
    logout();
    navigate('/');
    toast.success(isRTL ? 'تم تسجيل الخروج بنجاح' : 'Logged out successfully');
  };

  const menuItems = [
    {
      id: 'overview',
      label: isRTL ? 'نظرة عامة' : 'Overview',
      icon: TrendingUp,
      path: '/customer/dashboard'
    },
    {
      id: 'orders',
      label: isRTL ? 'طلباتي' : 'My Orders',
      icon: Package,
      path: '/customer/dashboard/orders'
    },
    {
      id: 'wishlist',
      label: isRTL ? 'المفضلة' : 'Wishlist',
      icon: Heart,
      path: '/customer/dashboard/wishlist'
    },
    {
      id: 'addresses',
      label: isRTL ? 'عناويني' : 'My Addresses',
      icon: MapPin,
      path: '/customer/dashboard/addresses'
    },
    {
      id: 'payment-cards',
      label: isRTL ? 'بطاقات الدفع' : 'Payment Cards',
      icon: CreditCard,
      path: '/customer/dashboard/payment-cards'
    },
    {
      id: 'settings',
      label: isRTL ? 'الإعدادات' : 'Settings',
      icon: Settings,
      path: '/customer/dashboard/settings'
    }
  ];

  const isActiveRoute = (path: string) => {
    if (path === '/customer/dashboard') {
      return location.pathname === '/customer/dashboard';
    }
    return location.pathname.startsWith(path);
  };

  const handleNavigation = (path: string) => {
    navigate(path);
    setSidebarOpen(false);
  };

  if (!user) {
    navigate('/login');
    return null;
  }

  return (
    <div className="min-h-screen bg-gray-50">
      {/* Mobile sidebar overlay */}
      {sidebarOpen && (
        <div
          className="fixed inset-0 z-40 bg-black bg-opacity-50 lg:hidden"
          onClick={() => setSidebarOpen(false)}
        />
      )}

      {/* Sidebar */}
      <div className={`
        fixed inset-y-0 z-50 w-64 bg-white shadow-lg transform transition-transform duration-300 ease-in-out lg:translate-x-0 lg:static lg:inset-0
        ${sidebarOpen ? 'translate-x-0' : isRTL ? 'translate-x-full' : '-translate-x-full'}
        ${isRTL ? 'right-0' : 'left-0'}
      `}>
        <div className="flex items-center justify-between h-16 px-6 border-b border-gray-200">
          <h2 className={`text-lg font-semibold text-gray-900 ${isRTL ? 'font-arabic' : ''}`}>
            {isRTL ? 'لوحة العميل' : 'Customer Dashboard'}
          </h2>
          <button
            onClick={() => setSidebarOpen(false)}
            className="lg:hidden p-2 rounded-md text-gray-400 hover:text-gray-600"
          >
            <X className="h-5 w-5" />
          </button>
        </div>

        <nav className="mt-6 px-3">
          <div className="space-y-1">
            {menuItems.map((item) => {
              const Icon = item.icon;
              const isActive = isActiveRoute(item.path);

              return (
                <button
                  key={item.id}
                  onClick={() => handleNavigation(item.path)}
                  className={`
                    w-full flex items-center px-3 py-2 text-sm font-medium rounded-md transition-colors
                    ${isRTL ? 'flex-row-reverse text-right' : 'text-left'}
                    ${isActive
                      ? 'bg-black text-white'
                      : 'text-gray-700 hover:bg-gray-100'
                    }
                  `}
                >
                  <Icon className={`h-5 w-5 ${isRTL ? 'ml-3' : 'mr-3'}`} />
                  <span className={isRTL ? 'font-arabic' : ''}>{item.label}</span>
                </button>
              );
            })}
          </div>

          <div className="mt-8 pt-6 border-t border-gray-200">
            <button
              onClick={handleLogout}
              className={`
                w-full flex items-center px-3 py-2 text-sm font-medium text-red-600 rounded-md hover:bg-red-50 transition-colors
                ${isRTL ? 'flex-row-reverse text-right' : 'text-left'}
              `}
            >
              <LogOut className={`h-5 w-5 ${isRTL ? 'ml-3' : 'mr-3'}`} />
              <span className={isRTL ? 'font-arabic' : ''}>{isRTL ? 'تسجيل الخروج' : 'Logout'}</span>
            </button>
          </div>
        </nav>
      </div>

      {/* Main content */}
      <div className={`lg:${isRTL ? 'pr' : 'pl'}-64`}>
        {/* Top bar */}
        <div className="bg-white shadow-sm border-b border-gray-200">
          <div className="px-4 sm:px-6 lg:px-8">
            <div className={`flex items-center justify-between h-16 ${isRTL ? 'flex-row-reverse' : ''}`}>
              <button
                onClick={() => setSidebarOpen(true)}
                className="lg:hidden p-2 rounded-md text-gray-400 hover:text-gray-600"
              >
                <Menu className="h-6 w-6" />
              </button>

              <div className={`flex items-center ${isRTL ? 'flex-row-reverse' : ''}`}>
                <div className={`flex items-center ${isRTL ? 'flex-row-reverse' : ''}`}>
                  <div className="h-8 w-8 bg-black rounded-full flex items-center justify-center">
                    <User className="h-5 w-5 text-white" />
                  </div>
                  <div className={`${isRTL ? 'mr-3 text-right' : 'ml-3 text-left'}`}>
                    <p className={`text-sm font-medium text-gray-900 ${isRTL ? 'font-arabic' : ''}`}>
                      {user.firstName} {user.lastName}
                    </p>
                    <p className="text-xs text-gray-500">{user.email}</p>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>

        {/* Page content */}
        <main className="p-4 sm:p-6 lg:p-8">
          <Routes>
            <Route path="/" element={<OverviewDashboard stats={stats} loading={loading} isRTL={isRTL} user={user} navigate={navigate} />} />
            <Route path="/orders" element={<CustomerOrders />} />
            <Route path="/wishlist" element={<CustomerWishlist />} />
            <Route path="/addresses" element={<CustomerAddresses />} />
            <Route path="/payment-cards" element={<CustomerPaymentCards />} />
            <Route path="/settings" element={<CustomerSettings />} />
          </Routes>
        </main>
      </div>
    </div>
  );
};

export default CustomerDashboard;