import { dataService, CustomerData, WishlistItem, Address, PaymentCard, Order } from './dataService';
import { User } from '../types';

export class CustomerService {
  // Get customer profile
  getCustomerProfile(userId: string): Partial<User> {
    const customerData = dataService.getCustomerData(userId);
    return customerData.profile;
  }

  // Update customer profile
  updateCustomerProfile(userId: string, profileData: Partial<User>): void {
    const customerData = dataService.getCustomerData(userId);
    customerData.profile = { ...customerData.profile, ...profileData };
    dataService.updateCustomerData(userId, { profile: customerData.profile });
  }

  // Get customer wishlist
  getCustomerWishlist(userId: string): WishlistItem[] {
    const customerData = dataService.getCustomerData(userId);
    return customerData.wishlist;
  }

  // Alias for getCustomerWishlist
  getWishlist(userId: string): WishlistItem[] {
    return this.getCustomerWishlist(userId);
  }

  // Add to wishlist
  addToWishlist(userId: string, item: WishlistItem): void {
    dataService.addToWishlist(userId, item);
  }

  // Remove from wishlist
  removeFromWishlist(userId: string, productId: string): boolean {
    try {
      dataService.removeFromWishlist(userId, productId);
      return true;
    } catch (error) {
      console.error('Error removing from wishlist:', error);
      return false;
    }
  }

  // Clear wishlist
  clearWishlist(userId: string): boolean {
    try {
      const customerData = dataService.getCustomerData(userId);
      customerData.wishlist = [];
      dataService.updateCustomerData(userId, customerData);
      return true;
    } catch (error) {
      console.error('Error clearing wishlist:', error);
      return false;
    }
  }

  // Check if product is in wishlist
  isInWishlist(userId: string, productId: string): boolean {
    const wishlist = this.getCustomerWishlist(userId);
    return wishlist.some(item => item.productId === productId);
  }

  // Get customer addresses
  getCustomerAddresses(userId: string): Address[] {
    const customerData = dataService.getCustomerData(userId);
    return customerData.addresses;
  }

  // Add address
  addAddress(userId: string, address: Omit<Address, 'id'>): Address {
    const newAddress: Address = {
      ...address,
      id: this.generateId()
    };
    
    dataService.addAddress(userId, newAddress);
    return newAddress;
  }

  // Update address
  updateAddress(userId: string, addressId: string, updates: Partial<Address>): boolean {
    const addresses = this.getCustomerAddresses(userId);
    const addressExists = addresses.some(addr => addr.id === addressId);
    
    if (!addressExists) return false;

    dataService.updateAddress(userId, addressId, updates);
    return true;
  }

  // Delete address
  deleteAddress(userId: string, addressId: string): boolean {
    const addresses = this.getCustomerAddresses(userId);
    const addressExists = addresses.some(addr => addr.id === addressId);
    
    if (!addressExists) return false;

    dataService.deleteAddress(userId, addressId);
    return true;
  }

  // Set default address
  setDefaultAddress(userId: string, addressId: string): boolean {
    const addresses = this.getCustomerAddresses(userId);
    const targetAddress = addresses.find(addr => addr.id === addressId);
    
    if (!targetAddress) return false;

    // Remove default from all addresses
    addresses.forEach(addr => {
      if (addr.id !== addressId) {
        dataService.updateAddress(userId, addr.id, { isDefault: false });
      }
    });

    // Set new default
    dataService.updateAddress(userId, addressId, { isDefault: true });
    return true;
  }

  // Get default address
  getDefaultAddress(userId: string): Address | null {
    const addresses = this.getCustomerAddresses(userId);
    return addresses.find(addr => addr.isDefault) || null;
  }

  // Get customer settings
  getCustomerSettings(userId: string) {
    const customerData = dataService.getCustomerData(userId);
    return customerData.settings;
  }

  // Update customer settings
  updateCustomerSettings(userId: string, settings: Partial<CustomerData['settings']>): void {
    const customerData = dataService.getCustomerData(userId);
    customerData.settings = { ...customerData.settings, ...settings };
    dataService.updateCustomerData(userId, { settings: customerData.settings });
  }

  // Update notification preferences
  updateNotificationPreferences(userId: string, notifications: Partial<CustomerData['settings']['notifications']>): void {
    const customerData = dataService.getCustomerData(userId);
    customerData.settings.notifications = { 
      ...customerData.settings.notifications, 
      ...notifications 
    };
    dataService.updateCustomerData(userId, { settings: customerData.settings });
  }

  // Update language and currency preferences
  updatePreferences(userId: string, preferences: Partial<CustomerData['settings']['preferences']>): void {
    const customerData = dataService.getCustomerData(userId);
    customerData.settings.preferences = { 
      ...customerData.settings.preferences, 
      ...preferences 
    };
    dataService.updateCustomerData(userId, { settings: customerData.settings });
  }

  // Get customer statistics
  getCustomerStats(userId: string) {
    const customerData = dataService.getCustomerData(userId);
    const orders = customerData.orders;
    
    const totalOrders = orders.length;
    const totalSpent = orders.reduce((sum, order) => sum + order.total, 0);
    const avgOrderValue = totalOrders > 0 ? totalSpent / totalOrders : 0;
    
    const statusCounts = {
      pending: orders.filter(o => o.status === 'pending').length,
      processing: orders.filter(o => o.status === 'processing').length,
      shipped: orders.filter(o => o.status === 'shipped').length,
      delivered: orders.filter(o => o.status === 'delivered').length,
      cancelled: orders.filter(o => o.status === 'cancelled').length
    };

    const recentOrders = orders
      .sort((a, b) => new Date(b.createdAt).getTime() - new Date(a.createdAt).getTime())
      .slice(0, 5);

    return {
      totalOrders,
      totalSpent,
      avgOrderValue,
      statusCounts,
      recentOrders,
      wishlistCount: customerData.wishlist.length,
      addressCount: customerData.addresses.length
    };
  }

  // Get all customers (admin view)
  getAllCustomers(): Array<{
    id: string;
    profile: Partial<User>;
    stats: {
      totalOrders: number;
      totalSpent: number;
      lastOrderDate?: string;
    };
  }> {
    const allData = dataService.getAllData();
    const customers = Object.keys(allData.customers).map(customerId => {
      const customerData = allData.customers[customerId];
      const orders = customerData.orders;
      
      const totalOrders = orders.length;
      const totalSpent = orders.reduce((sum, order) => sum + order.total, 0);
      const lastOrder = orders.sort((a, b) => 
        new Date(b.createdAt).getTime() - new Date(a.createdAt).getTime()
      )[0];

      return {
        id: customerId,
        profile: customerData.profile,
        stats: {
          totalOrders,
          totalSpent,
          lastOrderDate: lastOrder?.createdAt
        }
      };
    });

    return customers.sort((a, b) => b.stats.totalSpent - a.stats.totalSpent);
  }

  // Search customers (admin view)
  searchCustomers(query: string) {
    const allCustomers = this.getAllCustomers();
    const searchTerm = query.toLowerCase();
    
    return allCustomers.filter(customer => {
      const profile = customer.profile;
      return (
        profile.firstName?.toLowerCase().includes(searchTerm) ||
        profile.lastName?.toLowerCase().includes(searchTerm) ||
        profile.email?.toLowerCase().includes(searchTerm) ||
        profile.phone?.toLowerCase().includes(searchTerm)
      );
    });
  }

  // Get customer details (admin view)
  getCustomerDetails(customerId: string) {
    const customerData = dataService.getCustomerData(customerId);
    const stats = this.getCustomerStats(customerId);
    
    return {
      profile: customerData.profile,
      orders: customerData.orders,
      wishlist: customerData.wishlist,
      addresses: customerData.addresses,
      settings: customerData.settings,
      stats
    };
  }

  // Validate customer data
  validateCustomerProfile(profile: Partial<User>): { isValid: boolean; errors: string[] } {
    const errors: string[] = [];

    if (profile.email && !/^[^\s@]+@[^\s@]+\.[^\s@]+$/.test(profile.email)) {
      errors.push('Invalid email format');
    }

    if (profile.phone && !/^\+?[\d\s-()]+$/.test(profile.phone)) {
      errors.push('Invalid phone number format');
    }

    if (profile.firstName && profile.firstName.trim().length < 2) {
      errors.push('First name must be at least 2 characters');
    }

    if (profile.lastName && profile.lastName.trim().length < 2) {
      errors.push('Last name must be at least 2 characters');
    }

    return {
      isValid: errors.length === 0,
      errors
    };
  }

  // Validate address data
  validateAddress(address: Partial<Address>): { isValid: boolean; errors: string[] } {
    const errors: string[] = [];

    if (!address.firstName || address.firstName.trim().length === 0) {
      errors.push('First name is required');
    }

    if (!address.lastName || address.lastName.trim().length === 0) {
      errors.push('Last name is required');
    }

    if (!address.phone || address.phone.trim().length === 0) {
      errors.push('Phone number is required');
    }

    if (!address.street || address.street.trim().length === 0) {
      errors.push('Street address is required');
    }

    if (!address.city || address.city.trim().length === 0) {
      errors.push('City is required');
    }

    if (!address.postalCode || address.postalCode.trim().length === 0) {
      errors.push('Postal code is required');
    }

    return {
      isValid: errors.length === 0,
      errors
    };
  }

  // Payment Cards Management
  getCustomerPaymentCards(userId: string) {
    const customerData = dataService.getCustomerData(userId);
    return customerData.paymentCards || [];
  }

  addPaymentCard(userId: string, cardData: Omit<import('./dataService').PaymentCard, 'id' | 'createdAt' | 'updatedAt'>) {
    const customerData = dataService.getCustomerData(userId);
    const newCard = {
      id: this.generateId(),
      ...cardData,
      createdAt: new Date().toISOString()
    };

    // If this is the first card or marked as default, make it default
    if (!customerData.paymentCards || customerData.paymentCards.length === 0 || cardData.isDefault) {
      // Set all other cards as non-default
      if (customerData.paymentCards) {
        customerData.paymentCards.forEach(card => card.isDefault = false);
      }
      newCard.isDefault = true;
    }

    customerData.paymentCards = customerData.paymentCards || [];
    customerData.paymentCards.push(newCard);
    dataService.updateCustomerData(userId, { paymentCards: customerData.paymentCards });

    return newCard;
  }

  updatePaymentCard(userId: string, cardId: string, cardData: Omit<import('./dataService').PaymentCard, 'id' | 'createdAt' | 'updatedAt'>) {
    const customerData = dataService.getCustomerData(userId);
    const cardIndex = customerData.paymentCards?.findIndex(card => card.id === cardId);

    if (cardIndex === -1 || cardIndex === undefined) {
      throw new Error('Payment card not found');
    }

    // If setting as default, remove default from other cards
    if (cardData.isDefault) {
      customerData.paymentCards.forEach(card => card.isDefault = false);
    }

    customerData.paymentCards[cardIndex] = {
      ...customerData.paymentCards[cardIndex],
      ...cardData,
      updatedAt: new Date().toISOString()
    };

    dataService.updateCustomerData(userId, { paymentCards: customerData.paymentCards });
    return customerData.paymentCards[cardIndex];
  }

  deletePaymentCard(userId: string, cardId: string) {
    const customerData = dataService.getCustomerData(userId);
    const cardIndex = customerData.paymentCards?.findIndex(card => card.id === cardId);

    if (cardIndex === -1 || cardIndex === undefined) {
      throw new Error('Payment card not found');
    }

    const deletedCard = customerData.paymentCards[cardIndex];
    customerData.paymentCards.splice(cardIndex, 1);

    // If deleted card was default and there are other cards, make the first one default
    if (deletedCard.isDefault && customerData.paymentCards.length > 0) {
      customerData.paymentCards[0].isDefault = true;
    }

    dataService.updateCustomerData(userId, { paymentCards: customerData.paymentCards });
    return deletedCard;
  }

  setDefaultPaymentCard(userId: string, cardId: string) {
    const customerData = dataService.getCustomerData(userId);
    const cardIndex = customerData.paymentCards?.findIndex(card => card.id === cardId);

    if (cardIndex === -1 || cardIndex === undefined) {
      throw new Error('Payment card not found');
    }

    // Set all cards as non-default
    customerData.paymentCards.forEach(card => card.isDefault = false);

    // Set the selected card as default
    customerData.paymentCards[cardIndex].isDefault = true;

    dataService.updateCustomerData(userId, { paymentCards: customerData.paymentCards });
    return customerData.paymentCards[cardIndex];
  }

  // Initialize customer with comprehensive mock data
  initializeCustomerWithMockData(userId: string): void {
    const customerData = dataService.getCustomerData(userId);

    // Only initialize if customer has no data
    if (customerData.orders.length === 0 && customerData.wishlist.length === 0 &&
        customerData.addresses.length === 0 && customerData.paymentCards.length === 0) {

      // Create mock orders
      const mockOrders = this.createMockOrders();

      // Create mock wishlist
      const mockWishlist = this.createMockWishlist();

      // Create mock addresses
      const mockAddresses = this.createMockAddresses();

      // Create mock payment cards
      const mockPaymentCards = this.createMockPaymentCards();

      // Update customer data with all mock data
      dataService.updateCustomerData(userId, {
        orders: mockOrders,
        wishlist: mockWishlist,
        addresses: mockAddresses,
        paymentCards: mockPaymentCards
      });
    }
  }

  private createMockOrders() {
    return [
      {
        id: 'order-001',
        orderNumber: 'ORD-2024-001',
        customerId: 'customer-001',
        customerName: 'أحمد محمد',
        customerEmail: '<EMAIL>',
        status: 'delivered' as const,
        total: 459.97,
        subtotal: 399.97,
        tax: 60.00,
        shipping: 0,
        items: [
          {
            id: 'item-001',
            productId: 'prod-001',
            name: 'White Cotton Shirt',
            nameAr: 'قميص قطني أبيض',
            price: 129.99,
            quantity: 2,
            image: '/images/products/shirt-white.jpg',
            variant: {
              size: 'L',
              color: 'أبيض'
            }
          },
          {
            id: 'item-002',
            productId: 'prod-002',
            name: 'Blue Jeans',
            nameAr: 'بنطلون جينز أزرق',
            price: 269.98,
            quantity: 1,
            image: '/images/products/jeans-blue.jpg',
            variant: {
              size: '32',
              color: 'أزرق'
            }
          }
        ],
        shippingAddress: {
          id: 'addr-001',
          type: 'home' as const,
          firstName: 'أحمد',
          lastName: 'محمد',
          street: 'شارع الملك فهد، حي النخيل',
          city: 'الرياض',
          state: 'منطقة الرياض',
          postalCode: '12345',
          country: 'المملكة العربية السعودية',
          phone: '+966501234567',
          isDefault: true
        },
        paymentMethod: 'credit_card',
        createdAt: '2024-01-15T10:30:00Z',
        updatedAt: '2024-01-18T14:20:00Z'
      },
      {
        id: 'order-002',
        orderNumber: 'ORD-2024-002',
        customerId: 'customer-001',
        customerName: 'أحمد محمد',
        customerEmail: '<EMAIL>',
        status: 'shipped' as const,
        total: 234.99,
        subtotal: 199.99,
        tax: 30.00,
        shipping: 5.00,
        items: [
          {
            id: 'item-003',
            productId: 'prod-003',
            name: 'Black Winter Jacket',
            nameAr: 'جاكيت شتوي أسود',
            price: 199.99,
            quantity: 1,
            image: '/images/products/jacket-black.jpg',
            variant: {
              size: 'M',
              color: 'أسود'
            }
          }
        ],
        shippingAddress: {
          id: 'addr-001',
          type: 'home' as const,
          firstName: 'أحمد',
          lastName: 'محمد',
          street: 'شارع الملك فهد، حي النخيل',
          city: 'الرياض',
          state: 'منطقة الرياض',
          postalCode: '12345',
          country: 'المملكة العربية السعودية',
          phone: '+966501234567',
          isDefault: true
        },
        paymentMethod: 'credit_card',
        createdAt: '2024-01-20T09:15:00Z',
        updatedAt: '2024-01-22T11:30:00Z'
      },
      {
        id: 'order-003',
        orderNumber: 'ORD-2024-003',
        customerId: 'customer-001',
        customerName: 'أحمد محمد',
        customerEmail: '<EMAIL>',
        status: 'processing' as const,
        total: 159.99,
        subtotal: 129.99,
        tax: 19.50,
        shipping: 10.50,
        items: [
          {
            id: 'item-004',
            productId: 'prod-004',
            name: 'White Sneakers',
            nameAr: 'حذاء رياضي أبيض',
            price: 129.99,
            quantity: 1,
            image: '/images/products/sneakers-white.jpg',
            variant: {
              size: '42',
              color: 'أبيض'
            }
          }
        ],
        shippingAddress: {
          id: 'addr-001',
          type: 'home' as const,
          firstName: 'أحمد',
          lastName: 'محمد',
          street: 'شارع الملك فهد، حي النخيل',
          city: 'الرياض',
          state: 'منطقة الرياض',
          postalCode: '12345',
          country: 'المملكة العربية السعودية',
          phone: '+966501234567',
          isDefault: true
        },
        paymentMethod: 'cash_on_delivery',
        createdAt: '2024-01-25T11:00:00Z',
        updatedAt: '2024-01-25T11:00:00Z'
      },
      {
        id: 'order-004',
        orderNumber: 'ORD-2024-004',
        customerId: 'customer-001',
        customerName: 'أحمد محمد',
        customerEmail: '<EMAIL>',
        status: 'pending' as const,
        total: 89.99,
        subtotal: 79.99,
        tax: 10.00,
        shipping: 0,
        items: [
          {
            id: 'item-005',
            productId: 'prod-005',
            name: 'Summer Hat',
            nameAr: 'قبعة صيفية',
            price: 79.99,
            quantity: 1,
            image: '/images/products/hat-beige.jpg',
            variant: {
              size: 'One Size',
              color: 'بيج'
            }
          }
        ],
        shippingAddress: {
          id: 'addr-001',
          type: 'home' as const,
          firstName: 'أحمد',
          lastName: 'محمد',
          street: 'شارع الملك فهد، حي النخيل',
          city: 'الرياض',
          state: 'منطقة الرياض',
          postalCode: '12345',
          country: 'المملكة العربية السعودية',
          phone: '+966501234567',
          isDefault: true
        },
        paymentMethod: 'credit_card',
        createdAt: '2024-01-28T14:20:00Z',
        updatedAt: '2024-01-28T14:20:00Z'
      }
    ];
  }

  private createMockWishlist(): WishlistItem[] {
    return [
      {
        id: 'wish-001',
        productId: 'prod-006',
        name: 'Blue Summer Dress',
        nameAr: 'فستان صيفي أزرق',
        price: 189.99,
        originalPrice: 229.99,
        image: '/images/products/dress-blue.jpg',
        category: 'dresses',
        inStock: true,
        addedDate: '2024-01-20T10:00:00Z'
      },
      {
        id: 'wish-002',
        productId: 'prod-007',
        name: 'Leather Handbag',
        nameAr: 'حقيبة يد جلدية',
        price: 299.99,
        originalPrice: 299.99,
        image: '/images/products/handbag-brown.jpg',
        category: 'accessories',
        inStock: true,
        addedDate: '2024-01-18T15:30:00Z'
      },
      {
        id: 'wish-003',
        productId: 'prod-008',
        name: 'Smart Watch',
        nameAr: 'ساعة ذكية',
        price: 599.99,
        originalPrice: 699.99,
        image: '/images/products/smartwatch-black.jpg',
        category: 'electronics',
        inStock: false,
        addedDate: '2024-01-15T09:45:00Z'
      },
      {
        id: 'wish-004',
        productId: 'prod-009',
        name: 'Sunglasses',
        nameAr: 'نظارة شمسية',
        price: 149.99,
        originalPrice: 179.99,
        image: '/images/products/sunglasses-black.jpg',
        category: 'accessories',
        inStock: true,
        addedDate: '2024-01-12T12:20:00Z'
      },
      {
        id: 'wish-005',
        productId: 'prod-010',
        name: 'High Heel Shoes',
        nameAr: 'حذاء كعب عالي',
        price: 249.99,
        originalPrice: 249.99,
        image: '/images/products/heels-red.jpg',
        category: 'shoes',
        inStock: true,
        addedDate: '2024-01-10T16:15:00Z'
      }
    ];
  }

  private createMockAddresses(): Address[] {
    return [
      {
        id: 'addr-001',
        type: 'home',
        firstName: 'أحمد',
        lastName: 'محمد',
        phone: '+966501234567',
        street: 'شارع الملك فهد، حي النخيل، مبنى 123',
        city: 'الرياض',
        state: 'منطقة الرياض',
        postalCode: '12345',
        country: 'المملكة العربية السعودية',
        isDefault: true
      },
      {
        id: 'addr-002',
        type: 'work',
        firstName: 'أحمد',
        lastName: 'محمد',
        phone: '+966501234567',
        street: 'طريق الملك عبدالعزيز، حي العليا، برج الأعمال',
        city: 'الرياض',
        state: 'منطقة الرياض',
        postalCode: '11564',
        country: 'المملكة العربية السعودية',
        isDefault: false
      },
      {
        id: 'addr-003',
        type: 'other',
        firstName: 'فاطمة',
        lastName: 'أحمد',
        phone: '+966509876543',
        street: 'شارع التحلية، حي السليمانية',
        city: 'الرياض',
        state: 'منطقة الرياض',
        postalCode: '12244',
        country: 'المملكة العربية السعودية',
        isDefault: false
      }
    ];
  }

  private createMockPaymentCards(): PaymentCard[] {
    return [
      {
        id: 'card-001',
        cardNumber: '4532 1234 5678 9012',
        cardholderName: 'أحمد محمد',
        expiryDate: '12/26',
        cardType: 'visa',
        isDefault: true,
        createdAt: '2024-01-10T10:00:00Z'
      },
      {
        id: 'card-002',
        cardNumber: '5555 4444 3333 2222',
        cardholderName: 'أحمد محمد',
        expiryDate: '08/25',
        cardType: 'mastercard',
        isDefault: false,
        createdAt: '2024-01-05T15:30:00Z'
      }
    ];
  }

  // Generate unique ID
  private generateId(): string {
    return Date.now().toString(36) + Math.random().toString(36).substring(2);
  }
}

export const customerService = new CustomerService();
