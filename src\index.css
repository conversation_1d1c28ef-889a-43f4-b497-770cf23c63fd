@import url('https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700&display=swap');
@import url('https://fonts.googleapis.com/css2?family=Cairo:wght@300;400;500;600;700&display=swap');

@tailwind base;
@tailwind components;
@tailwind utilities;

* {
  margin: 0;
  padding: 0;
  box-sizing: border-box;
}

body {
  font-family: 'Inter', -apple-system, BlinkMacSystemFont, 'Segoe UI', 'Roboto', 'Oxygen',
    'Ubuntu', 'Cantarell', 'Fira Sans', 'Droid Sans', 'Helvetica Neue',
    sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  line-height: 1.5;
  color: #1f2937;
  background-color: #ffffff;
}

/* Arabic font support */
body.rtl {
  font-family: 'Cairo', 'Inter', -apple-system, BlinkMacSystemFont, 'Segoe UI', 'Robot<PERSON>', 'Oxygen',
    'Ubuntu', 'Cantarell', '<PERSON>ra Sans', 'Droid Sans', 'Helvetica Neue',
    sans-serif;
}

.font-arabic {
  font-family: 'Cairo', 'Inter', -apple-system, BlinkMacSystemFont, 'Segoe UI', 'Roboto', 'Oxygen',
    'Ubuntu', 'Cantarell', 'Fira Sans', 'Droid Sans', 'Helvetica Neue',
    sans-serif;
}

/* RTL specific styles */
[dir="rtl"] {
  text-align: right;
}

[dir="ltr"] {
  text-align: left;
}

/* RTL utilities */
.rtl .space-x-reverse > :not([hidden]) ~ :not([hidden]) {
  --tw-space-x-reverse: 1;
}

.rtl .divide-x-reverse > :not([hidden]) ~ :not([hidden]) {
  --tw-divide-x-reverse: 1;
}

/* Header spacing fixes */
.header-logo {
  margin-inline-end: 2rem; /* 32px spacing after logo in both directions */
}

.header-nav {
  margin-inline-start: auto; /* Push navigation to the opposite side */
  margin-inline-end: 2rem; /* Space before search/icons */
}

/* Arabic text improvements */
.rtl {
  font-feature-settings: "liga" 1, "kern" 1;
  text-rendering: optimizeLegibility;
}

/* Better spacing for Arabic text */
.rtl h1, .rtl h2, .rtl h3, .rtl h4, .rtl h5, .rtl h6 {
  letter-spacing: 0.025em;
}

/* Responsive Grid Improvements */
@media (max-width: 640px) {
  .grid-responsive {
    grid-template-columns: 1fr;
    gap: 1rem;
  }
}

@media (min-width: 641px) and (max-width: 1024px) {
  .grid-responsive {
    grid-template-columns: repeat(2, 1fr);
    gap: 1.5rem;
  }
}

@media (min-width: 1025px) {
  .grid-responsive {
    grid-template-columns: repeat(4, 1fr);
    gap: 2rem;
  }
}

/* Modal Responsive */
@media (max-width: 640px) {
  .modal-content {
    margin: 1rem;
    max-width: calc(100vw - 2rem);
  }
}

/* RTL-aware flex utilities */
.flex-rtl {
  display: flex;
}

[dir="rtl"] .flex-rtl {
  flex-direction: row-reverse;
}

/* Navigation spacing */
.nav-spacing > * + * {
  margin-inline-start: 2rem;
}

code {
  font-family: source-code-pro, Menlo, Monaco, Consolas, 'Courier New',
    monospace;
}

/* Custom scrollbar */
::-webkit-scrollbar {
  width: 8px;
}

::-webkit-scrollbar-track {
  background: #f1f5f9;
}

::-webkit-scrollbar-thumb {
  background: #cbd5e1;
  border-radius: 4px;
}

::-webkit-scrollbar-thumb:hover {
  background: #94a3b8;
}

/* Focus styles */
.focus-ring {
  @apply focus:outline-none focus:ring-2 focus:ring-black focus:ring-offset-2;
}

/* Button animations */
.btn-hover {
  @apply transition-all duration-200 ease-in-out;
}

/* Loading animation */
@keyframes spin {
  to {
    transform: rotate(360deg);
  }
}

.animate-spin {
  animation: spin 1s linear infinite;
}

/* Fade in animation */
@keyframes fadeIn {
  from {
    opacity: 0;
    transform: translateY(10px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

.animate-fade-in {
  animation: fadeIn 0.3s ease-out;
}

/* Card hover effects */
.card-hover {
  @apply transition-all duration-300 ease-in-out hover:shadow-lg hover:-translate-y-1;
}

/* Image hover effects */
.image-hover {
  @apply transition-transform duration-300 ease-in-out hover:scale-105;
}

/* Custom utilities */
.text-balance {
  text-wrap: balance;
}

.container-custom {
  @apply max-w-7xl mx-auto px-4 sm:px-6 lg:px-8;
}

/* Form styles */
.form-input {
  @apply w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm placeholder-gray-400 focus:outline-none focus:ring-2 focus:ring-black focus:border-black;
}

.form-label {
  @apply block text-sm font-medium text-gray-700 mb-1;
}

.form-error {
  @apply text-red-600 text-sm mt-1;
}

/* Button variants */
.btn-primary {
  @apply bg-black text-white hover:bg-gray-800 focus:ring-black;
}

.btn-secondary {
  @apply bg-gray-100 text-gray-900 hover:bg-gray-200 focus:ring-gray-500;
}

.btn-outline {
  @apply border border-gray-300 text-gray-700 hover:bg-gray-50 focus:ring-gray-500;
}

.btn-ghost {
  @apply text-gray-700 hover:bg-gray-100 focus:ring-gray-500;
}

/* Loading states */
.loading {
  @apply opacity-50 cursor-not-allowed;
}

/* Mobile menu animation */
.mobile-menu-enter {
  @apply opacity-0 scale-95;
}

.mobile-menu-enter-active {
  @apply opacity-100 scale-100 transition-all duration-200 ease-out;
}

.mobile-menu-exit {
  @apply opacity-100 scale-100;
}

.mobile-menu-exit-active {
  @apply opacity-0 scale-95 transition-all duration-150 ease-in;
}