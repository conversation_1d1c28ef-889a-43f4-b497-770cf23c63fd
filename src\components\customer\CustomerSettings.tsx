import React, { useState, useEffect } from 'react';
import { <PERSON>ting<PERSON>, <PERSON>, Lock, Eye, EyeOff } from 'lucide-react';
import { useLanguage } from '../../contexts/LanguageContext';
import { useAuth } from '../../contexts/AuthContext';
import { CustomerService } from '../../services/customerService';
import toast from 'react-hot-toast';


const CustomerSettings: React.FC = () => {
  const { isRTL, language, changeLanguage } = useLanguage();
  const { user } = useAuth();
  const [activeTab, setActiveTab] = useState('preferences');
  const [showCurrentPassword, setShowCurrentPassword] = useState(false);
  const [showNewPassword, setShowNewPassword] = useState(false);
  const [showConfirmPassword, setShowConfirmPassword] = useState(false);
  const [, setLoading] = useState(true);

  const customerService = new CustomerService();

  const tabs = [
    { id: 'preferences', label: isRTL ? 'التفضيلات' : 'Preferences', icon: Settings },
    { id: 'notifications', label: isRTL ? 'الإشعارات' : 'Notifications', icon: Bell },
    { id: 'security', label: isRTL ? 'الأمان' : 'Security', icon: Lock }
  ];

  const [settings, setSettings] = useState({
    preferences: {
      language: language,
      currency: 'SAR',
      timezone: 'Asia/Riyadh'
    },
    notifications: {
      orderUpdates: true,
      promotions: true,
      newsletter: false,
      smsNotifications: true,
      emailNotifications: true
    },
    security: {
      currentPassword: '',
      newPassword: '',
      confirmPassword: ''
    }
  });

  // Load customer settings on component mount
  useEffect(() => {
    if (user?.id) {
      loadSettings();
    }
  }, [user?.id]);

  const loadSettings = () => {
    if (!user?.id) return;

    setLoading(true);
    try {
      const customerSettings = customerService.getCustomerSettings(user.id);
      setSettings(prev => ({
        ...prev,
        preferences: {
          ...prev.preferences,
          ...customerSettings.preferences
        },
        notifications: {
          ...prev.notifications,
          ...customerSettings.notifications
        }
      }));
    } catch (error) {
      console.error('Error loading settings:', error);
    } finally {
      setLoading(false);
    }
  };

  const handleSave = () => {
    if (!user?.id) return;

    try {
      customerService.updateCustomerSettings(user.id, {
        preferences: settings.preferences,
        notifications: settings.notifications
      });
      toast.success(isRTL ? 'تم حفظ الإعدادات بنجاح' : 'Settings saved successfully');
    } catch (error) {
      console.error('Error saving settings:', error);
      toast.error(isRTL ? 'حدث خطأ أثناء حفظ الإعدادات' : 'Error saving settings');
    }
  };

  const handlePasswordChange = () => {
    if (!user?.id) return;

    if (settings.security.newPassword !== settings.security.confirmPassword) {
      toast.error(isRTL ? 'كلمات المرور غير متطابقة' : 'Passwords do not match');
      return;
    }

    if (settings.security.newPassword.length < 6) {
      toast.error(isRTL ? 'كلمة المرور يجب أن تكون 6 أحرف على الأقل' : 'Password must be at least 6 characters');
      return;
    }

    try {
      // In a real app, you would validate the current password and update it
      toast.success(isRTL ? 'تم تغيير كلمة المرور بنجاح' : 'Password changed successfully');
      setSettings(prev => ({
        ...prev,
        security: {
          currentPassword: '',
          newPassword: '',
          confirmPassword: ''
        }
      }));
    } catch (error) {
      console.error('Error changing password:', error);
      toast.error(isRTL ? 'حدث خطأ أثناء تغيير كلمة المرور' : 'Error changing password');
    }
  };

  const updateSetting = (category: string, key: string, value: any) => {
    setSettings(prev => ({
      ...prev,
      [category]: {
        ...prev[category as keyof typeof prev],
        [key]: value
      }
    }));

    // Handle language change immediately
    if (category === 'preferences' && key === 'language') {
      changeLanguage(value);
    }
  };

  return (
    <div className="space-y-6">
      {/* Header */}
      <div>
        <h1 className={`text-3xl font-bold text-gray-900 ${isRTL ? 'font-arabic' : ''}`}>
          {isRTL ? 'إعدادات الحساب' : 'Account Settings'}
        </h1>
        <p className={`mt-2 text-gray-600 ${isRTL ? 'font-arabic' : ''}`}>
          {isRTL ? 'إدارة تفضيلاتك وإعدادات الأمان' : 'Manage your preferences and security settings'}
        </p>
      </div>

      <div className="bg-white rounded-lg shadow-sm border border-gray-200">
        {/* Tabs */}
        <div className="border-b border-gray-200">
          <nav className={`flex space-x-8 ${isRTL ? 'space-x-reverse' : ''} px-6`}>
            {tabs.map((tab) => {
              const Icon = tab.icon;
              return (
                <button
                  key={tab.id}
                  onClick={() => setActiveTab(tab.id)}
                  className={`
                    flex items-center py-4 px-1 border-b-2 font-medium text-sm transition-colors
                    ${activeTab === tab.id
                      ? 'border-black text-black'
                      : 'border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300'
                    }
                    ${isRTL ? 'flex-row-reverse' : ''}
                  `}
                >
                  <Icon className={`h-5 w-5 ${isRTL ? 'ml-2' : 'mr-2'}`} />
                  <span className={isRTL ? 'font-arabic' : ''}>{tab.label}</span>
                </button>
              );
            })}
          </nav>
        </div>

        {/* Tab content */}
        <div className="p-6">
          {activeTab === 'preferences' && (
            <div className="space-y-6">
              <h3 className={`text-lg font-semibold text-gray-900 ${isRTL ? 'font-arabic' : ''}`}>
                التفضيلات العامة
              </h3>
              
              <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                <div>
                  <label className={`block text-sm font-medium text-gray-700 mb-2 ${isRTL ? 'font-arabic' : ''}`}>
                    اللغة
                  </label>
                  <select
                    value={settings.preferences.language}
                    onChange={(e) => updateSetting('preferences', 'language', e.target.value)}
                    className={`w-full px-4 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-black focus:border-transparent ${isRTL ? 'font-arabic' : ''}`}
                  >
                    <option value="ar">العربية</option>
                    <option value="en">English</option>
                  </select>
                </div>

                <div>
                  <label className={`block text-sm font-medium text-gray-700 mb-2 ${isRTL ? 'font-arabic' : ''}`}>
                    العملة
                  </label>
                  <select
                    value={settings.preferences.currency}
                    onChange={(e) => updateSetting('preferences', 'currency', e.target.value)}
                    className={`w-full px-4 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-black focus:border-transparent ${isRTL ? 'font-arabic' : ''}`}
                  >
                    <option value="SAR">ريال سعودي (SAR)</option>
                    <option value="USD">دولار أمريكي (USD)</option>
                    <option value="EUR">يورو (EUR)</option>
                  </select>
                </div>

                <div>
                  <label className={`block text-sm font-medium text-gray-700 mb-2 ${isRTL ? 'font-arabic' : ''}`}>
                    المنطقة الزمنية
                  </label>
                  <select
                    value={settings.preferences.timezone}
                    onChange={(e) => updateSetting('preferences', 'timezone', e.target.value)}
                    className={`w-full px-4 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-black focus:border-transparent ${isRTL ? 'font-arabic' : ''}`}
                  >
                    <option value="Asia/Riyadh">الرياض (GMT+3)</option>
                    <option value="Asia/Dubai">دبي (GMT+4)</option>
                    <option value="Europe/London">لندن (GMT+0)</option>
                  </select>
                </div>
              </div>
            </div>
          )}

          {activeTab === 'notifications' && (
            <div className="space-y-6">
              <h3 className={`text-lg font-semibold text-gray-900 ${isRTL ? 'font-arabic' : ''}`}>
                إعدادات الإشعارات
              </h3>
              
              <div className="space-y-4">
                <div className={`flex items-center justify-between ${isRTL ? 'flex-row-reverse' : ''}`}>
                  <div>
                    <h4 className={`font-medium text-gray-900 ${isRTL ? 'font-arabic' : ''}`}>
                      تحديثات الطلبات
                    </h4>
                    <p className={`text-sm text-gray-500 ${isRTL ? 'font-arabic' : ''}`}>
                      تلقي إشعارات حول حالة طلباتك
                    </p>
                  </div>
                  <label className="relative inline-flex items-center cursor-pointer">
                    <input
                      type="checkbox"
                      checked={settings.notifications.orderUpdates}
                      onChange={(e) => updateSetting('notifications', 'orderUpdates', e.target.checked)}
                      className="sr-only peer"
                    />
                    <div className="w-11 h-6 bg-gray-200 peer-focus:outline-none peer-focus:ring-4 peer-focus:ring-black/20 rounded-full peer peer-checked:after:translate-x-full peer-checked:after:border-white after:content-[''] after:absolute after:top-[2px] after:left-[2px] after:bg-white after:border-gray-300 after:border after:rounded-full after:h-5 after:w-5 after:transition-all peer-checked:bg-black"></div>
                  </label>
                </div>

                <div className={`flex items-center justify-between ${isRTL ? 'flex-row-reverse' : ''}`}>
                  <div>
                    <h4 className={`font-medium text-gray-900 ${isRTL ? 'font-arabic' : ''}`}>
                      العروض والخصومات
                    </h4>
                    <p className={`text-sm text-gray-500 ${isRTL ? 'font-arabic' : ''}`}>
                      تلقي إشعارات حول العروض الخاصة
                    </p>
                  </div>
                  <label className="relative inline-flex items-center cursor-pointer">
                    <input
                      type="checkbox"
                      checked={settings.notifications.promotions}
                      onChange={(e) => updateSetting('notifications', 'promotions', e.target.checked)}
                      className="sr-only peer"
                    />
                    <div className="w-11 h-6 bg-gray-200 peer-focus:outline-none peer-focus:ring-4 peer-focus:ring-black/20 rounded-full peer peer-checked:after:translate-x-full peer-checked:after:border-white after:content-[''] after:absolute after:top-[2px] after:left-[2px] after:bg-white after:border-gray-300 after:border after:rounded-full after:h-5 after:w-5 after:transition-all peer-checked:bg-black"></div>
                  </label>
                </div>

                <div className={`flex items-center justify-between ${isRTL ? 'flex-row-reverse' : ''}`}>
                  <div>
                    <h4 className={`font-medium text-gray-900 ${isRTL ? 'font-arabic' : ''}`}>
                      الإشعارات عبر الرسائل النصية
                    </h4>
                    <p className={`text-sm text-gray-500 ${isRTL ? 'font-arabic' : ''}`}>
                      تلقي رسائل نصية للتحديثات المهمة
                    </p>
                  </div>
                  <label className="relative inline-flex items-center cursor-pointer">
                    <input
                      type="checkbox"
                      checked={settings.notifications.smsNotifications}
                      onChange={(e) => updateSetting('notifications', 'smsNotifications', e.target.checked)}
                      className="sr-only peer"
                    />
                    <div className="w-11 h-6 bg-gray-200 peer-focus:outline-none peer-focus:ring-4 peer-focus:ring-black/20 rounded-full peer peer-checked:after:translate-x-full peer-checked:after:border-white after:content-[''] after:absolute after:top-[2px] after:left-[2px] after:bg-white after:border-gray-300 after:border after:rounded-full after:h-5 after:w-5 after:transition-all peer-checked:bg-black"></div>
                  </label>
                </div>
              </div>
            </div>
          )}

          {activeTab === 'security' && (
            <div className="space-y-6">
              <h3 className={`text-lg font-semibold text-gray-900 ${isRTL ? 'font-arabic' : ''}`}>
                تغيير كلمة المرور
              </h3>
              
              <div className="max-w-md space-y-4">
                <div>
                  <label className={`block text-sm font-medium text-gray-700 mb-2 ${isRTL ? 'font-arabic' : ''}`}>
                    كلمة المرور الحالية
                  </label>
                  <div className="relative">
                    <input
                      type={showCurrentPassword ? 'text' : 'password'}
                      value={settings.security.currentPassword}
                      onChange={(e) => updateSetting('security', 'currentPassword', e.target.value)}
                      className="w-full px-4 py-2 pr-10 border border-gray-300 rounded-lg focus:ring-2 focus:ring-black focus:border-transparent"
                    />
                    <button
                      type="button"
                      onClick={() => setShowCurrentPassword(!showCurrentPassword)}
                      className="absolute inset-y-0 right-0 pr-3 flex items-center"
                    >
                      {showCurrentPassword ? <EyeOff className="h-5 w-5 text-gray-400" /> : <Eye className="h-5 w-5 text-gray-400" />}
                    </button>
                  </div>
                </div>

                <div>
                  <label className={`block text-sm font-medium text-gray-700 mb-2 ${isRTL ? 'font-arabic' : ''}`}>
                    كلمة المرور الجديدة
                  </label>
                  <div className="relative">
                    <input
                      type={showNewPassword ? 'text' : 'password'}
                      value={settings.security.newPassword}
                      onChange={(e) => updateSetting('security', 'newPassword', e.target.value)}
                      className="w-full px-4 py-2 pr-10 border border-gray-300 rounded-lg focus:ring-2 focus:ring-black focus:border-transparent"
                    />
                    <button
                      type="button"
                      onClick={() => setShowNewPassword(!showNewPassword)}
                      className="absolute inset-y-0 right-0 pr-3 flex items-center"
                    >
                      {showNewPassword ? <EyeOff className="h-5 w-5 text-gray-400" /> : <Eye className="h-5 w-5 text-gray-400" />}
                    </button>
                  </div>
                </div>

                <div>
                  <label className={`block text-sm font-medium text-gray-700 mb-2 ${isRTL ? 'font-arabic' : ''}`}>
                    تأكيد كلمة المرور الجديدة
                  </label>
                  <div className="relative">
                    <input
                      type={showConfirmPassword ? 'text' : 'password'}
                      value={settings.security.confirmPassword}
                      onChange={(e) => updateSetting('security', 'confirmPassword', e.target.value)}
                      className="w-full px-4 py-2 pr-10 border border-gray-300 rounded-lg focus:ring-2 focus:ring-black focus:border-transparent"
                    />
                    <button
                      type="button"
                      onClick={() => setShowConfirmPassword(!showConfirmPassword)}
                      className="absolute inset-y-0 right-0 pr-3 flex items-center"
                    >
                      {showConfirmPassword ? <EyeOff className="h-5 w-5 text-gray-400" /> : <Eye className="h-5 w-5 text-gray-400" />}
                    </button>
                  </div>
                </div>

                <button
                  onClick={handlePasswordChange}
                  className={`w-full px-4 py-2 bg-black text-white rounded-lg hover:bg-gray-800 transition-colors ${isRTL ? 'font-arabic' : ''}`}
                >
                  تغيير كلمة المرور
                </button>
              </div>
            </div>
          )}
        </div>

        {/* Save button */}
        <div className="px-6 py-4 bg-gray-50 border-t border-gray-200 rounded-b-lg">
          <button
            onClick={handleSave}
            className={`px-6 py-2 bg-black text-white rounded-lg hover:bg-gray-800 transition-colors ${isRTL ? 'font-arabic' : ''}`}
          >
            حفظ الإعدادات
          </button>
        </div>
      </div>
    </div>
  );
};

export default CustomerSettings;
